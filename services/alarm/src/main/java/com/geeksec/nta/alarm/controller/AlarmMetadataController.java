package com.geeksec.nta.alarm.controller;

import java.util.List;
import java.util.Map;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.geeksec.common.enums.AlarmHandlingStatus;
import com.geeksec.common.enums.AlarmEntityRole;
import com.geeksec.common.enums.AlarmTarget;
import com.geeksec.common.enums.CyberKillChain;
import com.geeksec.nta.alarm.service.AlarmMetadataService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 告警元数据控制器
 * 提供告警模块专用的元数据查询接口
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/alarm/metadata")
@RequiredArgsConstructor
public class AlarmMetadataController {

    private final AlarmMetadataService alarmMetadataService;

    // ==================== 告警目标相关接口 ====================

    @GetMapping("/targets")
    public ResponseEntity<List<AlarmTarget>> getAllAlarmTargets() {
        log.info("获取所有告警目标");
        List<AlarmTarget> result = alarmMetadataService.getAllAlarmTargets();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/targets/{code}")
    public ResponseEntity<AlarmTarget> getAlarmTargetByCode(@PathVariable int code) {
        log.info("根据代码获取告警目标: {}", code);
        AlarmTarget result = alarmMetadataService.getAlarmTargetByCode(code);
        return ResponseEntity.ok(result);
    }

    // ==================== 告警实体角色相关接口 ====================

    @GetMapping("/entity-roles")
    public ResponseEntity<List<AlarmEntityRole>> getAllAlarmEntityRoles() {
        log.info("获取所有告警实体角色");
        List<AlarmEntityRole> result = alarmMetadataService.getAllAlarmEntityRoles();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/entity-roles/{code}")
    public ResponseEntity<AlarmEntityRole> getAlarmEntityRoleByCode(@PathVariable int code) {
        log.info("根据代码获取告警实体角色: {}", code);
        AlarmEntityRole result = alarmMetadataService.getAlarmEntityRoleByCode(code);
        return ResponseEntity.ok(result);
    }

    // ==================== 告警状态相关接口 ====================

    @GetMapping("/states")
    public ResponseEntity<List<AlarmHandlingStatus>> getAllAlarmStates() {
        log.info("获取所有告警状态");
        List<AlarmHandlingStatus> result = alarmMetadataService.getAllAlarmStates();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/states/{code}")
    public ResponseEntity<AlarmHandlingStatus> getAlarmStateByCode(@PathVariable int code) {
        log.info("根据代码获取告警状态: {}", code);
        AlarmHandlingStatus result = alarmMetadataService.getAlarmStateByCode(code);
        return ResponseEntity.ok(result);
    }

    // ==================== 告警类型相关接口 ====================

    @GetMapping("/types")
    public ResponseEntity<Map<String, String>> getAllAlarmTypes() {
        log.info("获取所有告警类型");
        Map<String, String> result = alarmMetadataService.getAllAlarmTypes();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/types/{code}")
    public ResponseEntity<String> getAlarmTypeByCode(@PathVariable String code) {
        log.info("根据代码获取告警类型: {}", code);
        String result = alarmMetadataService.getAlarmTypeByCode(code);
        return ResponseEntity.ok(result);
    }

    // ==================== 告警目标类型相关接口 ====================

    @GetMapping("/target-types")
    public ResponseEntity<Map<String, String>> getAllAlarmTargetTypes() {
        log.info("获取所有告警目标类型");
        Map<String, String> result = alarmMetadataService.getAllAlarmTargetTypes();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/target-types/{code}")
    public ResponseEntity<String> getAlarmTargetTypeByCode(@PathVariable String code) {
        log.info("根据代码获取告警目标类型: {}", code);
        String result = alarmMetadataService.getAlarmTargetTypeByCode(code);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/target-belongs")
    public ResponseEntity<Map<String, String>> getAllAlarmTargetBelongs() {
        log.info("获取所有告警目标归属");
        Map<String, String> result = alarmMetadataService.getAllAlarmTargetBelongs();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/target-belongs/{code}")
    public ResponseEntity<String> getAlarmTargetBelongByCode(@PathVariable String code) {
        log.info("根据代码获取告警目标归属: {}", code);
        String result = alarmMetadataService.getAlarmTargetBelongByCode(code);
        return ResponseEntity.ok(result);
    }

    // ==================== 攻击链相关接口 ====================

    @GetMapping("/attack-chain")
    public ResponseEntity<List<Map<String, Object>>> getAttackChainModel() {
        log.info("获取攻击链模型");
        List<Map<String, Object>> result = alarmMetadataService.getAttackChainModel();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/cyber-kill-chain/stages")
    public ResponseEntity<List<CyberKillChain>> getAllCyberKillChainStages() {
        log.info("获取所有 Cyber Kill Chain 阶段");
        List<CyberKillChain> result = alarmMetadataService.getAllCyberKillChainStages();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/cyber-kill-chain/stages/{stage}/details")
    public ResponseEntity<Map<String, Object>> getCyberKillChainDetails(@PathVariable CyberKillChain stage) {
        log.info("获取 Cyber Kill Chain 阶段详情: {}", stage);
        Map<String, Object> result = alarmMetadataService.getCyberKillChainDetails(stage);
        return ResponseEntity.ok(result);
    }

    // ==================== 缓存管理接口 ====================

    @PostMapping("/cache/clear")
    public ResponseEntity<Void> clearAlarmCache() {
        log.info("清除告警元数据缓存");
        alarmMetadataService.clearAlarmCache();
        return ResponseEntity.ok().build();
    }

    @PostMapping("/cache/init")
    public ResponseEntity<Void> initAlarmCache() {
        log.info("初始化告警元数据缓存");
        alarmMetadataService.initAlarmCache();
        return ResponseEntity.ok().build();
    }
}
