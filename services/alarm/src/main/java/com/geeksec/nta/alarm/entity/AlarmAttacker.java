package com.geeksec.nta.alarm.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

import java.util.Date;

@Table(value = "alarm_attacker", schema = "nta" , comment = "告警-攻击者")
@Data
public class AlarmAttacker {

    @Id(keyType = KeyType.Generator)
    @Column(value = "id" , comment = "id")
    private String id;

    @Column(value = "alarm_id" , comment = "告警主表id")
    private String alarmId;

    @Column(value = "ip" , comment = "攻击者ip")
    private String ip;

    @Column(value = "create_time" , comment = "创建时间")
    private Date createTime;

}