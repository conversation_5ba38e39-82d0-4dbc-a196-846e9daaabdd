package com.geeksec.nta.alarm.service;

import com.geeksec.common.enums.AlarmTarget;
import com.geeksec.common.enums.AlarmEntityRole;
import com.geeksec.common.enums.AlarmHandlingStatus;
import com.geeksec.common.enums.CyberKillChain;

import java.util.List;
import java.util.Map;

/**
 * 告警元数据服务接口
 * 提供告警模块专用的元数据查询功能
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmMetadataService {

    // ==================== 告警目标相关 ====================

    /**
     * 获取所有告警目标
     *
     * @return 告警目标列表
     */
    List<AlarmTarget> getAllAlarmTargets();

    /**
     * 根据代码获取告警目标
     *
     * @param code 目标代码
     * @return 告警目标
     */
    AlarmTarget getAlarmTargetByCode(int code);

    // ==================== 告警实体角色相关 ====================

    /**
     * 获取所有告警实体角色
     *
     * @return 告警实体角色列表
     */
    List<AlarmEntityRole> getAllAlarmEntityRoles();

    /**
     * 根据代码获取告警实体角色
     *
     * @param code 角色代码
     * @return 告警实体角色
     */
    AlarmEntityRole getAlarmEntityRoleByCode(int code);

    // ==================== 告警状态相关 ====================

    /**
     * 获取所有告警状态
     *
     * @return 告警状态列表
     */
    List<AlarmHandlingStatus> getAllAlarmStates();

    /**
     * 根据代码获取告警状态
     *
     * @param code 状态代码
     * @return 告警状态
     */
    AlarmHandlingStatus getAlarmStateByCode(int code);

    // ==================== 告警类型相关 ====================

    /**
     * 获取所有告警类型
     *
     * @return 告警类型映射 (代码 -> 名称)
     */
    Map<String, String> getAllAlarmTypes();

    /**
     * 根据代码获取告警类型名称
     *
     * @param code 告警类型代码
     * @return 告警类型名称
     */
    String getAlarmTypeByCode(String code);

    // ==================== 告警目标类型相关 ====================

    /**
     * 获取所有告警目标类型
     *
     * @return 告警目标类型映射 (代码 -> 名称)
     */
    Map<String, String> getAllAlarmTargetTypes();

    /**
     * 根据代码获取告警目标类型名称
     *
     * @param code 告警目标类型代码
     * @return 告警目标类型名称
     */
    String getAlarmTargetTypeByCode(String code);

    /**
     * 获取所有告警目标归属
     *
     * @return 告警目标归属映射 (代码 -> 名称)
     */
    Map<String, String> getAllAlarmTargetBelongs();

    /**
     * 根据代码获取告警目标归属名称
     *
     * @param code 告警目标归属代码
     * @return 告警目标归属名称
     */
    String getAlarmTargetBelongByCode(String code);

    // ==================== 攻击链相关 ====================

    /**
     * 获取攻击链模型数据
     * 兼容 NTA 2.0 的 /dict/alarm 接口
     *
     * @return 攻击链模型列表
     */
    List<Map<String, Object>> getAttackChainModel();

    /**
     * 获取所有 Cyber Kill Chain 阶段
     *
     * @return Cyber Kill Chain 阶段列表
     */
    List<CyberKillChain> getAllCyberKillChainStages();

    /**
     * 根据阶段获取 Cyber Kill Chain 详情
     *
     * @param stage Cyber Kill Chain 阶段
     * @return 阶段详情映射
     */
    Map<String, Object> getCyberKillChainDetails(CyberKillChain stage);

    // ==================== 缓存管理相关 ====================

    /**
     * 清除告警元数据缓存
     */
    void clearAlarmCache();

    /**
     * 初始化告警元数据缓存
     */
    void initAlarmCache();
}
