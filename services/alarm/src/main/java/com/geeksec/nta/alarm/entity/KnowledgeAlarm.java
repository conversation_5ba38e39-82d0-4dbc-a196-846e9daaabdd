package com.geeksec.nta.alarm.entity;


import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Table("tb_knowledge_alarm")
public class KnowledgeAlarm implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * //告警id
     */
    @Column("knowledge_alarm_id")
    private Integer knowledgeAlarmId;

    /**
     * //告警名称
     */
    @Column("alarm_name")
    private String alarmName;

    /**
     * 攻击类型 1 非法接入          
     */
    @Column("attack_type")
    private Integer attackType;

    /**
     * 可能关联的告警名称 , 逗号分割
     */
    @Column("relation_tag_id")
    private String relationTagId;

    /**
     * 的告警名称 , 逗号分割
     */
    @Column("exclude_tag_id")
    private String excludeTagId;

    /**
     * 黑名单权重
     */
    @Column("black_list")
    private Integer blackList;

    /**
     * //备注 , 说明
     */
    @Column("remark")
    private String remark;

    /**
     * //自增id  ---告警表---
     */
    @Id(value = "id", keyType = KeyType.Auto)
    private Long id;


}
