package com.geeksec.nta.alarm.service;

import com.geeksec.nta.alarm.dto.condition.AlarmRoleJudgeCondition;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description：告警研判相关服务
 */
public interface AlarmJudgeService {

    /**
     * 生成告警研判绘图
     * @param alarmMap
     * @return
     */
    Map<String, Object> createAlarmJudgeGraph(Map<String, Object> alarmMap);

    /**
     * 通过角色生成告警研判绘图
     *
     * @param condition@return
     */
    Map<String, Object> createAlarmJudgeGraphByRole(AlarmRoleJudgeCondition condition);
}
