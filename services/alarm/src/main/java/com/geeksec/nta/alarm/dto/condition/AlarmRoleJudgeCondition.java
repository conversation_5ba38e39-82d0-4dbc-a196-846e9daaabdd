package com.geeksec.nta.alarm.dto.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description：根据角色告警研判图生成条件
 */
@Data
public class AlarmRoleJudgeCondition {

    /**
     * 角色
     */
    private List<String> role;

    /**
     * IP地址
     */
    @JsonProperty("ip_addr")
    private String ipAddr;

    /**
     * 查询条数
     */
    @JsonProperty("query_num")
    private Integer queryNum;

    /**
     * 告警时间范围
     */
    @JsonProperty("time_range")
    private TimeRange timeRange;

    @Data
    public static class TimeRange{
        /** 开始时间 */
        private Long left = -1L;
        /** 结束时间 */
        private Long right = -1L;
    }
}
