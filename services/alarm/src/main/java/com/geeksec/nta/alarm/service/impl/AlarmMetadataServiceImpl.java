package com.geeksec.nta.alarm.service.impl;

import com.geeksec.common.enums.AlarmTarget;
import com.geeksec.common.enums.AlarmEntityRole;
import com.geeksec.common.enums.AlarmHandlingStatus;
import com.geeksec.common.enums.CyberKillChain;
import com.geeksec.nta.alarm.service.AlarmMetadataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 告警元数据服务实现类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
public class AlarmMetadataServiceImpl implements AlarmMetadataService {

    // ==================== 静态数据定义 ====================

    /**
     * 告警类型映射
     */
    private static final Map<String, String> ALARM_TYPE_MAP = Map.of(
        "0", "IP",
        "1", "端口",
        "2", "域名",
        "3", "证书",
        "4", "应用",
        "5", "会话",
        "6", "指纹",
        "7", "其他"
    );

    /**
     * 告警目标类型映射
     */
    private static final Map<String, String> ALARM_TARGET_TYPE_MAP = Map.of(
        "0", "IP地址",
        "1", "域名",
        "2", "证书",
        "3", "应用协议",
        "4", "网络会话",
        "5", "系统指纹",
        "6", "其他目标"
    );

    /**
     * 告警目标归属映射
     */
    private static final Map<String, String> ALARM_TARGET_BELONG_MAP = Map.of(
        "0", "内网",
        "1", "外网",
        "2", "DMZ区",
        "3", "未知"
    );

    @Override
    @Cacheable(value = "alarmMetadata", key = "'targets'")
    public List<AlarmTarget> getAllAlarmTargets() {
        log.debug("获取所有告警目标");
        return Arrays.asList(AlarmTarget.values());
    }

    @Override
    @Cacheable(value = "alarmMetadata", key = "'entityRoles'")
    public List<AlarmEntityRole> getAllAlarmEntityRoles() {
        log.debug("获取所有告警实体角色");
        return Arrays.asList(AlarmEntityRole.values());
    }

    @Override
    @Cacheable(value = "alarmMetadata", key = "'states'")
    public List<AlarmHandlingStatus> getAllAlarmStates() {
        log.debug("获取所有告警状态");
        return Arrays.asList(AlarmHandlingStatus.values());
    }

    @Override
    public AlarmTarget getAlarmTargetByCode(int code) {
        log.debug("根据代码获取告警目标: {}", code);
        return AlarmTarget.fromCode(code);
    }

    @Override
    public AlarmEntityRole getAlarmEntityRoleByCode(int code) {
        log.debug("根据代码获取告警实体角色: {}", code);
        return AlarmEntityRole.fromCode(code);
    }

    @Override
    public AlarmHandlingStatus getAlarmStateByCode(int code) {
        log.debug("根据代码获取告警状态: {}", code);
        return AlarmHandlingStatus.fromCode(code);
    }

    // ==================== 告警类型相关实现 ====================

    @Override
    @Cacheable(value = "alarmMetadata", key = "'alarmTypes'")
    public Map<String, String> getAllAlarmTypes() {
        log.debug("获取所有告警类型");
        return new LinkedHashMap<>(ALARM_TYPE_MAP);
    }

    @Override
    public String getAlarmTypeByCode(String code) {
        log.debug("根据代码获取告警类型: {}", code);
        String type = ALARM_TYPE_MAP.get(code);
        if (type == null) {
            throw new IllegalArgumentException("未知的告警类型代码: " + code);
        }
        return type;
    }

    // ==================== 告警目标类型相关实现 ====================

    @Override
    @Cacheable(value = "alarmMetadata", key = "'alarmTargetTypes'")
    public Map<String, String> getAllAlarmTargetTypes() {
        log.debug("获取所有告警目标类型");
        return new LinkedHashMap<>(ALARM_TARGET_TYPE_MAP);
    }

    @Override
    public String getAlarmTargetTypeByCode(String code) {
        log.debug("根据代码获取告警目标类型: {}", code);
        String type = ALARM_TARGET_TYPE_MAP.get(code);
        if (type == null) {
            throw new IllegalArgumentException("未知的告警目标类型代码: " + code);
        }
        return type;
    }

    @Override
    @Cacheable(value = "alarmMetadata", key = "'alarmTargetBelongs'")
    public Map<String, String> getAllAlarmTargetBelongs() {
        log.debug("获取所有告警目标归属");
        return new LinkedHashMap<>(ALARM_TARGET_BELONG_MAP);
    }

    @Override
    public String getAlarmTargetBelongByCode(String code) {
        log.debug("根据代码获取告警目标归属: {}", code);
        String belong = ALARM_TARGET_BELONG_MAP.get(code);
        if (belong == null) {
            throw new IllegalArgumentException("未知的告警目标归属代码: " + code);
        }
        return belong;
    }

    // ==================== 攻击链相关实现 ====================

    @Override
    @Cacheable(value = "alarmMetadata", key = "'attackChainModel'")
    public List<Map<String, Object>> getAttackChainModel() {
        log.debug("获取攻击链模型");
        return Arrays.stream(CyberKillChain.values())
                .map(stage -> {
                    Map<String, Object> item = new HashMap<>();
                    item.put("value", stage.ordinal());
                    item.put("label", stage.getChineseName());
                    item.put("description", stage.getDescription());
                    item.put("englishName", stage.getEnglishName());
                    return item;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "alarmMetadata", key = "'cyberKillChainStages'")
    public List<CyberKillChain> getAllCyberKillChainStages() {
        log.debug("获取所有 Cyber Kill Chain 阶段");
        return Arrays.asList(CyberKillChain.values());
    }

    @Override
    public Map<String, Object> getCyberKillChainDetails(CyberKillChain stage) {
        log.debug("获取 Cyber Kill Chain 阶段详情: {}", stage);
        Map<String, Object> details = new HashMap<>();
        details.put("stage", stage.name());
        details.put("chineseName", stage.getChineseName());
        details.put("englishName", stage.getEnglishName());
        details.put("description", stage.getDescription());
        details.put("order", stage.ordinal());
        return details;
    }

    // ==================== 缓存管理实现 ====================

    @Override
    @CacheEvict(value = "alarmMetadata", allEntries = true)
    public void clearAlarmCache() {
        log.info("清除告警元数据缓存");
    }

    @Override
    public void initAlarmCache() {
        log.info("初始化告警元数据缓存");
        // 预热缓存
        getAllAlarmTargets();
        getAllAlarmEntityRoles();
        getAllAlarmStates();
        getAllAlarmTypes();
        getAllAlarmTargetTypes();
        getAllAlarmTargetBelongs();
        getAttackChainModel();
        getAllCyberKillChainStages();
    }
}
