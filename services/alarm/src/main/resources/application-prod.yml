# Alarm Service 生产环境配置
# 继承通用生产环境配置

spring:
  profiles:
    active: prod
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8090}
  servlet:
    context-path: /api/alarm

# 告警服务特定配置
alarm:
  # 生产环境告警规则配置
  rules:
    # 告警阈值配置
    thresholds:
      cpu-usage: 70.0  # CPU使用率告警阈值
      memory-usage: 75.0  # 内存使用率告警阈值
      disk-usage: 80.0  # 磁盘使用率告警阈值
      network-latency: 500  # 网络延迟告警阈值(ms)
    
    # 告警频率限制
    rate-limit:
      max-alarms-per-minute: 20  # 生产环境严格限制
      duplicate-alarm-interval: 1800  # 重复告警间隔(秒)
  
  # 生产环境通知配置
  notification:
    # 邮件通知
    email:
      enabled: true  # 生产环境启用邮件通知
      smtp-host: ${SMTP_HOST:prod-smtp.nta.local}
      smtp-port: ${SMTP_PORT:587}
      username: ${SMTP_USERNAME:nta-prod}
      password: ${SMTP_PASSWORD}
      ssl-enabled: true
    
    # 短信通知
    sms:
      enabled: true  # 生产环境启用短信通知
      provider: ${SMS_PROVIDER:aliyun}
      access-key: ${SMS_ACCESS_KEY}
      secret-key: ${SMS_SECRET_KEY}
    
    # Webhook通知
    webhook:
      enabled: true  # 生产环境启用Webhook
      url: ${WEBHOOK_URL:https://prod-webhook.nta.local/webhook}
      timeout: 5000
      retry-count: 3
  
  # 生产环境数据保留策略
  retention:
    alarm-history-days: 90  # 告警历史保留90天
    metrics-history-days: 30  # 指标历史保留30天
  
  # 生产环境安全配置
  security:
    enable-alarm-audit: true
    enable-access-control: true
    allowed-sources: ${ALARM_ALLOWED_SOURCES:}

# 日志配置
logging:
  level:
    '[com.geeksec.nta.alarm]': INFO

send-url:
  #告警报告导出
  alarm_report_export: http://192.168.101.194:37777/probe_pdf_result
