<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.nta.alarm.mapper.DorisConnectMapper">

    <!--
        重要提示: 这里的表名 `connect_log` 是根据业务逻辑推断的。
        请根据您的实际数据库表名进行修改。
        假设的表结构至少包含: SessionId, sIp, dIp, AppName, Labels 等字段。
    -->

    <select id="findConnectLogsBySessionIds" resultType="java.util.Map">
        SELECT * FROM connect_log
        WHERE SessionId IN
        <foreach item="id" collection="sessionIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <select id="findConnectLogBySessionId" resultType="java.util.Map">
        SELECT * FROM connect_log
        WHERE SessionId = #{sessionId}
        LIMIT 1
    </select>

</mapper>