<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.nta.alarm.mapper.KnowledgeAlarmMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geeksec.nta.alarm.entity.KnowledgeAlarm">
        <id column="id" property="id"/>
        <result column="knowledge_alarm_id" property="knowledgeAlarmId"/>
        <result column="alarm_name" property="alarmName"/>
        <result column="attack_type" property="attackType"/>
        <result column="relation_tag_id" property="relationTagId"/>
        <result column="exclude_tag_id" property="excludeTagId"/>
        <result column="black_list" property="blackList"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 定义 resultMap 元素，将查询结果映射到 KnowledgeAlarm 类中 -->
    <resultMap id="KnowledgeAlarmResultMap" type="com.geeksec.nta.alarm.entity.ModelAttackInfo">
        <id property="knowledgeAlarmId" column="knowledge_alarm_id"/>
        <result property="alarmName" column="alarm_name"/>
        <result property="attackType" column="attack_type"/>
        <result property="attackTypeName" column="attack_type_name"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        knowledge_alarm_id, alarm_name, attack_type, relation_tag_id, exclude_tag_id, black_list, remark, id
    </sql>
    <insert id="insertAttackChain">
        insert into tb_alarm_white (attacker, victim, label)
        values (#{attackerIp}, #{victimIp}, #{labelStr})
    </insert>

    <select id="getKnowledgeAlarmList" resultType="com.geeksec.nta.alarm.vo.KnowledgeAlarmVo">
        SELECT
            knowledge_alarm_id id,
            alarm_name
        FROM
            tb_knowledge_alarm
        UNION ALL
        (
            SELECT
                rule_id id,
                rule_name alarm_name
            FROM
                tb_rule
            WHERE
                STATUS = 1
              AND task_id IN
                  ( SELECT task_id FROM tb_task_analysis WHERE task_id NOT IN ( 0, 1 )
                    UNION ALL
                    SELECT task_id FROM tb_task_analysis WHERE task_id IN ( 0, 1 )
                  )
        )
    </select>

    <select id="getKnowledgeType" resultType="com.geeksec.nta.alarm.vo.KnowledgeTypeVo">
        select A.knowledge_alarm_id knowledgeAlarmId, A.alarm_name, A.attack_type, B.attack_type_name
        from tb_knowledge_alarm A
                 left join tb_attack_type B on A.attack_type = B.attack_type_id
        union all
        select C.rule_id knowledge_alarm_id, C.rule_name alarm_name, 0, '未知'
        from tb_rule C
    </select>

    <select id="countAttackChain" resultType="java.lang.Long">
        select count(1)
        from tb_alarm_white
        where attacker = #{attackerIp}
          and victim = #{victimIp}
          and label = #{labelStr}
    </select>
    <select id="getAttackChainMap" resultMap="KnowledgeAlarmResultMap">
        select t1.knowledge_alarm_id, t1.alarm_name, t1.attack_type, t2.attack_type_name
        from tb_knowledge_alarm t1
                 left join tb_attack_type t2 on t1.attack_type = t2.attack_type_id
        where t1.attack_type not in (0, 7)
          and t1.knowledge_alarm_id >= 100000
    </select>

</mapper>
