package com.geeksec.knowledgebase.service;

import com.geeksec.knowledgebase.KnowledgeBaseApplicationTests;
import com.geeksec.knowledgebase.domain.entity.GeoIpInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@SpringBootTest(classes = KnowledgeBaseApplicationTests.class)
@ActiveProfiles("test")
class GeoIpServiceTest {

    @Autowired
    private GeoIpService geoIpService;

    private static final String TEST_PUBLIC_IP = "*******"; // Google DNS
    private static final String TEST_PRIVATE_IP = "***********";

    @Test
    void getGeoIpInfo() {
        Optional<GeoIpInfo> result = geoIpService.getGeoIpInfo(TEST_PUBLIC_IP);
        assertTrue(result.isPresent(), "Should find geo info for public IP");
        
        GeoIpInfo info = result.get();
        log.info("GeoIP Info for {}: {}", TEST_PUBLIC_IP, info);
        
        assertNotNull(info.getCountryCode(), "Country code should not be null");
        assertNotNull(info.getCountryName(), "Country name should not be null");
    }

    @Test
    void getGeoIpInfo_PrivateIp() {
        Optional<GeoIpInfo> result = geoIpService.getGeoIpInfo(TEST_PRIVATE_IP);
        assertTrue(result.isEmpty(), "Should not find geo info for private IP");
    }

    @Test
    void batchGetGeoIpInfo() {
        Map<String, GeoIpInfo> result = geoIpService.batchGetGeoIpInfo(
                List.of(TEST_PUBLIC_IP, TEST_PRIVATE_IP, "*******"));
        
        assertFalse(result.isEmpty(), "Should find geo info for public IPs");
        assertTrue(result.containsKey(TEST_PUBLIC_IP), "Should contain public IP");
        assertFalse(result.containsKey(TEST_PRIVATE_IP), "Should not contain private IP");
        
        result.forEach((ip, info) -> {
            log.info("Batch GeoIP Info for {}: {}", ip, info);
            assertNotNull(info.getCountryCode(), "Country code should not be null for " + ip);
        });
    }

    @Test
    void getCountryCode() {
        String countryCode = geoIpService.getCountryCode(TEST_PUBLIC_IP);
        assertNotNull(countryCode, "Country code should not be null");
        assertNotEquals("--", countryCode, "Country code should not be default value");
        log.info("Country code for {}: {}", TEST_PUBLIC_IP, countryCode);
    }

    @Test
    void getAsnInfo() {
        String asnInfo = geoIpService.getAsnInfo(TEST_PUBLIC_IP);
        assertNotNull(asnInfo, "ASN info should not be null");
        assertNotEquals("--", asnInfo, "ASN info should not be default value");
        assertTrue(asnInfo.startsWith("AS"), "ASN info should start with 'AS'");
        log.info("ASN Info for {}: {}", TEST_PUBLIC_IP, asnInfo);
    }

    @Test
    void getLocation() {
        String location = geoIpService.getLocation(TEST_PUBLIC_IP);
        assertNotNull(location, "Location should not be null");
        assertNotEquals("--", location, "Location should not be default value");
        log.info("Location for {}: {}", TEST_PUBLIC_IP, location);
    }

    @Test
    void getCoordinates() {
        String coordinates = geoIpService.getCoordinates(TEST_PUBLIC_IP);
        assertNotNull(coordinates, "Coordinates should not be null");
        assertNotEquals("--", coordinates, "Coordinates should not be default value");
        assertTrue(coordinates.contains(","), "Coordinates should contain a comma");
        log.info("Coordinates for {}: {}", TEST_PUBLIC_IP, coordinates);
    }

    @Test
    void getCountryDistribution() {
        List<Map<String, Object>> distribution = geoIpService.getCountryDistribution();
        assertNotNull(distribution, "Distribution should not be null");
        assertFalse(distribution.isEmpty(), "Distribution should not be empty");
        
        log.info("Country distribution (top 10):");
        distribution.stream().limit(10).forEach(entry -> 
                log.info("{}: {}", entry.get("country"), entry.get("count")));
    }

    @Test
    void getProvinceDistribution() {
        List<Map<String, Object>> distribution = geoIpService.getProvinceDistribution();
        assertNotNull(distribution, "Province distribution should not be null");
        
        log.info("Province distribution (top 10):");
        distribution.stream().limit(10).forEach(entry -> 
                log.info("{} - {}: {}", 
                        entry.get("country"), 
                        entry.get("province"), 
                        entry.get("count")));
    }

    @Test
    void getCityDistribution() {
        List<Map<String, Object>> distribution = geoIpService.getCityDistribution();
        assertNotNull(distribution, "City distribution should not be null");
        
        log.info("City distribution (top 10):");
        distribution.stream().limit(10).forEach(entry -> 
                log.info("{} - {} - {}: {}", 
                        entry.get("country"), 
                        entry.get("province"),
                        entry.get("city"),
                        entry.get("count")));
    }

    @Test
    void getAsnDistribution() {
        List<Map<String, Object>> distribution = geoIpService.getAsnDistribution();
        assertNotNull(distribution, "ASN distribution should not be null");
        
        log.info("ASN distribution (top 10):");
        distribution.stream().limit(10).forEach(entry -> 
                log.info("{} - {}: {}", 
                        entry.get("asn"), 
                        entry.get("organization"), 
                        entry.get("count")));
    }
}
