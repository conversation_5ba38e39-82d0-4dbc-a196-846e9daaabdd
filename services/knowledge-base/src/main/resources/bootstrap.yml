spring:
  application:
    name: knowledge-base-service
  profiles:
    # 激活Kubernetes配置
    include: kubernetes
  cloud:
    # 启用Kubernetes服务发现
    kubernetes:
      discovery:
        enabled: true
        # 服务名称
        service-name: ${spring.application.name}
        # 命名空间
        namespace: ${KUBERNETES_NAMESPACE:nta}
      config:
        enabled: true
        # 从ConfigMap加载配置
        sources:
          - name: ${spring.application.name}-config
            namespace: ${KUBERNETES_NAMESPACE:nta}
          - name: common-config
            namespace: ${KUBERNETES_NAMESPACE:nta}
    # 配置导入
    config:
      import:
        - "kubernetes:"
        - "classpath:application-kubernetes.yml"
