-- 创建GeoIP信息表
CREATE TABLE IF NOT EXISTS geo_ip_info
(
    id             BIGSERIAL PRIMARY KEY,
    ip_address     VARCHAR(45) NOT NULL UNIQUE,
    country_code   VARCHAR(10),
    country_name   VARCHAR(100),
    province_name  VARCHAR(100),
    city_name      VARCHAR(100),
    longitude      DOUBLE PRECISION,
    latitude       DOUBLE PRECISION,
    asn_number     VARCHAR(50),
    organization   VARCHAR(200),
    last_updated   BIGINT      NOT NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_geo_ip_country ON geo_ip_info (country_code);
CREATE INDEX IF NOT EXISTS idx_geo_ip_city ON geo_ip_info (city_name);
CREATE INDEX IF NOT EXISTS idx_geo_ip_asn ON geo_ip_info (asn_number);
CREATE INDEX IF NOT EXISTS idx_geo_ip_org ON geo_ip_info (organization);

-- 添加注释
COMMENT ON TABLE geo_ip_info IS 'IP地址地理位置信息表';
COMMENT ON COLUMN geo_ip_info.id IS '主键ID';
COMMENT ON COLUMN geo_ip_info.ip_address IS 'IP地址';
COMMENT ON COLUMN geo_ip_info.country_code IS '国家代码';
COMMENT ON COLUMN geo_ip_info.country_name IS '国家名称';
COMMENT ON COLUMN geo_ip_info.province_name IS '省份/州名称';
COMMENT ON COLUMN geo_ip_info.city_name IS '城市名称';
COMMENT ON COLUMN geo_ip_info.longitude IS '经度';
COMMENT ON COLUMN geo_ip_info.latitude IS '纬度';
COMMENT ON COLUMN geo_ip_info.asn_number IS 'ASN编号';
COMMENT ON COLUMN geo_ip_info.organization IS '组织名称';
COMMENT ON COLUMN geo_ip_info.last_updated IS '最后更新时间';

-- 创建函数：更新最后更新时间
CREATE OR REPLACE FUNCTION update_geo_ip_last_updated()
    RETURNS TRIGGER AS $$
BEGIN
    NEW.last_updated = EXTRACT(EPOCH FROM NOW()) * 1000;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器：在插入或更新时自动更新最后更新时间
DROP TRIGGER IF EXISTS trigger_update_geo_ip_last_updated ON geo_ip_info;
CREATE TRIGGER trigger_update_geo_ip_last_updated
    BEFORE INSERT OR UPDATE ON geo_ip_info
    FOR EACH ROW
EXECUTE FUNCTION update_geo_ip_last_updated();
