package com.geeksec.knowledgebase.domain.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * IP地址地理位置信息实体类
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Table("geo_ip_info")
public class GeoIpInfo {

    @Id(keyType = KeyType.Auto)
    private Long id;
    
    /**
     * IP地址
     */
    @Column("ip_address")
    private String ipAddress;

    /**
     * 国家代码
     */
    @Column("country_code")
    private String countryCode;

    /**
     * 国家名称
     */
    @Column("country_name")
    private String countryName;

    /**
     * 省份/州名称
     */
    @Column("province_name")
    private String provinceName;

    /**
     * 城市名称
     */
    @Column("city_name")
    private String cityName;

    /**
     * 经度
     */
    @Column("longitude")
    private Double longitude;

    /**
     * 纬度
     */
    @Column("latitude")
    private Double latitude;

    /**
     * ASN编号
     */
    @Column("asn_number")
    private String asnNumber;

    /**
     * 组织名称
     */
    @Column("organization")
    private String organization;

    /**
     * 最后更新时间
     */
    @Column("last_updated")
    private Long lastUpdated = System.currentTimeMillis();
}
