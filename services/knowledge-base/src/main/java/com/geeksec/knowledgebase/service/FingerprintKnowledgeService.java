package com.geeksec.knowledgebase.service;

import com.geeksec.knowledgebase.domain.entity.FingerprintKnowledge;
import com.geeksec.knowledgebase.repository.mapper.FingerprintKnowledgeMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 指纹知识服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FingerprintKnowledgeService {

    private final FingerprintKnowledgeMapper fingerprintKnowledgeMapper;

    /**
     * 搜索指纹知识
     */
    public List<FingerprintKnowledge> search(String keyword, String product, String vendor, String category) {
        if (keyword != null && !keyword.trim().isEmpty()) {
            return fingerprintKnowledgeMapper.searchByKeyword(keyword);
        }
        if (product != null && !product.trim().isEmpty()) {
            return fingerprintKnowledgeMapper.findByApplicationContainingIgnoreCase(product);
        }
        if (vendor != null && !vendor.trim().isEmpty()) {
            return fingerprintKnowledgeMapper.findByVendorContainingIgnoreCase(vendor);
        }
        return List.of();
    }

    /**
     * 根据产品查找指纹
     */
    public List<FingerprintKnowledge> findByProduct(String product) {
        return fingerprintKnowledgeMapper.findByApplicationContainingIgnoreCase(product);
    }

    /**
     * 获取统计信息
     */
    public Map<String, Object> getStatistics() {
        // 暂时返回空的统计信息
        return Map.of(
            "totalCount", 0,
            "vendorCount", 0,
            "applicationCount", 0
        );
    }

}
