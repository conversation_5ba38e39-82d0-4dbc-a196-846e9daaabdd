package com.geeksec.knowledgebase.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.knowledgebase.service.DomainService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/domain")
@RequiredArgsConstructor
@Tag(name = "Domain Controller", description = "域名知识库查询")
public class DomainController {

    private final DomainService domainService;

    @GetMapping("/is-dynamic-domain/{domain}")
    @Operation(summary = "检查是否为动态域名")
    public ApiResponse<Boolean> isDynamicDomain(@Parameter(description = "域名") @PathVariable String domain) {
        log.debug("检查动态域名: {}", domain);
        boolean isDynamic = domainService.isDynamicDomain(domain);
        return ApiResponse.success(isDynamic);
    }

    @GetMapping("/is-cdn-domain/{domain}")
    @Operation(summary = "检查是否为CDN域名")
    public ApiResponse<Boolean> isCdnDomain(@Parameter(description = "域名") @PathVariable String domain) {
        log.debug("检查CDN域名: {}", domain);
        boolean isCdn = domainService.isCdnDomain(domain);
        return ApiResponse.success(isCdn);
    }

    @GetMapping("/is-sinkhole-domain/{domain}")
    @Operation(summary = "检查是否为Sinkhole域名")
    public ApiResponse<Boolean> isSinkholeDomain(@Parameter(description = "域名") @PathVariable String domain) {
        log.debug("检查Sinkhole域名: {}", domain);
        boolean isSinkhole = domainService.isSinkholeDomain(domain);
        return ApiResponse.success(isSinkhole);
    }

    @GetMapping("/tld-info/{domain}")
    @Operation(summary = "获取顶级域名信息")
    public ApiResponse<Map<String, String>> getTldInfo(@Parameter(description = "域名") @PathVariable String domain) {
        log.debug("获取TLD信息: {}", domain);
        Map<String, String> tldInfo = domainService.getTldInfo(domain);
        return ApiResponse.success(tldInfo);
    }

    @GetMapping("/all-tlds")
    @Operation(summary = "获取所有顶级域名")
    public ApiResponse<List<String>> getAllTlds() {
        log.debug("获取所有TLD");
        List<String> tlds = domainService.getAllTlds();
        return ApiResponse.success(tlds);
    }
}
