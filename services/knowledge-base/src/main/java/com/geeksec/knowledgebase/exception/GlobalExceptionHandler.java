package com.geeksec.knowledgebase.exception;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.exception.BaseGlobalExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 知识库服务全局异常处理器
 * 继承自基础异常处理器，添加特定于知识库的异常处理
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler extends BaseGlobalExceptionHandler {

    /**
     * 处理知识库特定的业务异常
     * 这里可以添加知识库模块特有的异常处理逻辑
     * 通用的异常处理已经在父类中实现
     */
    @ExceptionHandler(KnowledgeBaseException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<String> handleKnowledgeBaseException(KnowledgeBaseException e) {
        log.warn("知识库业务异常: {}", e.getMessage(), e);
        return ApiResponse.error(e.getCode(), e.getMessage());
    }
}