package com.geeksec.knowledgebase.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 域名WHOIS管理器
 * 专门负责域名WHOIS信息的获取和缓存功能
 * 直接从WhoisXML API的CSV文件中读取域名WHOIS数据并提供高效查询
 *
 * <AUTHOR>
 */
@Slf4j
public class DomainWhoisManager {

    /**
     * 单例实例
     */
    private static volatile DomainWhoisManager instance = null;

    /**
     * 域名WHOIS信息映射
     * Key: 域名, Value: WHOIS信息对象
     */
    private final Map<String, DomainWhoisInfo> domainWhoisMap = new ConcurrentHashMap<>();

    /**
     * 默认WHOIS数据文件名
     */
    private static final String DEFAULT_WHOIS_FILE = "sample_whois_db_download.csv";

    /**
     * 知识库资源路径（支持外部挂载）
     */
    private static final String KNOWLEDGE_BASE_PATH = System.getProperty("knowledgebase.path",
        System.getenv("KNOWLEDGE_BASE_PATH") != null ? System.getenv("KNOWLEDGE_BASE_PATH") : "/opt/flink/data/knowledge-base");

    /**
     * WHOIS数据完整路径
     */
    private static final String WHOIS_DATA_PATH = KNOWLEDGE_BASE_PATH + "/domain-whois/" + DEFAULT_WHOIS_FILE;

    /**
     * 私有构造函数
     */
    private DomainWhoisManager() {
        initialize();
    }

    /**
     * 获取单例实例
     *
     * @return DomainWhoisManager实例
     */
    public static DomainWhoisManager getInstance() {
        if (instance == null) {
            synchronized (DomainWhoisManager.class) {
                if (instance == null) {
                    instance = new DomainWhoisManager();
                }
            }
        }
        return instance;
    }

    /**
     * 初始化WHOIS管理器
     */
    private void initialize() {
        try {
            // 初始化时不加载数据，等到需要时再加载
            log.info("域名WHOIS管理器初始化成功");
        } catch (Exception e) {
            log.error("初始化域名WHOIS管理器失败", e);
        }
    }

    /**
     * 从CSV文件加载域名WHOIS数据
     */
    private void loadDomainWhoisInfo() {
        if (!domainWhoisMap.isEmpty()) {
            return; // 已经加载过了
        }

        loadFromFile(WHOIS_DATA_PATH);
    }

    /**
     * 从文件路径加载WHOIS数据
     */
    private void loadFromFile(String filePath) {
        try {
            java.io.File file = new java.io.File(filePath);
            if (!file.exists() || !file.canRead()) {
                log.error("WHOIS数据文件不存在或不可读：{}", filePath);
                return;
            }

            try (BufferedReader reader = new BufferedReader(
                    new java.io.FileReader(file, java.nio.charset.StandardCharsets.UTF_8))) {

                int loadedCount = parseWhoisData(reader);
                log.info("成功加载域名WHOIS数据：{}，加载了 {} 条记录", filePath, loadedCount);
            }
        } catch (IOException e) {
            log.error("加载WHOIS数据失败：{}", filePath, e);
        }
    }

    /**
     * 解析WHOIS数据
     */
    private int parseWhoisData(BufferedReader reader) throws IOException {
        String line;
        int lineNumber = 0;
        int loadedCount = 0;
        String[] headers = null;

        while ((line = reader.readLine()) != null) {
            lineNumber++;

            if (lineNumber == 1) {
                // 解析标题行
                headers = parseCsvLine(line);
                continue;
            }

            if (StringUtils.isEmpty(line.trim())) {
                continue;
            }

            try {
                DomainWhoisInfo whoisInfo = parseWhoisRecord(line, headers);
                if (whoisInfo != null && StringUtils.isNotEmpty(whoisInfo.getDomainName())) {
                    domainWhoisMap.put(whoisInfo.getDomainName().toLowerCase(), whoisInfo);
                    loadedCount++;
                }
            } catch (Exception e) {
                log.debug("解析第 {} 行失败: {}", lineNumber, e.getMessage());
            }
        }

        return loadedCount;
    }

    /**
     * 解析CSV行数据
     *
     * @param line CSV行
     * @return 字段数组
     */
    private String[] parseCsvLine(String line) {
        List<String> fields = new ArrayList<>();
        StringBuilder currentField = new StringBuilder();
        boolean inQuotes = false;

        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);

            if (c == '"') {
                inQuotes = !inQuotes;
            } else if (c == ',' && !inQuotes) {
                fields.add(currentField.toString().trim());
                currentField.setLength(0);
            } else {
                currentField.append(c);
            }
        }

        // 添加最后一个字段
        fields.add(currentField.toString().trim());

        return fields.toArray(new String[0]);
    }

    /**
     * 解析WHOIS记录
     *
     * @param line CSV数据行
     * @param headers 标题行
     * @return DomainWhoisInfo对象
     */
    private DomainWhoisInfo parseWhoisRecord(String line, String[] headers) {
        String[] fields = parseCsvLine(line);

        if (fields.length < headers.length) {
            return null;
        }

        // 核心字段
        String domainName = null;
        String registrarName = null;
        String registrantOrganization = null;
        String registrantCountry = null;
        String createdDate = null;
        String updatedDate = null;
        String expiresDate = null;
        String status = null;
        String nameServers = null;
        String whoisServer = null;
        String contactEmail = null;

        // 存储所有原始数据
        Map<String, String> rawData = new HashMap<>(headers.length);

        // 根据标题行映射字段
        for (int i = 0; i < headers.length && i < fields.length; i++) {
            String header = headers[i].toLowerCase();
            String value = fields[i];

            // 存储原始数据
            if (StringUtils.isNotEmpty(value)) {
                rawData.put(headers[i], value); // 保持原始大小写
            }

            // 提取核心字段
            switch (header) {
                case "domainname":
                    domainName = value;
                    break;
                case "registrarname":
                    registrarName = value;
                    break;
                case "registrant_organization":
                    registrantOrganization = value;
                    break;
                case "registrant_country":
                    registrantCountry = value;
                    break;
                case "createddate":
                    createdDate = value;
                    break;
                case "updateddate":
                    updatedDate = value;
                    break;
                case "expiresdate":
                    expiresDate = value;
                    break;
                case "status":
                    status = value;
                    break;
                case "nameservers":
                    nameServers = value;
                    break;
                case "whoisserver":
                    whoisServer = value;
                    break;
                case "contactemail":
                    contactEmail = value;
                    break;
                default:
                    // 其他字段已存储在rawData中
                    break;
            }
        }

        if (StringUtils.isEmpty(domainName)) {
            return null;
        }

        return new DomainWhoisInfo(domainName, registrarName, registrantOrganization,
                                  registrantCountry, createdDate, updatedDate, expiresDate,
                                  status, nameServers, whoisServer, contactEmail, rawData);
    }

    /**
     * 获取域名WHOIS信息
     *
     * @param domain 域名
     * @return WHOIS信息，如果不存在则返回空字符串
     */
    public String getDomainWhois(String domain) {
        if (StringUtils.isEmpty(domain)) {
            return "";
        }

        // 确保数据已加载
        loadDomainWhoisInfo();

        // 从缓存中获取
        DomainWhoisInfo whoisInfo = domainWhoisMap.get(domain.toLowerCase());
        if (whoisInfo != null) {
            return whoisInfo.getWhoisSummary();
        }
        return "";
    }

    /**
     * 获取域名详细WHOIS信息
     *
     * @param domain 域名
     * @return 详细WHOIS信息对象，如果不存在则返回null
     */
    public DomainWhoisInfo getDomainWhoisInfo(String domain) {
        if (StringUtils.isEmpty(domain)) {
            return null;
        }

        // 确保数据已加载
        loadDomainWhoisInfo();

        return domainWhoisMap.get(domain.toLowerCase());
    }

    /**
     * 刷新域名WHOIS缓存
     */
    public void refresh() {
        domainWhoisMap.clear();
        loadDomainWhoisInfo();
        log.info("域名WHOIS缓存已刷新");
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存中的域名数量
     */
    public int getCacheSize() {
        return domainWhoisMap.size();
    }

    /**
     * 域名WHOIS信息数据类
     * 支持新的混合设计，包含核心字段和完整JSON数据
     */
    public static class DomainWhoisInfo {
        private final String domainName;
        private final String registrarName;
        private final String registrantOrganization;
        private final String registrantCountry;
        private final String createdDate;
        private final String updatedDate;
        private final String expiresDate;
        private final String status;
        private final String nameServers;
        private final String whoisServer;
        private final String contactEmail;

        // 完整的原始数据，用于生成JSON
        private final Map<String, String> rawData;

        public DomainWhoisInfo(String domainName, String registrarName, String registrantOrganization,
                              String createdDate, String expiresDate, String status) {
            this(domainName, registrarName, registrantOrganization, null, createdDate, null,
                 expiresDate, status, null, null, null, new HashMap<>());
        }

        public DomainWhoisInfo(String domainName, String registrarName, String registrantOrganization,
                              String registrantCountry, String createdDate, String updatedDate,
                              String expiresDate, String status, String nameServers,
                              String whoisServer, String contactEmail, Map<String, String> rawData) {
            this.domainName = domainName;
            this.registrarName = registrarName;
            this.registrantOrganization = registrantOrganization;
            this.registrantCountry = registrantCountry;
            this.createdDate = createdDate;
            this.updatedDate = updatedDate;
            this.expiresDate = expiresDate;
            this.status = status;
            this.nameServers = nameServers;
            this.whoisServer = whoisServer;
            this.contactEmail = contactEmail;
            this.rawData = rawData != null ? rawData : new HashMap<>();
        }

        // 核心字段的getter方法
        public String getDomainName() { return domainName; }
        public String getRegistrarName() { return registrarName; }
        public String getRegistrantOrganization() { return registrantOrganization; }
        public String getRegistrantCountry() { return registrantCountry; }
        public String getCreatedDate() { return createdDate; }
        public String getUpdatedDate() { return updatedDate; }
        public String getExpiresDate() { return expiresDate; }
        public String getStatus() { return status; }
        public String getNameServers() { return nameServers; }
        public String getWhoisServer() { return whoisServer; }
        public String getContactEmail() { return contactEmail; }

        /**
         * 获取域名服务器数组
         * @return 域名服务器数组
         */
        public String[] getNameServersArray() {
            if (StringUtils.isEmpty(nameServers)) {
                return new String[0];
            }
            return nameServers.split("\\|");
        }

        /**
         * 获取完整的JSON格式WHOIS数据
         * @return JSON格式的WHOIS数据
         */
        public String getWhoisJson() {
            try {
                Map<String, Object> jsonData = new HashMap<>();

                // 基本信息
                jsonData.put("domainName", domainName);
                jsonData.put("registrarName", registrarName);
                jsonData.put("contactEmail", contactEmail);
                jsonData.put("whoisServer", whoisServer);
                jsonData.put("nameServers", nameServers);
                jsonData.put("createdDate", createdDate);
                jsonData.put("updatedDate", updatedDate);
                jsonData.put("expiresDate", expiresDate);
                jsonData.put("status", status);

                // 注册人信息
                Map<String, Object> registrant = new HashMap<>();
                registrant.put("organization", registrantOrganization);
                registrant.put("country", registrantCountry);

                // 从原始数据中提取更多注册人信息
                for (Map.Entry<String, String> entry : rawData.entrySet()) {
                    String key = entry.getKey();
                    if (key.startsWith("registrant_")) {
                        String fieldName = key.substring("registrant_".length());
                        registrant.put(fieldName, entry.getValue());
                    }
                }
                jsonData.put("registrant", registrant);

                // 管理联系人信息
                Map<String, Object> adminContact = new HashMap<>();
                for (Map.Entry<String, String> entry : rawData.entrySet()) {
                    String key = entry.getKey();
                    if (key.startsWith("administrativeContact_")) {
                        String fieldName = key.substring("administrativeContact_".length());
                        adminContact.put(fieldName, entry.getValue());
                    }
                }
                if (!adminContact.isEmpty()) {
                    jsonData.put("administrativeContact", adminContact);
                }

                // 添加其他原始数据
                for (Map.Entry<String, String> entry : rawData.entrySet()) {
                    String key = entry.getKey();
                    if (!key.startsWith("registrant_") && !key.startsWith("administrativeContact_")) {
                        jsonData.put(key, entry.getValue());
                    }
                }

                // 使用简单的JSON序列化（避免引入额外依赖）
                return toJsonString(jsonData);

            } catch (Exception e) {
                log.warn("生成WHOIS JSON数据失败: {}", domainName, e);
                return "{}";
            }
        }

        /**
         * 简单的JSON序列化方法
         */
        private String toJsonString(Map<String, Object> data) {
            StringBuilder json = new StringBuilder("{");
            boolean first = true;

            for (Map.Entry<String, Object> entry : data.entrySet()) {
                if (!first) {
                    json.append(",");
                }
                first = false;

                json.append("\"").append(entry.getKey()).append("\":");
                Object value = entry.getValue();
                if (value == null) {
                    json.append("null");
                } else if (value instanceof Map) {
                    json.append(toJsonString((Map<String, Object>) value));
                } else {
                    json.append("\"").append(String.valueOf(value).replace("\"", "\\\"")).append("\"");
                }
            }

            json.append("}");
            return json.toString();
        }

        /**
         * 获取WHOIS信息摘要
         *
         * @return WHOIS摘要字符串
         */
        public String getWhoisSummary() {
            StringBuilder summary = new StringBuilder();

            if (StringUtils.isNotEmpty(registrarName)) {
                summary.append("Registrar: ").append(registrarName);
            }

            if (StringUtils.isNotEmpty(registrantOrganization)) {
                if (summary.length() > 0) {
                    summary.append("; ");
                }
                summary.append("Organization: ").append(registrantOrganization);
            }

            return summary.toString();
        }

        @Override
        public String toString() {
            return String.format("DomainWhoisInfo{domain='%s', registrar='%s', organization='%s', created='%s', expires='%s', status='%s'}",
                    domainName, registrarName, registrantOrganization, createdDate, expiresDate, status);
        }
    }
}
