package com.geeksec.knowledgebase.service.impl;

import com.geeksec.knowledgebase.domain.entity.GeoIpInfo;
import com.geeksec.knowledgebase.service.GeoIpService;
import com.geeksec.knowledgebase.util.GeoIpDatabaseManager;
import com.maxmind.geoip2.model.AsnResponse;
import com.maxmind.geoip2.model.CityResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class GeoIpServiceImpl implements GeoIpService {

    private final GeoIpDatabaseManager geoIpDatabaseManager;

    @Override
    public Optional<GeoIpInfo> getGeoIpInfo(String ipAddress) {
        try {
            InetAddress ip = InetAddress.getByName(ipAddress);
            CityResponse cityResponse = geoIpDatabaseManager.getCityReader().city(ip);
            AsnResponse asnResponse = geoIpDatabaseManager.getAsnReader().asn(ip);
            return Optional.of(new GeoIpInfo(cityResponse, asnResponse));
        } catch (Exception e) {
            log.warn("无法获取IP地址 {} 的GeoIP信息: {}", ipAddress, e.getMessage());
            return Optional.empty();
        }
    }

    @Override
    public List<GeoIpInfo> getGeoIpInfoBatch(List<String> ipAddresses) {
        return ipAddresses.stream()
                .map(this::getGeoIpInfo)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
    }

    @Override
    public String getDatabaseInfo() {
        return geoIpDatabaseManager.getDatabaseInfo();
    }

    @Override
    public boolean refreshGeoIpDatabase() {
        return geoIpDatabaseManager.reloadDatabases();
    }
    
    // The following methods are not fully implemented in the original code, 
    // so they are left as placeholders. A full implementation would require
    // more complex data processing and aggregation logic.

    @Override
    public Map<String, GeoIpInfo> batchGetGeoIpInfo(List<String> ipAddresses) {
        return ipAddresses.stream()
                .map(ip -> Map.entry(ip, getGeoIpInfo(ip)))
                .filter(entry -> entry.getValue().isPresent())
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().get()));
    }

    @Override
    public String getCountryCode(String ipAddress) {
        return getGeoIpInfo(ipAddress).map(GeoIpInfo::getCountryCode).orElse("--");
    }

    @Override
    public String getAsnInfo(String ipAddress) {
        return getGeoIpInfo(ipAddress).map(GeoIpInfo::getAutonomousSystemOrganization).orElse("--");
    }

    @Override
    public String getLocation(String ipAddress) {
        return getGeoIpInfo(ipAddress).map(GeoIpInfo::getCityName).orElse("--");
    }

    @Override
    public String getCoordinates(String ipAddress) {
        return getGeoIpInfo(ipAddress)
                .map(info -> info.getLatitude() + "," + info.getLongitude())
                .orElse("0,0");
    }

    @Override
    public CityResponse getCityResponse(String ipAddress) {
        try {
            return geoIpDatabaseManager.getCityReader().city(InetAddress.getByName(ipAddress));
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public AsnResponse getAsnResponse(String ipAddress) {
        try {
            return geoIpDatabaseManager.getAsnReader().asn(InetAddress.getByName(ipAddress));
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Map<String, Object>> getCountryDistribution() {
        return List.of(); // Placeholder
    }

    @Override
    public List<Map<String, Object>> getProvinceDistribution() {
        return List.of(); // Placeholder
    }

    @Override
    public List<Map<String, Object>> getCityDistribution() {
        return List.of(); // Placeholder
    }

    @Override
    public List<Map<String, Object>> getAsnDistribution() {
        return List.of(); // Placeholder
    }
}
