package com.geeksec.knowledgebase.domain.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

/**
 * 指纹知识实体
 *
 * <AUTHOR>
 */
@Data
@Table("fingerprint_knowledge")
public class FingerprintKnowledge {

    @Id(keyType = KeyType.Auto)
    private Long id;
    
    /**
     * 指纹值
     */
    @Column("fingerprint")
    private String fingerprint;

    /**
     * JA3指纹
     */
    @Column("ja3")
    private String ja3;

    /**
     * 指纹类型
     */
    @Column("type")
    private String type;

    /**
     * 应用/设备名称
     */
    @Column("application")
    private String application;

    /**
     * 版本
     */
    @Column("version")
    private String version;

    /**
     * 操作系统
     */
    @Column("os")
    private String os;

    /**
     * 设备类型
     */
    @Column("device_type")
    private String deviceType;

    /**
     * 厂商
     */
    @Column("vendor")
    private String vendor;

    /**
     * 描述
     */
    @Column("description")
    private String description;

    /**
     * 置信度 (0-100)
     */
    @Column("confidence")
    private Integer confidence;

    /**
     * 是否活跃
     */
    @Column("active")
    private boolean active = true;
    
    /**
     * 数据来源
     */
    @Column(length = 50)
    private String source = "FINGERPRINT";
}
