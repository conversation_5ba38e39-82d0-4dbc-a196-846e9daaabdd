package com.geeksec.knowledgebase.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Knife4j API文档配置
 * 
 * <AUTHOR>
 */
@Configuration
public class Knife4jConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("NTA 3.0 知识库服务 API")
                        .description("提供威胁情报、检测规则、地理位置等知识数据的统一管理和API访问")
                        .version("3.0.0-SNAPSHOT")
                        .contact(new Contact()
                                .name("hufengkai")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("https://www.apache.org/licenses/LICENSE-2.0.html")));
    }
}
