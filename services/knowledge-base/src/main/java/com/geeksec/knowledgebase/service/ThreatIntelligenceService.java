package com.geeksec.knowledgebase.service;

import com.geeksec.knowledgebase.domain.entity.ThreatIntelligence;
import java.util.List;
import java.util.Optional;

/**
 * 威胁情报服务
 */
public interface ThreatIntelligenceService {

    /**
     * 检查指标是否存在
     * @param indicator 威胁指标
     * @return 威胁情报
     */
    Optional<ThreatIntelligence> checkIndicator(String indicator);

    List<ThreatIntelligence> findAll();

    ThreatIntelligence create(ThreatIntelligence threatIntelligence);

    ThreatIntelligence update(ThreatIntelligence threatIntelligence);

    void delete(Long id);

    List<ThreatIntelligence> batchCreate(List<ThreatIntelligence> threatIntelligences);
}
