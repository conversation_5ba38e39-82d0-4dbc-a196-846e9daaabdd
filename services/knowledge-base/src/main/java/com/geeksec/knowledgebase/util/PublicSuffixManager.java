package com.geeksec.knowledgebase.util;

import com.geeksec.knowledgebase.util.IpUtils;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixList;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixListFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.InputStream;

/**
 * Manages the Public Suffix List (PSL) for domain parsing.
 * This class provides utilities to normalize domains and extract
 * registrable domains and public suffixes.
 * The public_suffix_list.dat file is expected to be in the classpath resources.
 *
 * <AUTHOR>
 */
@Slf4j
public class PublicSuffixManager {

    private static volatile PublicSuffixManager instance;
    private final PublicSuffixList psl;

    private static final String PSL_FILE_NAME = "public_suffix_list.dat";

    /**
     * 知识库数据路径（统一存储在 PersistentVolume 中）
     */
    private static final String KNOWLEDGE_BASE_DATA_PATH = "/opt/flink/data/knowledge-base";

    /**
     * Public Suffix List完整路径
     */
    private static final String PSL_FILE_PATH = KNOWLEDGE_BASE_DATA_PATH + "/public-suffix/" + PSL_FILE_NAME;

    private PublicSuffixManager() {
        PublicSuffixList tempPsl = null;
        try {
            PublicSuffixListFactory factory = new PublicSuffixListFactory();
            tempPsl = loadFromFile(factory, PSL_FILE_PATH);
        } catch (Exception e) {
            log.error("初始化Public Suffix List失败", e);
        }
        this.psl = tempPsl;
    }

    /**
     * 从文件路径加载Public Suffix List
     */
    private PublicSuffixList loadFromFile(PublicSuffixListFactory factory, String filePath) {
        try {
            java.io.File file = new java.io.File(filePath);
            if (file.exists() && file.canRead()) {
                try (InputStream pslStream = new java.io.FileInputStream(file)) {
                    PublicSuffixList psl = factory.build(pslStream);
                    log.info("Public Suffix List加载成功：{}", filePath);
                    return psl;
                }
            } else {
                log.error("Public Suffix List文件不存在或不可读：{}", filePath);
                return null;
            }
        } catch (Exception e) {
            log.error("加载Public Suffix List失败：{}", filePath, e);
            return null;
        }
    }

    public static PublicSuffixManager getInstance() {
        if (instance == null) {
            synchronized (PublicSuffixManager.class) {
                if (instance == null) {
                    instance = new PublicSuffixManager();
                }
            }
        }
        return instance;
    }

    /**
     * Normalizes a domain string: converts to lowercase, removes common protocol
     * schemes,
     * port numbers, paths, leading/trailing dots, and performs Punycode conversion.
     *
     * @param domain The original domain string.
     * @return The normalized domain string, or null if the input is blank or
     *         becomes blank after processing.
     */
    public String normalizeDomain(String domain) {
        if (StringUtils.isBlank(domain)) {
            return null;
        }
        String normalized = domain.trim().toLowerCase();

        // Remove scheme (http://, https://, ftp://, etc.)
        int schemeIndex = normalized.indexOf("://");
        if (schemeIndex != -1) {
            normalized = normalized.substring(schemeIndex + 3);
        }

        // Remove user info (user:pass@)
        int userInfoIndex = normalized.indexOf('@');
        if (userInfoIndex != -1) {
            normalized = normalized.substring(userInfoIndex + 1);
        }

        // Remove path part (from the first /)
        int pathIndex = normalized.indexOf('/');
        if (pathIndex != -1) {
            normalized = normalized.substring(0, pathIndex);
        }

        // Remove port number (if any, after removing path)
        int portIndex = normalized.lastIndexOf(':');
        // Check if this colon is part of an IPv6 address (e.g., [::1]:80 or just ::1)
        // A simple check: if there's a ']' before ':', it might be IPv6 with port.
        // Or if there are other colons and no ']', it's likely IPv6.
        // This heuristic isn't perfect for all edge cases of IPv6 and ports.
        boolean mightBeIPv6 = normalized.contains(":")
                && (normalized.contains("]") || StringUtils.countMatches(normalized, ":") > 1);
        if (portIndex != -1 && !mightBeIPv6) {
            // Ensure it's a port and not part of an IPv6 address without brackets
            boolean isLikelyPort = true;
            if (normalized.lastIndexOf(']') > portIndex) { // e.g. host:[::1]
                isLikelyPort = false;
            }
            if (isLikelyPort) {
                String portStr = normalized.substring(portIndex + 1);
                if (portStr.matches("\\d+")) { // Check if the part after colon is numeric
                    normalized = normalized.substring(0, portIndex);
                }
            }
        }

        // Remove leading/trailing dots, but be careful not to remove all dots from
        // valid domains like "..."
        normalized = StringUtils.strip(normalized, ".");

        if (StringUtils.isBlank(normalized)) {
            return null;
        }

        // Handle Internationalized Domain Names (IDN) by converting to ASCII (Punycode)
        // The PSL library usually expects Punycode for IDNs.
        try {
            // java.net.IDN.toASCII will return the input string if it's already ASCII.
            normalized = java.net.IDN.toASCII(normalized, java.net.IDN.ALLOW_UNASSIGNED);
        } catch (IllegalArgumentException e) {
            log.warn("Failed to convert domain to ASCII (Punycode): '{}'. This might be an invalid domain. Error: {}",
                    normalized, e.getMessage());
            return null; // Conversion failed, treat as invalid.
        }
        return normalized;
    }

    /**
     * Checks if the Public Suffix List was loaded successfully.
     *
     * @return true if PSL is available, false otherwise.
     */
    public boolean isPslAvailable() {
        return this.psl != null;
    }

    /**
     * Gets the registrable domain (e.g., "example.com" from "www.example.com").
     * Assumes the input domain is already normalized.
     *
     * @param normalizedDomain The normalized domain string.
     * @return The registrable domain, or null if not found, not a domain, or PSL
     *         not available.
     */
    public String getRegistrableDomain(String normalizedDomain) {
        if (!isPslAvailable() || StringUtils.isBlank(normalizedDomain) ||
                (IpUtils.isValidIpv4(normalizedDomain) || IpUtils.isValidIpv6(normalizedDomain))) {
            return null;
        }
        try {
            return psl.getRegistrableDomain(normalizedDomain);
        } catch (Exception e) {
            log.trace("Could not get registrable domain for '{}' using de.malkusch.PublicSuffixList: {}",
                    normalizedDomain, e.getMessage());
            return null;
        }
    }

    /**
     * Gets the public suffix (e.g., "com" from "www.example.com").
     * Assumes the input domain is already normalized.
     *
     * @param normalizedDomain The normalized domain string.
     * @return The public suffix, or null if not found, not a domain, or PSL not
     *         available.
     */
    public String getPublicSuffix(String normalizedDomain) {
        if (!isPslAvailable() || StringUtils.isBlank(normalizedDomain) ||
                (IpUtils.isValidIpv4(normalizedDomain) || IpUtils.isValidIpv6(normalizedDomain))) {
            return null;
        }
        try {
            return psl.getPublicSuffix(normalizedDomain);
        } catch (Exception e) {
            log.trace("Could not get public suffix for '{}' using de.malkusch.PublicSuffixList: {}", normalizedDomain,
                    e.getMessage());
            return null;
        }
    }

    /**
     * Determines if the given normalized string is a valid domain (not an IP, and
     * has a public suffix).
     * Assumes the input domain is already normalized.
     *
     * @param normalizedDomain The normalized domain string.
     * @return true if it's considered a domain that can be processed by PSL, false
     *         otherwise.
     */
    public boolean isDomain(String normalizedDomain) {
        if (!isPslAvailable() || StringUtils.isBlank(normalizedDomain) ||
                (IpUtils.isValidIpv4(normalizedDomain) || IpUtils.isValidIpv6(normalizedDomain))) {
            return false;
        }
        try {
            // Alternative logic: if it has a registrable domain, it's likely a domain.
            // This is used because the IDE reports psl.isDomain() as undefined.
            // Ideally, psl.isDomain() should be used if available and working.
            return psl.getRegistrableDomain(normalizedDomain) != null;
        } catch (Exception e) {
            log.trace("Error checking if '{}' is a domain (using getRegistrableDomain) with de.malkusch.PublicSuffixList: {}",
                    normalizedDomain, e.getMessage());
            return false;
        }
    }
}
