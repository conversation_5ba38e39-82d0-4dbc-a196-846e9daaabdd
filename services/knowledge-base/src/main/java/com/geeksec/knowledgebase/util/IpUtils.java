package com.geeksec.knowledgebase.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * IP地址工具类
 */
@Slf4j
public class IpUtils {

    private static final String UNKNOWN = "unknown";
    private static final String LOCALHOST_IP = "127.0.0.1";
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";
    private static final String SEPARATOR = ",";

    private IpUtils() {
        // 私有构造方法，防止实例化
    }

    /**
     * 获取客户端真实IP地址
     * 使用Nginx等反向代理软件， 则不能直接通过request.getRemoteAddr()获取IP地址
     * 如果使用了多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP地址，X-Forwarded-For中第一个非unknown的有效IP字符串，则为真实IP地址
     */
    public static String getIpAddr(HttpServletRequest request) {
        if (request == null) {
            return UNKNOWN;
        }
        
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Forwarded-For");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
            if (LOCALHOST_IP.equals(ip) || LOCALHOST_IPV6.equals(ip)) {
                // 根据网卡取本机配置的IP
                try {
                    InetAddress inet = InetAddress.getLocalHost();
                    ip = inet.getHostAddress();
                } catch (UnknownHostException e) {
                    log.error("获取IP地址异常: ", e);
                }
            }
        }
        
        // 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if (ip != null && ip.length() > 15) {
            if (ip.indexOf(SEPARATOR) > 0) {
                ip = ip.substring(0, ip.indexOf(SEPARATOR));
            }
        }
        
        return StringUtils.isBlank(ip) ? UNKNOWN : ip;
    }
    
    /**
     * 将IP地址转换为long值
     */
    public static long ipToLong(String ipAddress) {
        if (ipAddress == null || ipAddress.isEmpty()) {
            throw new IllegalArgumentException("IP地址不能为空");
        }
        
        String[] ipAddressInArray = ipAddress.split("\\.");
        if (ipAddressInArray.length != 4) {
            throw new IllegalArgumentException("无效的IP地址: " + ipAddress);
        }
        
        long result = 0;
        for (int i = 0; i < ipAddressInArray.length; i++) {
            int power = 3 - i;
            int ip = Integer.parseInt(ipAddressInArray[i]);
            result += ip * Math.pow(256, power);
        }
        return result;
    }
    
    /**
     * 将long值转换为IP地址
     */
    public static String longToIp(long ip) {
        return ((ip >> 24) & 0xFF) + "." +
               ((ip >> 16) & 0xFF) + "." +
               ((ip >> 8) & 0xFF) + "." +
               (ip & 0xFF);
    }
    
    /**
     * 检查IP地址是否在指定范围内
     * @param ipAddress IP地址
     * @param startIp 起始IP
     * @param endIp 结束IP
     * @return 是否在范围内
     */
    public static boolean isInRange(String ipAddress, String startIp, String endIp) {
        try {
            long ip = ipToLong(ipAddress);
            long start = ipToLong(startIp);
            long end = ipToLong(endIp);
            return ip >= start && ip <= end;
        } catch (Exception e) {
            log.error("检查IP范围异常: ", e);
            return false;
        }
    }
    
    /**
     * 检查IP地址是否为内网IP
     */
    public static boolean isInternalIp(String ipAddress) {
        try {
            long ip = ipToLong(ipAddress);
            // 10.0.0.0 - **************
            long aBegin = ipToLong("10.0.0.0");
            long aEnd = ipToLong("**************");
            // ********** - **************
            long bBegin = ipToLong("**********");
            long bEnd = ipToLong("**************");
            // *********** - ***************
            long cBegin = ipToLong("***********");
            long cEnd = ipToLong("***************");
            // 127.0.0.1
            long localhost = ipToLong("127.0.0.1");
            
            return isInRange(ip, aBegin, aEnd) || 
                   isInRange(ip, bBegin, bEnd) || 
                   isInRange(ip, cBegin, cEnd) || 
                   ip == localhost;
        } catch (Exception e) {
            log.error("检查内网IP异常: ", e);
            return false;
        }
    }
}
