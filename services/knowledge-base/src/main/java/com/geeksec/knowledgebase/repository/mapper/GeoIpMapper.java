package com.geeksec.knowledgebase.repository.mapper;

import com.geeksec.knowledgebase.domain.entity.GeoIpInfo;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Optional;

/**
 * 地理位置信息 Mapper 接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface GeoIpMapper extends BaseMapper<GeoIpInfo> {

    /**
     * 根据IP地址查询地理位置信息
     */
    default Optional<GeoIpInfo> findByIpAddress(String ipAddress) {
        GeoIpInfo geoIpInfo = selectOneByQuery(QueryWrapper.create()
                .where(GeoIpInfo::getIpAddress).eq(ipAddress));
        return Optional.ofNullable(geoIpInfo);
    }

    /**
     * 根据国家代码查询IP列表
     */
    default List<GeoIpInfo> findByCountryCode(String countryCode) {
        return selectListByQuery(QueryWrapper.create()
                .where(GeoIpInfo::getCountryCode).eq(countryCode));
    }

    /**
     * 根据国家名称查询IP列表
     */
    default List<GeoIpInfo> findByCountryName(String countryName) {
        return selectListByQuery(QueryWrapper.create()
                .where(GeoIpInfo::getCountryName).eq(countryName));
    }

    /**
     * 根据城市名称查询IP列表
     */
    default List<GeoIpInfo> findByCityName(String cityName) {
        return selectListByQuery(QueryWrapper.create()
                .where(GeoIpInfo::getCityName).eq(cityName));
    }

    /**
     * 根据省份名称查询IP列表
     */
    default List<GeoIpInfo> findByProvinceName(String provinceName) {
        return selectListByQuery(QueryWrapper.create()
                .where(GeoIpInfo::getProvinceName).eq(provinceName));
    }

    /**
     * 根据ASN编号查询IP列表
     */
    default List<GeoIpInfo> findByAsnNumber(String asnNumber) {
        return selectListByQuery(QueryWrapper.create()
                .where(GeoIpInfo::getAsnNumber).eq(asnNumber));
    }

    /**
     * 根据组织名称查询IP列表
     */
    default List<GeoIpInfo> findByOrganization(String organization) {
        return selectListByQuery(QueryWrapper.create()
                .where(GeoIpInfo::getOrganization).eq(organization));
    }

    /**
     * 根据组织名称模糊查询IP列表
     */
    default List<GeoIpInfo> findByOrganizationContaining(String organizationPattern) {
        return selectListByQuery(QueryWrapper.create()
                .where(GeoIpInfo::getOrganization).like(organizationPattern));
    }

    /**
     * 根据经纬度范围查询IP列表
     */
    default List<GeoIpInfo> findByLocationRange(Double minLongitude, Double maxLongitude, 
                                               Double minLatitude, Double maxLatitude) {
        return selectListByQuery(QueryWrapper.create()
                .where(GeoIpInfo::getLongitude).between(minLongitude, maxLongitude)
                .and(GeoIpInfo::getLatitude).between(minLatitude, maxLatitude));
    }

    /**
     * 根据国家和城市查询IP列表
     */
    default List<GeoIpInfo> findByCountryCodeAndCityName(String countryCode, String cityName) {
        return selectListByQuery(QueryWrapper.create()
                .where(GeoIpInfo::getCountryCode).eq(countryCode)
                .and(GeoIpInfo::getCityName).eq(cityName));
    }

    /**
     * 查询指定时间后更新的IP信息
     */
    default List<GeoIpInfo> findByLastUpdatedAfter(Long timestamp) {
        return selectListByQuery(QueryWrapper.create()
                .where(GeoIpInfo::getLastUpdated).gt(timestamp));
    }

    /**
     * 获取所有国家代码列表
     */
    default List<String> findDistinctCountryCodes() {
        // 这个方法需要使用原生SQL或者在Service层实现
        // 暂时返回空列表，在Service层实现具体逻辑
        return List.of();
    }

    /**
     * 获取所有国家名称列表
     */
    default List<String> findDistinctCountryNames() {
        // 这个方法需要使用原生SQL或者在Service层实现
        // 暂时返回空列表，在Service层实现具体逻辑
        return List.of();
    }

    /**
     * 获取指定国家的所有城市列表
     */
    default List<String> findDistinctCitiesByCountryCode(String countryCode) {
        // 这个方法需要使用原生SQL或者在Service层实现
        // 暂时返回空列表，在Service层实现具体逻辑
        return List.of();
    }

    /**
     * 统计各国家的IP数量
     */
    default List<Object[]> countByCountryCode() {
        // 这个方法需要使用原生SQL或者在Service层实现
        // 暂时返回空列表，在Service层实现具体逻辑
        return List.of();
    }

    /**
     * 统计各组织的IP数量
     */
    default List<Object[]> countByOrganization() {
        // 这个方法需要使用原生SQL或者在Service层实现
        // 暂时返回空列表，在Service层实现具体逻辑
        return List.of();
    }

    /**
     * 批量查询IP地址列表的地理位置信息
     */
    default List<GeoIpInfo> findByIpAddressIn(List<String> ipAddresses) {
        return selectListByQuery(QueryWrapper.create()
                .where(GeoIpInfo::getIpAddress).in(ipAddresses));
    }
}
