package com.geeksec.knowledgebase.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 知识库服务配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "knowledge-base")
public class KnowledgeBaseProperties {
    private TrancoBaseProperties trancobase;
    private DomainWhoisProperties domainWhois;
    private FingerprintProperties fingerprint;
    private GeoIpProperties geoIp;

    @Data
    public static class TrancoBaseProperties {
        private String dataFile;
        private long cacheTtl;
        private String refreshCron;
    }

    @Data
    public static class DomainWhoisProperties {
        private String dataFile;
        private long cacheTtl;
        private String refreshCron;
    }

    @Data
    public static class FingerprintProperties {
        private String dataFile;
        private long cacheTtl;
        private String refreshCron;
    }
    
    @Data
    public static class GeoIpProperties {
        /**
         * GeoIP 数据库文件基础路径
         */
        private String databasePath = "/data/geoip";
        
        /**
         * 城市数据库文件名
         */
        private String cityDbName = "GeoLite2-City.mmdb";
        
        /**
         * ASN数据库文件名
         */
        private String asnDbName = "GeoLite2-ASN.mmdb";
        
        /**
         * 缓存过期时间(秒)
         */
        private long cacheTtl = 86400;
        
        /**
         * 数据库刷新cron表达式
         */
        private String refreshCron = "0 0 2 * * ?";
        
        /**
         * 是否启用GeoIP功能
         */
        private boolean enabled = true;
    }
}
