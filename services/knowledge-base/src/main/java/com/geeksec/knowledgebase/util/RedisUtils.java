package com.geeksec.knowledgebase.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * Redis工具类
 */
@Slf4j
@Component
public class RedisUtils {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    private static final String LOCK_SCRIPT = 
        "if redis.call('setnx', KEYS[1], ARGV[1]) == 1 then " +
        "return redis.call('pexpire', KEYS[1], ARGV[2]) else return 0 end";
    
    private static final String UNLOCK_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "return redis.call('del', KEYS[1]) else return 0 end";
    
    public RedisUtils(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
    
    /**
     * 获取分布式锁
     * @param lockKey 锁的key
     * @param requestId 请求标识
     * @param expireTime 过期时间(毫秒)
     * @return 是否获取成功
     */
    public boolean tryGetDistributedLock(String lockKey, String requestId, long expireTime) {
        try {
            RedisScript<Long> redisScript = new DefaultRedisScript<>(LOCK_SCRIPT, Long.class);
            Long result = redisTemplate.execute(redisScript, 
                Collections.singletonList(lockKey), 
                requestId, 
                String.valueOf(expireTime));
            return result != null && result == 1;
        } catch (Exception e) {
            log.error("Error getting distributed lock: " + lockKey, e);
            return false;
        }
    }
    
    /**
     * 释放分布式锁
     * @param lockKey 锁的key
     * @param requestId 请求标识
     * @return 是否释放成功
     */
    public boolean releaseDistributedLock(String lockKey, String requestId) {
        try {
            RedisScript<Long> redisScript = new DefaultRedisScript<>(UNLOCK_SCRIPT, Long.class);
            Long result = redisTemplate.execute(redisScript, 
                Collections.singletonList(lockKey), 
                requestId);
            return result != null && result == 1;
        } catch (Exception e) {
            log.error("Error releasing distributed lock: " + lockKey, e);
            return false;
        }
    }
    
    /**
     * 设置缓存
     */
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        try {
            redisTemplate.opsForValue().set(key, value, timeout, unit);
        } catch (Exception e) {
            log.error("Error setting cache for key: " + key, e);
        }
    }
    
    /**
     * 获取缓存
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key) {
        try {
            return (T) redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error("Error getting cache for key: " + key, e);
            return null;
        }
    }
    
    /**
     * 删除缓存
     */
    public void delete(String key) {
        try {
            redisTemplate.delete(key);
        } catch (Exception e) {
            log.error("Error deleting cache for key: " + key, e);
        }
    }
    
    /**
     * 批量删除缓存
     */
    public void deletePattern(String pattern) {
        try {
            redisTemplate.delete(redisTemplate.keys(pattern));
        } catch (Exception e) {
            log.error("Error deleting cache with pattern: " + pattern, e);
        }
    }
    
    /**
     * 设置过期时间
     */
    public boolean expire(String key, long timeout, TimeUnit unit) {
        try {
            return Boolean.TRUE.equals(redisTemplate.expire(key, timeout, unit));
        } catch (Exception e) {
            log.error("Error setting expire for key: " + key, e);
            return false;
        }
    }
    
    /**
     * 获取剩余过期时间
     */
    public long getExpire(String key, TimeUnit timeUnit) {
        try {
            Long expire = redisTemplate.getExpire(key, timeUnit);
            return expire != null ? expire : -2;
        } catch (Exception e) {
            log.error("Error getting expire for key: " + key, e);
            return -1;
        }
    }
}
