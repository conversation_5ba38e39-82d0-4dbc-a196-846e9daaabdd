package com.geeksec.knowledgebase.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 域名数据解析器
 * <p>
 * 负责从数据流中解析域名相关的配置文件。
 * 此类遵循单一职责原则，专注于域名数据的解析任务。
 *
 * <AUTHOR>
 */
public final class DomainDataParser {

    private DomainDataParser() {
        throw new AssertionError("工具类不应被实例化");
    }

    /**
     * 从 BufferedReader 加载域名列表。
     * <p>
     * 该方法读取 CSV 格式的域名文件，提取第二列的数据，并排除特定的字符串 "mine_domain"。
     *
     * @param reader BufferedReader 对象，提供域名数据源
     * @return 包含域名字符串的列表
     * @throws IOException 如果读取过程中发生错误
     */
    public static List<String> parseDomainList(BufferedReader reader) throws IOException {
        List<String> result = new ArrayList<>();
        String line;
        while ((line = reader.readLine()) != null) {
            String[] parts = line.trim().split(",");
            if (parts.length > 1) {
                String domain = parts[1];
                // 硬编码的业务逻辑：排除特定占位符
                if (!"mine_domain".equalsIgnoreCase(domain)) {
                    result.add(domain);
                }
            }
        }
        return result;
    }
}
