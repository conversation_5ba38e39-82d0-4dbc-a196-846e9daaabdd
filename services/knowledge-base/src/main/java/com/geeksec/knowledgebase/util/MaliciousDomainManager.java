package com.geeksec.knowledgebase.util;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 恶意域名管理器
 * 用于加载和检查域名是否在已知恶意域名列表中
 *
 * <AUTHOR>
 */
@Slf4j
public class MaliciousDomainManager {

    private static volatile MaliciousDomainManager instance = null;
    private final Set<String> maliciousDomains = ConcurrentHashMap.newKeySet();

    /**
     * 恶意域名资源路径（支持外部挂载）
     */
    private static final String MALICIOUS_DOMAINS_PATH = System.getProperty("malicious.domains.path",
        System.getenv("MALICIOUS_DOMAINS_PATH") != null ? System.getenv("MALICIOUS_DOMAINS_PATH") : "/opt/flink/resources/malicious-domains");

    private static final String[] DOMAIN_FILES = {
        "full-domains-aa.txt",
        "full-domains-ab.txt",
        "full-domains-ac.txt"
    };

    private MaliciousDomainManager() {
        loadMaliciousDomains();
    }

    /**
     * 获取单例实例
     * @return MaliciousDomainManager实例
     */
    public static MaliciousDomainManager getInstance() {
        if (instance == null) {
            synchronized (MaliciousDomainManager.class) {
                if (instance == null) {
                    instance = new MaliciousDomainManager();
                }
            }
        }
        return instance;
    }

    /**
     * 加载恶意域名列表
     */
    private void loadMaliciousDomains() {
        int totalLoaded = 0;

        for (String fileName : DOMAIN_FILES) {
            String filePath = MALICIOUS_DOMAINS_PATH + "/" + fileName;
            int count = loadFromFile(filePath, fileName);
            totalLoaded += count;
        }

        log.info("恶意域名管理器初始化完成，共加载 {} 个恶意域名", totalLoaded);
    }

    /**
     * 从文件路径加载恶意域名
     */
    private int loadFromFile(String filePath, String fileName) {
        try {
            java.io.File file = new java.io.File(filePath);
            if (!file.exists() || !file.canRead()) {
                log.error("恶意域名文件不存在或不可读：{}", filePath);
                return 0;
            }

            try (BufferedReader reader = new BufferedReader(
                    new java.io.FileReader(file, StandardCharsets.UTF_8))) {

                int count = 0;
                String domain;
                while ((domain = reader.readLine()) != null) {
                    domain = domain.trim().toLowerCase();
                    if (!domain.isEmpty()) {
                        maliciousDomains.add(domain);
                        count++;
                    }
                }
                log.info("已从 {} 加载 {} 个恶意域名", filePath, count);
                return count;
            }
        } catch (IOException e) {
            log.error("加载恶意域名文件失败：{}", filePath, e);
            return 0;
        }
    }

    /**
     * 检查域名是否在恶意域名列表中
     * @param domain 要检查的域名
     * @return 如果是恶意域名返回true，否则返回false
     */
    public boolean isMaliciousDomain(String domain) {
        if (domain == null || domain.isEmpty()) {
            return false;
        }
        // 转换为小写进行比较，确保大小写不敏感
        return maliciousDomains.contains(domain.toLowerCase());
    }

    /**
     * 获取恶意域名总数
     * @return 恶意域名数量
     */
    public int getMaliciousDomainCount() {
        return maliciousDomains.size();
    }
}
