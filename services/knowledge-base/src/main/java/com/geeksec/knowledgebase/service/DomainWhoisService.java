package com.geeksec.knowledgebase.service;

import com.geeksec.knowledgebase.domain.entity.DomainWhoisInfo;
import com.geeksec.knowledgebase.repository.mapper.DomainWhoisMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 域名WHOIS服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DomainWhoisService {

    private final DomainWhoisMapper domainWhoisMapper;

    /**
     * 根据域名查询WHOIS信息
     */
    public DomainWhoisInfo getDomainWhoisInfo(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return null;
        }
        return domainWhoisMapper.findByDomain(domain.toLowerCase()).orElse(null);
    }

    /**
     * 根据注册商查询域名列表
     */
    public List<DomainWhoisInfo> findByRegistrar(String registrar) {
        return domainWhoisMapper.findByRegistrar(registrar);
    }

    /**
     * 根据注册人查询域名列表
     */
    public List<DomainWhoisInfo> findByRegistrant(String registrant) {
        return domainWhoisMapper.findByRegistrant(registrant);
    }

}
