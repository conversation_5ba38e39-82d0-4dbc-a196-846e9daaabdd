package com.geeksec.knowledgebase.domain.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 域名WHOIS信息实体
 *
 * <AUTHOR>
 */
@Data
@Table("domain_whois_info")
public class DomainWhoisInfo {

    @Id(keyType = KeyType.Auto)
    private Long id;
    
    /**
     * 域名
     */
    @Column("domain")
    private String domain;

    /**
     * 注册商
     */
    @Column("registrar")
    private String registrar;

    /**
     * 注册人
     */
    @Column("registrant")
    private String registrant;

    /**
     * 注册人组织
     */
    @Column("registrant_org")
    private String registrantOrg;

    /**
     * 注册人国家/地区
     */
    @Column("registrant_country")
    private String registrantCountry;

    /**
     * 注册人邮箱
     */
    @Column("registrant_email")
    private String registrantEmail;

    /**
     * 注册日期
     */
    @Column("create_date")
    private LocalDateTime createDate;

    /**
     * 更新日期
     */
    @Column("update_date")
    private LocalDateTime updateDate;

    /**
     * 过期日期
     */
    @Column("expire_date")
    private LocalDateTime expireDate;

    /**
     * 名称服务器
     */
    @Column("name_servers")
    private String nameServers;
    
    /**
     * 状态
     */
    @Column("status")
    private String status;

    /**
     * 原始WHOIS信息
     */
    @Column("raw_whois")
    private String rawWhois;

    /**
     * 数据来源
     */
    @Column("source")
    private String source = "WHOIS";

    /**
     * 创建时间
     */
    @Column("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 是否有效
     */
    @Column("active")
    private boolean active = true;
}
