package com.geeksec.knowledgebase.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.knowledgebase.service.CertificateLabelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/certificate-label")
@RequiredArgsConstructor
@Tag(name = "Certificate Label Controller", description = "证书标签知识库")
public class CertificateLabelController {

    private final CertificateLabelService certificateLabelService;

    @GetMapping("/all-labels")
    @Operation(summary = "获取所有证书标签")
    public ApiResponse<List<Map<String, Object>>> getAllCertificateLabels() {
        log.debug("获取所有证书标签");
        List<Map<String, Object>> labels = certificateLabelService.getAllCertificateLabels();
        return ApiResponse.success(labels);
    }

    @GetMapping("/labels-by-type/{type}")
    @Operation(summary = "根据类型获取证书标签")
    public ApiResponse<List<Map<String, Object>>> getCertificateLabelsByType(
            @Parameter(description = "标签类型") @PathVariable String type) {
        log.debug("根据类型获取证书标签: {}", type);
        List<Map<String, Object>> labels = certificateLabelService.getCertificateLabelsByType(type);
        return ApiResponse.success(labels);
    }
}
