package com.geeksec.knowledgebase.util;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 指纹知识管理器
 * 提供指纹相关知识的获取和缓存功能
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FingerprintKnowledgeManager {

    private final DataSource dataSource;

    /**
     * 指纹类型映射
     */
    private final Map<String, String> fingerTypeMap = new ConcurrentHashMap<>();

    /**
     * 指纹JA3映射
     */
    private final Map<String, String> fingerJa3Map = new ConcurrentHashMap<>();

    /**
     * 加载指纹信息
     *
     * @return 指纹信息列表
     */
    public List<FingerInfo> loadFingerInfo() {
        log.info("开始加载指纹信息...");
        List<FingerInfo> resultList = new ArrayList<>();
        String sql = "SELECT * FROM finger_info";

        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                FingerInfo info = new FingerInfo();
                info.setId(rs.getInt("id"));
                info.setFingerType(rs.getString("finger_type"));
                info.setFingerInfo(rs.getString("finger_info"));
                resultList.add(info);
            }
            log.info("指纹信息加载成功，共加载 {} 条记录", resultList.size());
            return resultList;
        } catch (Exception e) {
            log.error("加载指纹信息失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 加载JA3指纹信息
     *
     * @return JA3指纹信息列表
     */
    public List<Ja3Info> loadJa3() {
        log.info("开始加载JA3指纹信息...");
        List<Ja3Info> resultList = new ArrayList<>();
        String sql = "SELECT * FROM ja3";

        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                Ja3Info info = new Ja3Info();
                info.setId(rs.getInt("id"));
                info.setJa3(rs.getString("ja3"));
                info.setFinger(rs.getString("finger"));
                resultList.add(info);
            }
            log.info("JA3指纹信息加载成功，共加载 {} 条记录", resultList.size());
            return resultList;
        } catch (Exception e) {
            log.error("加载JA3指纹信息失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 指纹信息内部类
     */
    @Getter
    @Setter
    public static class FingerInfo {
        private int id;
        private String fingerType;
        private String fingerInfo;
    }

    /**
     * JA3指纹信息内部类
     */
    @Getter
    @Setter
    public static class Ja3Info {
        private int id;
        private String ja3;
        private String finger;
    }
}
