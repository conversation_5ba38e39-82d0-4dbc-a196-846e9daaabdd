package com.geeksec.knowledgebase.service;

import com.geeksec.knowledgebase.domain.entity.CertificateLabel;
import com.geeksec.knowledgebase.domain.enums.CertificateLabelType;
import com.geeksec.knowledgebase.repository.mapper.CertificateLabelMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 证书标签服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class CertificateLabelService {

    private final CertificateLabelMapper certificateLabelMapper;

    private Map<String, Object> convertToMap(CertificateLabel label) {
        return Map.of(
                "labelId", label.getLabelId(),
                "labelName", label.getLabelName(),
                "labelRemark", label.getLabelRemark(),
                "labelType", label.getLabelType().name(),
                "isActive", label.getIsActive()
        );
    }

    @Cacheable(value = "certificate-labels", key = "'all'")
    public List<Map<String, Object>> getAllCertificateLabels() {
        return certificateLabelMapper.findByIsActiveTrue().stream()
                .map(this::convertToMap)
                .collect(Collectors.toList());
    }

    @Cacheable(value = "certificate-labels", key = "'type:' + #type")
    public List<Map<String, Object>> getCertificateLabelsByType(String type) {
        try {
            CertificateLabelType labelType = CertificateLabelType.valueOf(type.toUpperCase());
            return certificateLabelMapper.findByLabelTypeAndIsActiveTrue(labelType).stream()
                    .map(this::convertToMap)
                    .collect(Collectors.toList());
        } catch (IllegalArgumentException e) {
            log.warn("无效的证书标签类型: {}", type);
            return List.of();
        }
    }

    @Cacheable(value = "certificate-labels", key = "'id:' + #labelId")
    public Optional<CertificateLabel> findByLabelId(Integer labelId) {
        return certificateLabelMapper.findByLabelId(labelId);
    }

    @Cacheable(value = "certificate-labels", key = "'name:' + #labelName")
    public Optional<CertificateLabel> findByLabelName(String labelName) {
        return certificateLabelMapper.findByLabelName(labelName);
    }

    @Cacheable(value = "certificate-labels", key = "'remark:' + #labelRemark")
    public List<CertificateLabel> findByLabelRemark(String labelRemark) {
        // MyBatis-Flex 不支持按备注精确查询，使用模糊查询
        return certificateLabelMapper.findByLabelNameContainingIgnoreCaseAndIsActiveTrue(labelRemark);
    }

    @Cacheable(value = "certificate-labels", key = "'stats:type-count'")
    public Map<CertificateLabelType, Long> getLabelTypeStatistics() {
        // 暂时返回空Map，需要在Service层实现统计逻辑
        return Map.of();
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "certificate-labels", allEntries = true)
    public CertificateLabel create(CertificateLabel certificateLabel) {
        log.info("创建证书标签: {}", certificateLabel.getLabelName());
        certificateLabelMapper.insert(certificateLabel);
        return certificateLabel;
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "certificate-labels", allEntries = true)
    public CertificateLabel update(CertificateLabel certificateLabel) {
        log.info("更新证书标签: {}", certificateLabel.getLabelName());
        certificateLabelMapper.update(certificateLabel);
        return certificateLabel;
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "certificate-labels", allEntries = true)
    public void delete(Long id) {
        CertificateLabel label = certificateLabelMapper.selectOneById(id);
        if (label != null) {
            label.setIsActive(false);
            certificateLabelMapper.update(label);
            log.info("删除证书标签: {}", label.getLabelName());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "certificate-labels", allEntries = true)
    public List<CertificateLabel> batchCreate(List<CertificateLabel> certificateLabels) {
        log.info("批量创建证书标签: {} 条", certificateLabels.size());
        // 使用 MyBatis-Flex 的批量插入，性能更好
        certificateLabelMapper.insertBatch(certificateLabels);
        return certificateLabels;
    }
}
