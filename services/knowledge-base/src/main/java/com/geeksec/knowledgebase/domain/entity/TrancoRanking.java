package com.geeksec.knowledgebase.domain.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

/**
 * Tranco域名排名实体
 *
 * <AUTHOR>
 */
@Data
@Table("tranco_ranking")
public class TrancoRanking {

    @Id(keyType = KeyType.Auto)
    private Long id;

    /**
     * 域名
     */
    @Column("domain")
    private String domain;

    /**
     * 排名
     */
    @Column("rank_value")
    private Integer rankValue;

    /**
     * 数据版本
     */
    @Column("data_version")
    private String dataVersion;
    
    /**
     * 数据来源
     */
    @Column("source")
    private String source = "Tranco";
}
