package com.geeksec.knowledgebase.service;

import com.geeksec.knowledgebase.domain.entity.TrancoRanking;
import com.geeksec.knowledgebase.repository.mapper.TrancoRankingMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Tranco排名服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrancoRankingService {

    private final TrancoRankingMapper trancoRankingMapper;

    /**
     * 获取域名排名
     */
    public Integer getDomainRank(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return null;
        }
        return trancoRankingMapper.findByDomain(domain.toLowerCase())
                .map(TrancoRanking::getRankValue)
                .orElse(null);
    }

    /**
     * 检查域名是否在Tranco排名中
     */
    public boolean isDomainInTranco(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }
        return trancoRankingMapper.existsByDomain(domain.toLowerCase());
    }

    /**
     * 批量获取域名排名
     */
    public java.util.Map<String, Integer> batchGetDomainRanks(java.util.List<String> domains) {
        if (domains == null || domains.isEmpty()) {
            return java.util.Map.of();
        }

        java.util.Map<String, Integer> result = new java.util.HashMap<>();
        for (String domain : domains) {
            Integer rank = getDomainRank(domain);
            if (rank != null) {
                result.put(domain, rank);
            }
        }
        return result;
    }

    /**
     * 获取Top域名列表
     */
    public java.util.List<java.util.Map<String, Object>> getTopDomains(int limit) {
        java.util.List<TrancoRanking> rankings = trancoRankingMapper.findTopRankedDomains(limit);
        return rankings.stream()
                .map(ranking -> {
                    java.util.Map<String, Object> map = new java.util.HashMap<>();
                    map.put("domain", ranking.getDomain());
                    map.put("rank", ranking.getRankValue());
                    return map;
                })
                .collect(java.util.stream.Collectors.toList());
    }
}
