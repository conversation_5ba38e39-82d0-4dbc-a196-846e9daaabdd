package com.geeksec.task.application.service;

import com.geeksec.task.application.service.impl.MinioFileServiceImpl;
import com.geeksec.task.config.MinioConfig;
import com.geeksec.task.model.vo.FileTreeNodeVo;
import io.minio.MinioClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * MinIO 文件服务测试类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@ExtendWith(MockitoExtension.class)
class MinioFileServiceTest {

    @Mock
    private MinioClient minioClient;

    @Mock
    private MinioConfig minioConfig;

    private MinioFileServiceImpl minioFileService;

    @BeforeEach
    void setUp() {
        minioFileService = new MinioFileServiceImpl(minioClient, minioConfig);
        
        // 设置默认配置
        when(minioConfig.getPcapBucket()).thenReturn("test-pcap-bucket");
        when(minioConfig.getDefaultBucket()).thenReturn("test-default-bucket");
    }

    @Test
    void testListServerPath_EmptyDirectory() {
        // Given
        String directoryPath = "";
        
        // When & Then
        assertDoesNotThrow(() -> {
            List<FileTreeNodeVo> result = minioFileService.listServerPath(directoryPath);
            assertNotNull(result);
        });
    }

    @Test
    void testCheckFilePaths_EmptyList() {
        // Given
        List<String> filePathList = List.of();
        
        // When & Then
        assertDoesNotThrow(() -> {
            MinioFileService.CheckFilePathsResult result = minioFileService.checkFilePaths(filePathList);
            assertNotNull(result);
            assertTrue(result.isStatus());
            assertTrue(result.getNonExistFiles().isEmpty());
        });
    }

    @Test
    void testDeletePcapFiles_ValidTaskId() {
        // Given
        String taskId = "test-task-123";
        
        // When & Then
        assertDoesNotThrow(() -> {
            MinioFileService.DeleteFilesResult result = minioFileService.deletePcapFiles(taskId);
            assertNotNull(result);
        });
    }

    @Test
    void testUploadFile_ValidData() {
        // Given
        String filePath = "test/sample.pcap";
        byte[] content = "test content".getBytes();
        
        // When & Then
        assertDoesNotThrow(() -> {
            MinioFileService.UploadResult result = minioFileService.uploadFile(filePath, content);
            assertNotNull(result);
        });
    }

    @Test
    void testDownloadFile_ValidPath() {
        // Given
        String filePath = "test/sample.pcap";
        
        // When & Then
        assertThrows(RuntimeException.class, () -> {
            // 这个测试预期会抛出异常，因为我们没有真实的MinIO连接
            minioFileService.downloadFile(filePath);
        });
    }
}
