package com.geeksec.task.application.service;

import com.geeksec.task.application.service.impl.OfflineTaskBatchFileServiceImpl;
import com.geeksec.task.model.dto.OfflineFilePathDto;
import com.geeksec.task.model.entity.OfflineTaskBatchFile;
import com.geeksec.task.model.entity.TaskBatch;
import com.geeksec.task.repository.OfflineTaskBatchFileRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 离线任务批次文件服务测试类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@ExtendWith(MockitoExtension.class)
class OfflineTaskBatchFileServiceTest {

    @Mock
    private OfflineTaskBatchFileRepository offlineTaskBatchFileRepository;

    private OfflineTaskBatchFileServiceImpl offlineTaskBatchFileService;

    @BeforeEach
    void setUp() {
        offlineTaskBatchFileService = new OfflineTaskBatchFileServiceImpl(offlineTaskBatchFileRepository);
    }

    @Test
    void testAddBatchFiles() {
        // Given
        TaskBatch taskBatch = new TaskBatch();
        taskBatch.setTaskId(1);
        taskBatch.setBatchId(1);

        List<OfflineFilePathDto> filePathList = Arrays.asList(
            new OfflineFilePathDto("/local/path1.pcap", "/server/path1.pcap", 1024L, 1),
            new OfflineFilePathDto("/local/path2.pcap", "/server/path2.pcap", 2048L, 1)
        );

        when(offlineTaskBatchFileRepository.insert(any(OfflineTaskBatchFile.class))).thenReturn(1);

        // When
        assertDoesNotThrow(() -> {
            offlineTaskBatchFileService.addBatchFiles(taskBatch, filePathList);
        });

        // Then
        verify(offlineTaskBatchFileRepository, times(2)).insert(any(OfflineTaskBatchFile.class));
    }

    @Test
    void testAddBatchFiles_EmptyList() {
        // Given
        TaskBatch taskBatch = new TaskBatch();
        taskBatch.setTaskId(1);
        taskBatch.setBatchId(1);

        List<OfflineFilePathDto> filePathList = Arrays.asList();

        // When
        assertDoesNotThrow(() -> {
            offlineTaskBatchFileService.addBatchFiles(taskBatch, filePathList);
        });

        // Then
        verify(offlineTaskBatchFileRepository, never()).insert(any(OfflineTaskBatchFile.class));
    }

    @Test
    void testGetBatchFiles() {
        // Given
        Integer batchId = 1;
        List<OfflineTaskBatchFile> expectedFiles = Arrays.asList(
            new OfflineTaskBatchFile(),
            new OfflineTaskBatchFile()
        );

        when(offlineTaskBatchFileRepository.findByBatchId(batchId)).thenReturn(expectedFiles);

        // When
        List<OfflineTaskBatchFile> result = offlineTaskBatchFileService.getBatchFiles(batchId);

        // Then
        assertEquals(expectedFiles, result);
        verify(offlineTaskBatchFileRepository).findByBatchId(batchId);
    }

    @Test
    void testDeleteBatchFiles() {
        // Given
        Integer batchId = 1;
        when(offlineTaskBatchFileRepository.deleteByBatchId(batchId)).thenReturn(2);

        // When
        assertDoesNotThrow(() -> {
            offlineTaskBatchFileService.deleteBatchFiles(batchId);
        });

        // Then
        verify(offlineTaskBatchFileRepository).deleteByBatchId(batchId);
    }

    @Test
    void testUpdateFileStatus() {
        // Given
        Integer fileId = 1;
        Integer status = 2;
        when(offlineTaskBatchFileRepository.updateStatus(fileId, status)).thenReturn(1);

        // When
        assertDoesNotThrow(() -> {
            offlineTaskBatchFileService.updateFileStatus(fileId, status);
        });

        // Then
        verify(offlineTaskBatchFileRepository).updateStatus(fileId, status);
    }

    @Test
    void testGetBatchFileStatistics() {
        // Given
        Integer batchId = 1;
        when(offlineTaskBatchFileRepository.countByBatchId(batchId)).thenReturn(10L);
        when(offlineTaskBatchFileRepository.countByBatchIdAndStatus(batchId, 0)).thenReturn(3L);
        when(offlineTaskBatchFileRepository.countByBatchIdAndStatus(batchId, 1)).thenReturn(2L);
        when(offlineTaskBatchFileRepository.countByBatchIdAndStatus(batchId, 2)).thenReturn(4L);
        when(offlineTaskBatchFileRepository.countByBatchIdAndStatus(batchId, 3)).thenReturn(1L);

        // When
        OfflineTaskBatchFileService.BatchFileStatistics statistics = 
                offlineTaskBatchFileService.getBatchFileStatistics(batchId);

        // Then
        assertEquals(10L, statistics.getTotalFiles());
        assertEquals(3L, statistics.getPendingFiles());
        assertEquals(2L, statistics.getProcessingFiles());
        assertEquals(4L, statistics.getCompletedFiles());
        assertEquals(1L, statistics.getFailedFiles());
        assertEquals(40.0, statistics.getProgressPercentage(), 0.01);
        assertFalse(statistics.isAllCompleted());
        assertTrue(statistics.hasFailedFiles());
    }

    @Test
    void testStartProcessFile() {
        // Given
        Integer fileId = 1;
        OfflineTaskBatchFile file = new OfflineTaskBatchFile();
        file.setId(fileId);
        file.setStatus(0);

        when(offlineTaskBatchFileRepository.selectById(fileId)).thenReturn(file);
        when(offlineTaskBatchFileRepository.update(any(OfflineTaskBatchFile.class))).thenReturn(1);

        // When
        assertDoesNotThrow(() -> {
            offlineTaskBatchFileService.startProcessFile(fileId);
        });

        // Then
        verify(offlineTaskBatchFileRepository).selectById(fileId);
        verify(offlineTaskBatchFileRepository).update(any(OfflineTaskBatchFile.class));
    }

    @Test
    void testFinishProcessFile_Success() {
        // Given
        Integer fileId = 1;
        OfflineTaskBatchFile file = new OfflineTaskBatchFile();
        file.setId(fileId);
        file.setStatus(1);

        when(offlineTaskBatchFileRepository.selectById(fileId)).thenReturn(file);
        when(offlineTaskBatchFileRepository.update(any(OfflineTaskBatchFile.class))).thenReturn(1);

        // When
        assertDoesNotThrow(() -> {
            offlineTaskBatchFileService.finishProcessFile(fileId, true, null);
        });

        // Then
        verify(offlineTaskBatchFileRepository).selectById(fileId);
        verify(offlineTaskBatchFileRepository).update(any(OfflineTaskBatchFile.class));
    }

    @Test
    void testFinishProcessFile_Failed() {
        // Given
        Integer fileId = 1;
        String errorMessage = "处理失败";
        OfflineTaskBatchFile file = new OfflineTaskBatchFile();
        file.setId(fileId);
        file.setStatus(1);

        when(offlineTaskBatchFileRepository.selectById(fileId)).thenReturn(file);
        when(offlineTaskBatchFileRepository.update(any(OfflineTaskBatchFile.class))).thenReturn(1);

        // When
        assertDoesNotThrow(() -> {
            offlineTaskBatchFileService.finishProcessFile(fileId, false, errorMessage);
        });

        // Then
        verify(offlineTaskBatchFileRepository).selectById(fileId);
        verify(offlineTaskBatchFileRepository).update(any(OfflineTaskBatchFile.class));
    }
}
