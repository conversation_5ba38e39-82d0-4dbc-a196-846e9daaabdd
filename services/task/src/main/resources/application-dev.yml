# Task Service 开发环境配置
# 继承通用开发环境配置

spring:
  profiles:
    active: dev
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8086}
  servlet:
    context-path: /task

# 任务服务特定配置
task:
  # 开发环境启用定时任务调度
  enabled:
    task_scheduled: true
  
  # 开发环境文件路径配置
  file:
    tmp-path: ${TASK_TMP_PATH:./dev-data/tmp/}
    pcap-download-path: ${TASK_PCAP_DOWNLOAD_PATH:./dev-data/download/pcap/}
    session-path: ${TASK_SESSION_PATH:./dev-data/download/logs/}
    template-path: ${TASK_TEMPLATE_PATH:./dev-data/template/}
    pcap-path: ${TASK_PCAP_PATH:./dev-data/import/pcap/}
    server-pcap-path: ${TASK_SERVER_PCAP_PATH:./dev-data/pcapfiles/}
  
  # 开发环境外部服务URL配置
  external:
    list-files-url: ${IMPORT_SERVICE_URL:http://localhost:5000}/offline/listServerPath
    check-files-url: ${IMPORT_SERVICE_URL:http://localhost:5000}/offline/checkFilePaths
    delete-batch-pcaps: ${IMPORT_SERVICE_URL:http://localhost:5000}/offline/deletePcapFiles
    stop-batch-offline-thd: ${IMPORT_SERVICE_URL:http://localhost:5000}/offline/stopOfflineThd
  
  # 开发环境调度配置
  scheduler:
    pool-size: 5
    thread-name-prefix: task-dev-
    await-termination-seconds: 60
    wait-for-tasks-to-complete-on-shutdown: true
  
  # 开发环境任务配置
  jobs:
    # 数据清理任务
    cleanup:
      enabled: true
      cron: "0 0 2 * * ?"  # 每天凌晨2点执行
      retention-days: 7  # 开发环境保留7天
    # 数据同步任务
    sync:
      enabled: true
      cron: "0 */10 * * * ?"  # 每10分钟执行一次
      batch-size: 100

# 日志配置
logging:
  level:
    com.geeksec.task: DEBUG
    org.springframework.scheduling: DEBUG
