package com.geeksec.task.model.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 离线分析任务数据传输对象
 * 用于传输离线分析任务（读取pcap文件进行流量分析）的相关信息
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
public class OfflineTaskDto {

    /**
     * 任务ID（更新时使用）
     */
    private Integer taskId;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    @Size(max = 100, message = "任务名称长度不能超过100个字符")
    private String taskName;

    /**
     * 任务描述
     */
    @Size(max = 500, message = "任务描述长度不能超过500个字符")
    private String taskDescription;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * 任务备注
     */
    @Size(max = 1000, message = "任务备注长度不能超过1000个字符")
    private String taskRemark;

    /**
     * 网络流量信息
     */
    private String netFlow;
}
