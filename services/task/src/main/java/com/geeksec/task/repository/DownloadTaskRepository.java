package com.geeksec.task.repository;

import com.geeksec.task.model.entity.DownloadTask;
import com.mybatisflex.core.service.IService;

import java.util.List;

/**
 * 下载任务仓储接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface DownloadTaskRepository extends IService<DownloadTask> {

    /**
     * 根据用户ID查询下载任务列表
     * 
     * @param userId 用户ID
     * @return 下载任务列表
     */
    List<DownloadTask> findByUserId(Integer userId);

    /**
     * 根据任务状态查询下载任务列表
     * 
     * @param state 任务状态
     * @return 下载任务列表
     */
    List<DownloadTask> findByState(Integer state);

    /**
     * 根据任务ID获取PCAP文件路径
     * 
     * @param taskId 任务ID
     * @return PCAP文件路径
     */
    String getPcapPath(Integer taskId);

    /**
     * 根据用户ID和状态查询下载任务
     * 
     * @param userId 用户ID
     * @param state 任务状态
     * @return 下载任务列表
     */
    List<DownloadTask> findByUserIdAndState(Integer userId, Integer state);

    /**
     * 统计用户的下载任务数量
     * 
     * @param userId 用户ID
     * @return 任务数量
     */
    Long countByUserId(Integer userId);

    /**
     * 根据任务类型查询下载任务
     * 
     * @param taskType 任务类型
     * @return 下载任务列表
     */
    List<DownloadTask> findByTaskType(Integer taskType);
}
