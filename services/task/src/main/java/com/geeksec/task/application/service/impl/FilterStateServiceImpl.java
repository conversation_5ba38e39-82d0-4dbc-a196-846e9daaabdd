package com.geeksec.task.application.service.impl;

import com.geeksec.task.application.service.FilterStateService;
import com.geeksec.task.model.entity.FilterState;
import com.geeksec.task.repository.FilterStateRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 过滤状态服务实现类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FilterStateServiceImpl implements FilterStateService {

    private final FilterStateRepository filterStateRepository;
    // TODO: 注入探针服务客户端
    // private final ProbeServiceClient probeServiceClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createFilterState(Integer taskId, Integer state, Integer createdBy) {
        try {
            // 检查是否已存在
            FilterState existingState = filterStateRepository.findByTaskId(taskId);
            if (existingState != null) {
                log.warn("任务 {} 的过滤状态已存在，跳过创建", taskId);
                return;
            }

            int result = filterStateRepository.createFilterState(taskId, state, createdBy);
            if (result > 0) {
                log.info("创建过滤状态成功，任务ID: {}, 状态: {}", taskId, state);
            } else {
                log.error("创建过滤状态失败，任务ID: {}", taskId);
                throw new RuntimeException("创建过滤状态失败");
            }
        } catch (Exception e) {
            log.error("创建过滤状态失败，任务ID: {}", taskId, e);
            throw new RuntimeException("创建过滤状态失败: " + e.getMessage(), e);
        }
    }

    @Override
    public FilterState getFilterState(Integer taskId) {
        try {
            FilterState filterState = filterStateRepository.findByTaskId(taskId);
            log.debug("查询过滤状态，任务ID: {}, 结果: {}", taskId, filterState != null ? "存在" : "不存在");
            return filterState;
        } catch (Exception e) {
            log.error("查询过滤状态失败，任务ID: {}", taskId, e);
            throw new RuntimeException("查询过滤状态失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFilterState(Integer taskId, Integer state) {
        try {
            int result = filterStateRepository.updateState(taskId, state);
            if (result > 0) {
                log.info("更新过滤状态成功，任务ID: {}, 新状态: {}", taskId, state);
            } else {
                log.warn("更新过滤状态失败，任务ID: {} 不存在", taskId);
            }
        } catch (Exception e) {
            log.error("更新过滤状态失败，任务ID: {}, 状态: {}", taskId, state, e);
            throw new RuntimeException("更新过滤状态失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFilterConfig(Integer taskId, String config) {
        try {
            int result = filterStateRepository.updateConfig(taskId, config);
            if (result > 0) {
                log.info("更新过滤配置成功，任务ID: {}", taskId);
            } else {
                log.warn("更新过滤配置失败，任务ID: {} 不存在", taskId);
            }
        } catch (Exception e) {
            log.error("更新过滤配置失败，任务ID: {}", taskId, e);
            throw new RuntimeException("更新过滤配置失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFilterState(Integer taskId) {
        try {
            int result = filterStateRepository.deleteByTaskId(taskId);
            if (result > 0) {
                log.info("删除过滤状态成功，任务ID: {}", taskId);
            } else {
                log.warn("删除过滤状态失败，任务ID: {} 不存在", taskId);
            }
        } catch (Exception e) {
            log.error("删除过滤状态失败，任务ID: {}", taskId, e);
            throw new RuntimeException("删除过滤状态失败: " + e.getMessage(), e);
        }
    }

    @Override
    public SyncResult syncFilterStateToProbe(Integer taskId) {
        try {
            FilterState filterState = filterStateRepository.findByTaskId(taskId);
            if (filterState == null) {
                return new SyncResult(taskId, false, "过滤状态不存在");
            }

            // TODO: 实现探针同步逻辑
            // 这里需要调用探针服务的API来同步过滤状态
            /*
            try {
                ProbeFilterConfigRequest request = new ProbeFilterConfigRequest();
                request.setTaskId(taskId);
                request.setConfig(filterState.getConfig());
                request.setState(filterState.getState());
                
                ProbeResponse response = probeServiceClient.syncFilterConfig(request);
                
                if (response.isSuccess()) {
                    updateSyncStatus(taskId, 1, null); // 同步成功
                    return new SyncResult(taskId, true, "同步成功");
                } else {
                    updateSyncStatus(taskId, 2, response.getErrorMessage()); // 同步失败
                    return new SyncResult(taskId, false, "同步失败", response.getErrorMessage());
                }
            } catch (Exception e) {
                updateSyncStatus(taskId, 2, e.getMessage()); // 同步失败
                return new SyncResult(taskId, false, "同步异常", e.getMessage());
            }
            */

            // 临时实现：标记为同步成功
            updateSyncStatus(taskId, 1, null);
            log.info("过滤状态同步成功（临时实现），任务ID: {}", taskId);
            return new SyncResult(taskId, true, "同步成功（临时实现）");

        } catch (Exception e) {
            log.error("同步过滤状态失败，任务ID: {}", taskId, e);
            return new SyncResult(taskId, false, "同步异常", e.getMessage());
        }
    }

    @Override
    public List<SyncResult> batchSyncFilterStates() {
        List<SyncResult> results = new ArrayList<>();
        
        try {
            List<FilterState> needSyncStates = filterStateRepository.findNeedSync();
            log.info("开始批量同步过滤状态，待同步数量: {}", needSyncStates.size());

            for (FilterState filterState : needSyncStates) {
                SyncResult result = syncFilterStateToProbe(filterState.getTaskId());
                results.add(result);
            }

            long successCount = results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
            log.info("批量同步过滤状态完成，总数: {}, 成功: {}, 失败: {}", 
                    results.size(), successCount, results.size() - successCount);

        } catch (Exception e) {
            log.error("批量同步过滤状态失败", e);
        }

        return results;
    }

    @Override
    public List<FilterState> getFilterStatesByState(Integer state) {
        try {
            List<FilterState> filterStates = filterStateRepository.findByState(state);
            log.debug("根据状态查询过滤状态，状态: {}, 数量: {}", state, filterStates.size());
            return filterStates;
        } catch (Exception e) {
            log.error("根据状态查询过滤状态失败，状态: {}", state, e);
            throw new RuntimeException("根据状态查询过滤状态失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<FilterState> getNeedSyncFilterStates() {
        try {
            List<FilterState> needSyncStates = filterStateRepository.findNeedSync();
            log.debug("查询需要同步的过滤状态，数量: {}", needSyncStates.size());
            return needSyncStates;
        } catch (Exception e) {
            log.error("查询需要同步的过滤状态失败", e);
            throw new RuntimeException("查询需要同步的过滤状态失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSyncStatus(Integer taskId, Integer syncStatus, String syncError) {
        try {
            int result = filterStateRepository.updateSyncStatus(taskId, syncStatus, syncError);
            if (result > 0) {
                log.debug("更新同步状态成功，任务ID: {}, 同步状态: {}", taskId, syncStatus);
            } else {
                log.warn("更新同步状态失败，任务ID: {} 不存在", taskId);
            }
        } catch (Exception e) {
            log.error("更新同步状态失败，任务ID: {}, 同步状态: {}", taskId, syncStatus, e);
            throw new RuntimeException("更新同步状态失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean existsFilterState(Integer taskId) {
        try {
            FilterState filterState = filterStateRepository.findByTaskId(taskId);
            boolean exists = filterState != null;
            log.debug("检查过滤状态是否存在，任务ID: {}, 结果: {}", taskId, exists);
            return exists;
        } catch (Exception e) {
            log.error("检查过滤状态是否存在失败，任务ID: {}", taskId, e);
            return false;
        }
    }
}
