package com.geeksec.task.application.service.impl;

import com.geeksec.task.application.service.MinioFileService;
import com.geeksec.task.config.MinioConfig;
import com.geeksec.task.model.vo.FileTreeNodeVo;
import io.minio.*;
import io.minio.messages.Item;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * MinIO 文件管理服务实现类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MinioFileServiceImpl implements MinioFileService {

    private final MinioClient minioClient;
    private final MinioConfig minioConfig;

    @Override
    public List<FileTreeNodeVo> listServerPath(String directoryPath) {
        List<FileTreeNodeVo> result = new ArrayList<>();
        
        try {
            // 确保存储桶存在
            ensureBucketExists(minioConfig.getPcapBucket());
            
            // 构建前缀路径
            String prefix = buildPrefix(directoryPath);
            
            // 列出对象
            Iterable<Result<Item>> objects = minioClient.listObjects(
                ListObjectsArgs.builder()
                    .bucket(minioConfig.getPcapBucket())
                    .prefix(prefix)
                    .recursive(false) // 不递归，只列出当前层级
                    .build()
            );
            
            for (Result<Item> objectResult : objects) {
                Item item = objectResult.get();
                String objectName = item.objectName();
                
                // 跳过当前目录本身
                if (objectName.equals(prefix)) {
                    continue;
                }
                
                // 提取相对路径和文件名
                String relativePath = objectName.startsWith(prefix) ? 
                    objectName.substring(prefix.length()) : objectName;
                String fileName = extractFileName(relativePath);
                
                // 判断是否为目录
                boolean isDirectory = item.isDir() || objectName.endsWith("/");
                
                // 过滤 PCAP 文件类型
                if (!isDirectory && !isPcapFile(fileName)) {
                    continue;
                }
                
                FileTreeNodeVo node = new FileTreeNodeVo(
                    relativePath,
                    fileName,
                    isDirectory, // disabled 表示是否为文件夹
                    isDirectory ? null : item.size(),
                    isDirectory ? null : item.lastModified().toEpochSecond() * 1000
                );
                
                result.add(node);
            }
            
            log.info("列出文件路径成功，目录: {}, 文件数量: {}", directoryPath, result.size());
            
        } catch (Exception e) {
            log.error("列出文件路径失败，目录: {}", directoryPath, e);
            throw new RuntimeException("列出文件路径失败: " + e.getMessage(), e);
        }
        
        return result;
    }

    @Override
    public CheckFilePathsResult checkFilePaths(List<String> filePathList) {
        List<String> nonExistFiles = new ArrayList<>();
        
        try {
            // 确保存储桶存在
            ensureBucketExists(minioConfig.getPcapBucket());
            
            for (String filePath : filePathList) {
                try {
                    // 检查对象是否存在
                    minioClient.statObject(
                        StatObjectArgs.builder()
                            .bucket(minioConfig.getPcapBucket())
                            .object(filePath)
                            .build()
                    );
                } catch (Exception e) {
                    // 对象不存在
                    nonExistFiles.add(filePath);
                }
            }
            
            log.info("检查文件路径完成，总数: {}, 不存在: {}", filePathList.size(), nonExistFiles.size());
            
        } catch (Exception e) {
            log.error("检查文件路径失败", e);
            throw new RuntimeException("检查文件路径失败: " + e.getMessage(), e);
        }
        
        return new CheckFilePathsResult(true, nonExistFiles);
    }

    @Override
    public DeleteFilesResult deletePcapFiles(String taskId) {
        try {
            // 确保存储桶存在
            ensureBucketExists(minioConfig.getPcapBucket());
            
            // 构建任务目录前缀
            String prefix = "tasks/" + taskId + "/";
            
            // 列出要删除的对象
            List<String> objectsToDelete = new ArrayList<>();
            Iterable<Result<Item>> objects = minioClient.listObjects(
                ListObjectsArgs.builder()
                    .bucket(minioConfig.getPcapBucket())
                    .prefix(prefix)
                    .recursive(true)
                    .build()
            );
            
            for (Result<Item> objectResult : objects) {
                Item item = objectResult.get();
                if (isPcapFile(item.objectName())) {
                    objectsToDelete.add(item.objectName());
                }
            }
            
            // 删除对象
            for (String objectName : objectsToDelete) {
                minioClient.removeObject(
                    RemoveObjectArgs.builder()
                        .bucket(minioConfig.getPcapBucket())
                        .object(objectName)
                        .build()
                );
            }
            
            log.info("删除任务 PCAP 文件成功，任务ID: {}, 删除文件数: {}", taskId, objectsToDelete.size());
            return new DeleteFilesResult(true, "删除成功，共删除 " + objectsToDelete.size() + " 个文件");
            
        } catch (Exception e) {
            log.error("删除任务 PCAP 文件失败，任务ID: {}", taskId, e);
            return new DeleteFilesResult(false, "删除失败: " + e.getMessage());
        }
    }

    @Override
    public UploadResult uploadFile(String filePath, byte[] content) {
        try {
            // 确保存储桶存在
            ensureBucketExists(minioConfig.getPcapBucket());
            
            // 上传文件
            try (InputStream inputStream = new ByteArrayInputStream(content)) {
                minioClient.putObject(
                    PutObjectArgs.builder()
                        .bucket(minioConfig.getPcapBucket())
                        .object(filePath)
                        .stream(inputStream, content.length, -1)
                        .build()
                );
            }
            
            log.info("上传文件成功，路径: {}, 大小: {} 字节", filePath, content.length);
            return new UploadResult(true, "上传成功", filePath);
            
        } catch (Exception e) {
            log.error("上传文件失败，路径: {}", filePath, e);
            return new UploadResult(false, "上传失败: " + e.getMessage(), filePath);
        }
    }

    @Override
    public byte[] downloadFile(String filePath) {
        try {
            // 确保存储桶存在
            ensureBucketExists(minioConfig.getPcapBucket());
            
            // 下载文件
            try (InputStream inputStream = minioClient.getObject(
                GetObjectArgs.builder()
                    .bucket(minioConfig.getPcapBucket())
                    .object(filePath)
                    .build()
            )) {
                byte[] content = inputStream.readAllBytes();
                log.info("下载文件成功，路径: {}, 大小: {} 字节", filePath, content.length);
                return content;
            }
            
        } catch (Exception e) {
            log.error("下载文件失败，路径: {}", filePath, e);
            throw new RuntimeException("下载文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 确保存储桶存在
     * 
     * @param bucketName 存储桶名称
     */
    private void ensureBucketExists(String bucketName) {
        try {
            boolean exists = minioClient.bucketExists(
                BucketExistsArgs.builder()
                    .bucket(bucketName)
                    .build()
            );
            
            if (!exists) {
                minioClient.makeBucket(
                    MakeBucketArgs.builder()
                        .bucket(bucketName)
                        .build()
                );
                log.info("创建存储桶成功: {}", bucketName);
            }
        } catch (Exception e) {
            log.error("检查/创建存储桶失败: {}", bucketName, e);
            throw new RuntimeException("检查/创建存储桶失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建前缀路径
     * 
     * @param directoryPath 目录路径
     * @return 前缀路径
     */
    private String buildPrefix(String directoryPath) {
        if (directoryPath == null || directoryPath.trim().isEmpty()) {
            return "";
        }
        
        String prefix = directoryPath.trim();
        if (!prefix.endsWith("/")) {
            prefix += "/";
        }
        
        return prefix;
    }

    /**
     * 提取文件名
     * 
     * @param path 路径
     * @return 文件名
     */
    private String extractFileName(String path) {
        if (path == null || path.isEmpty()) {
            return "";
        }
        
        // 移除末尾的斜杠
        String cleanPath = path.endsWith("/") ? path.substring(0, path.length() - 1) : path;
        
        int lastSlashIndex = cleanPath.lastIndexOf('/');
        return lastSlashIndex >= 0 ? cleanPath.substring(lastSlashIndex + 1) : cleanPath;
    }

    /**
     * 判断是否为 PCAP 文件
     * 
     * @param fileName 文件名
     * @return 是否为 PCAP 文件
     */
    private boolean isPcapFile(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }
        
        String lowerFileName = fileName.toLowerCase();
        return lowerFileName.endsWith(".pcap") || 
               lowerFileName.endsWith(".cap") || 
               lowerFileName.endsWith(".pcapng");
    }
}
