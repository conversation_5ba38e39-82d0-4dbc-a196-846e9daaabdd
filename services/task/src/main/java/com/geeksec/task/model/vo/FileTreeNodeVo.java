package com.geeksec.task.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 文件路径树视图对象
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
public class FileTreeNodeVo {

    /**
     * 文件路径
     */
    @JsonProperty("file_path")
    private String filePath;

    /**
     * 文件名
     */
    @JsonProperty("file_name")
    private String fileName;

    /**
     * 文件类型（false-文件；true-文件夹）
     */
    @JsonProperty("disabled")
    private Boolean disabled;

    /**
     * 文件大小（字节）
     */
    @JsonProperty("file_size")
    private Long fileSize;

    /**
     * 最后修改时间
     */
    @JsonProperty("last_modified")
    private Long lastModified;

    /**
     * 构造函数
     */
    public FileTreeNodeVo() {}

    /**
     * 构造函数
     * 
     * @param filePath 文件路径
     * @param fileName 文件名
     * @param disabled 是否为文件夹
     */
    public FileTreeNodeVo(String filePath, String fileName, Boolean disabled) {
        this.filePath = filePath;
        this.fileName = fileName;
        this.disabled = disabled;
    }

    /**
     * 构造函数
     * 
     * @param filePath 文件路径
     * @param fileName 文件名
     * @param disabled 是否为文件夹
     * @param fileSize 文件大小
     * @param lastModified 最后修改时间
     */
    public FileTreeNodeVo(String filePath, String fileName, Boolean disabled, Long fileSize, Long lastModified) {
        this.filePath = filePath;
        this.fileName = fileName;
        this.disabled = disabled;
        this.fileSize = fileSize;
        this.lastModified = lastModified;
    }
}
