package com.geeksec.task.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 分析任务实体类
 * 用于表示流量分析任务，包括实时分析任务（从NIC采集流量数据）和离线分析任务（读取pcap文件）
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Table("tb_task_analysis")
public class AnalysisTask implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id(keyType = KeyType.Auto)
    @Column("id")
    private Integer id;

    /**
     * 任务ID
     */
    @Column("task_id")
    private Integer taskId;

    /**
     * 任务名称
     */
    @Column("task_name")
    private String taskName;

    /**
     * 网络流量信息
     */
    @Column("netflow")
    private String netFlow;

    /**
     * 任务备注
     */
    @Column("task_remark")
    private String taskRemark;

    /**
     * 任务状态
     */
    @Column("task_state")
    private Integer taskState;

    /**
     * 最后暂停时间
     */
    @Column("last_suspend_time")
    private LocalDateTime lastSuspendTime;

    /**
     * 暂停次数
     */
    @Column("suspend_times")
    private Long suspendTimes;

    /**
     * 用户ID
     */
    @Column("user_id")
    private Integer userId;

    /**
     * 分析任务类型 (1: 实时分析任务-从NIC采集流量数据, 2: 离线分析任务-读取pcap文件)
     */
    @Column("task_type")
    private Integer taskType;

    /**
     * 创建时间
     */
    @Column("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除 (0: 未删除, 1: 已删除)
     */
    @Column("deleted")
    private Integer deleted;
}
