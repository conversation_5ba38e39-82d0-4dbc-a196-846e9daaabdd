package com.geeksec.task.repository;

import com.geeksec.task.model.entity.Task;
import com.mybatisflex.core.service.IService;

import java.util.List;

/**
 * 任务仓储接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface TaskRepository extends IService<Task> {

    /**
     * 根据用户ID查询任务列表
     * 
     * @param userId 用户ID
     * @return 任务列表
     */
    List<Task> findByUserId(Integer userId);

    /**
     * 根据任务状态查询任务列表
     * 
     * @param taskStatus 任务状态
     * @return 任务列表
     */
    List<Task> findByTaskStatus(String taskStatus);

    /**
     * 根据任务类型查询任务列表
     * 
     * @param taskType 任务类型
     * @return 任务列表
     */
    List<Task> findByTaskType(String taskType);

    /**
     * 查询用户的最新任务
     * 
     * @param userId 用户ID
     * @return 最新任务
     */
    Task findLatestByUserId(Integer userId);

    /**
     * 统计用户的任务数量
     * 
     * @param userId 用户ID
     * @return 任务数量
     */
    Long countByUserId(Integer userId);

    /**
     * 根据任务名称查询任务
     * 
     * @param taskName 任务名称
     * @return 任务
     */
    Task findByTaskName(String taskName);

    /**
     * 检查任务名称是否存在
     * 
     * @param taskName 任务名称
     * @param excludeId 排除的任务ID
     * @return 是否存在
     */
    boolean existsByTaskName(String taskName, Integer excludeId);
}
