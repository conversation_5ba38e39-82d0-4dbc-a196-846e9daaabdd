package com.geeksec.task.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 下载任务实体类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Table("tb_download_task")
public class DownloadTask implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id(keyType = KeyType.Auto)
    @Column("id")
    private Integer id;

    /**
     * 创建者用户ID
     */
    @Column("created_by")
    private Integer createdBy;

    /**
     * 文件路径
     */
    @Column("path")
    private String path;

    /**
     * ES 下载检索条件
     */
    @Column("query")
    private String query;

    /**
     * 前端展示字段
     */
    @Column("show_query")
    private String showQuery;

    /**
     * 下载类型 (0: 部分下载, 1: 全量下载)
     */
    @Column("type")
    private Integer type;

    /**
     * 会话列表信息
     */
    @Column("session_id")
    private String sessionId;

    /**
     * 任务状态 (0: 准备数据, 1: 可下载, 2: 重新下载, 3: 已删除, 4: 待删除)
     */
    @Column("state")
    private Integer state;

    /**
     * 创建时间
     */
    @Column("created_at")
    private Long createdAt;

    /**
     * 数据存储结束时间
     */
    @Column("end_time")
    private Long endTime;

    /**
     * 数据状态 (0: 删除, 1: 存在)
     */
    @Column("status")
    private Integer status;

    /**
     * 关联的任务ID数组
     */
    @Column("task_id")
    private String taskId;

    /**
     * 下载次数
     */
    @Column("download_count")
    private Integer downloadCount;

    /**
     * 删除时间
     */
    @Column("delete_time")
    private LocalDateTime deleteTime;

    /**
     * 更新时间
     */
    @Column("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 创建时间（标准格式）
     */
    @Column("created_at")
    private LocalDateTime createdAtStandard;

    /**
     * 任务类型
     */
    @Column("task_type")
    private Integer taskType;

    /**
     * 错误信息
     */
    @Column("error_msg")
    private String errorMsg;
}
