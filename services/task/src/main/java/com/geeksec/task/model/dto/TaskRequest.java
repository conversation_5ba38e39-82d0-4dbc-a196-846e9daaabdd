package com.geeksec.task.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 任务请求DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Schema(description = "任务创建/更新请求")
public class TaskRequest {
    
    @Schema(description = "任务ID（更新时必填）", example = "1")
    private Integer taskId;
    
    @NotBlank(message = "任务名称不能为空")
    @Schema(description = "任务名称", example = "网络流量分析任务", required = true)
    private String taskName;
    
    @Schema(description = "任务描述", example = "分析企业内网流量")
    private String taskDescription;
    
    @NotBlank(message = "任务类型不能为空")
    @Schema(description = "任务类型", example = "ANALYSIS", required = true)
    private String taskType;
    
    @Schema(description = "任务优先级", example = "NORMAL")
    private String priority;
    
    @Schema(description = "计划开始时间", example = "2024-01-01T12:00:00")
    private LocalDateTime scheduledStartTime;
    
    @Schema(description = "计划结束时间", example = "2024-01-01T18:00:00")
    private LocalDateTime scheduledEndTime;
    
    @Schema(description = "任务配置参数")
    private Map<String, Object> taskConfig;
    
    @Schema(description = "Cron表达式（定时任务）", example = "0 0 2 * * ?")
    private String cronExpression;
    
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;
    
    @Schema(description = "重试次数", example = "3")
    private Integer retryCount;
    
    @Schema(description = "超时时间（秒）", example = "3600")
    private Integer timeoutSeconds;
    
    @NotNull(message = "用户ID不能为空")
    @Schema(description = "创建用户ID", example = "1", required = true)
    private Integer userId;
}
