package com.geeksec.task.infrastructure.mapper;

import com.geeksec.task.model.entity.DownloadTaskRegister;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 下载任务注册数据访问映射器
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Mapper
public interface DownloadTaskRegisterMapper extends BaseMapper<DownloadTaskRegister> {

    /**
     * 获取下一个待处理任务
     * 
     * @return 待处理任务
     */
    @Select("SELECT * FROM tb_download_task_register WHERE status = 1 AND type = 1 ORDER BY id ASC LIMIT 1")
    DownloadTaskRegister getNextPendingTask();
}
