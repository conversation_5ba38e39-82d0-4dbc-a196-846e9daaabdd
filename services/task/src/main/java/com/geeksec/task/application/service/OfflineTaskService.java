package com.geeksec.task.application.service;

import com.geeksec.task.model.condition.OfflineTaskQueryCondition;
import com.geeksec.task.model.dto.OfflineTaskDto;
import com.geeksec.task.model.dto.OfflineTaskDeleteDto;
import com.geeksec.task.model.vo.OfflineTaskVo;
import com.geeksec.task.model.vo.OfflineTaskPageVo;

import java.util.List;

/**
 * 离线分析任务应用服务接口
 * 用于管理离线分析任务（读取pcap文件进行流量分析）
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface OfflineTaskService {

    /**
     * 查询当前用户最新的离线分析任务
     *
     * @return 最新离线分析任务
     */
    OfflineTaskVo getLastTask();

    /**
     * 根据任务ID查询离线分析任务详情
     *
     * @param taskId 任务ID
     * @return 离线分析任务详情
     */
    OfflineTaskVo getTask(Integer taskId);

    /**
     * 分页查询离线分析任务列表
     *
     * @param condition 查询条件
     * @return 离线分析任务分页列表
     */
    TaskPageResult pageTask(OfflineTaskQueryCondition condition);

    /**
     * 创建离线分析任务
     *
     * @param dto 任务信息
     * @return 创建结果
     */
    TaskOperationResult addTask(OfflineTaskDto dto);

    /**
     * 更新离线分析任务
     *
     * @param dto 任务信息
     * @return 更新结果
     */
    TaskOperationResult updateTask(OfflineTaskDto dto);

    /**
     * 删除离线分析任务
     *
     * @param dto 删除参数
     * @return 删除结果
     */
    TaskOperationResult deleteTask(OfflineTaskDeleteDto dto);

    /**
     * 启动离线分析任务
     *
     * @param taskId 任务ID
     * @return 启动结果
     */
    TaskOperationResult startTask(Integer taskId);

    /**
     * 停止离线分析任务
     *
     * @param taskId 任务ID
     * @return 停止结果
     */
    TaskOperationResult stopTask(Integer taskId);

    /**
     * 任务操作结果
     */
    class TaskOperationResult {
        private boolean success;
        private String message;
        private Object data;

        public TaskOperationResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public TaskOperationResult(boolean success, String message, Object data) {
            this.success = success;
            this.message = message;
            this.data = data;
        }

        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public Object getData() { return data; }
    }

    /**
     * 任务分页结果
     */
    class TaskPageResult {
        private List<OfflineTaskPageVo> data;
        private long total;
        private int currentPage;
        private int pageSize;

        public TaskPageResult(List<OfflineTaskPageVo> data, long total, int currentPage, int pageSize) {
            this.data = data;
            this.total = total;
            this.currentPage = currentPage;
            this.pageSize = pageSize;
        }

        public List<OfflineTaskPageVo> getData() { return data; }
        public long getTotal() { return total; }
        public int getCurrentPage() { return currentPage; }
        public int getPageSize() { return pageSize; }
    }


}
