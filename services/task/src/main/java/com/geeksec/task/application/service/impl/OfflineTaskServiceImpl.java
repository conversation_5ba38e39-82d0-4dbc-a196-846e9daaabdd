package com.geeksec.task.application.service.impl;

import com.geeksec.task.application.service.OfflineTaskService;
import com.geeksec.task.application.service.MinioFileService;
import com.geeksec.task.application.service.OfflineTaskBatchFileService;
import com.geeksec.task.application.service.FilterStateService;
import com.geeksec.task.model.condition.OfflineTaskQueryCondition;
import com.geeksec.task.model.dto.OfflineTaskDto;
import com.geeksec.task.model.dto.OfflineTaskDeleteDto;
import com.geeksec.task.model.entity.AnalysisTask;
import com.geeksec.task.model.entity.TaskBatch;
import com.geeksec.task.model.vo.OfflineTaskVo;
import com.geeksec.task.model.vo.OfflineTaskPageVo;
import com.geeksec.task.model.vo.OfflineTaskBatchProgressVo;
import com.geeksec.task.repository.AnalysisTaskRepository;
import com.geeksec.task.repository.TaskBatchRepository;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 离线分析任务应用服务实现类
 * 用于管理离线分析任务（读取pcap文件进行流量分析）
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OfflineTaskServiceImpl implements OfflineTaskService {

    private final AnalysisTaskRepository analysisTaskRepository;
    private final TaskBatchRepository taskBatchRepository;
    private final MinioFileService minioFileService;
    private final OfflineTaskBatchFileService offlineTaskBatchFileService;
    private final FilterStateService filterStateService;

    @Override
    public OfflineTaskVo getLastTask() {
        // TODO: 获取当前用户ID
        Integer userId = getCurrentUserId();
        OfflineTaskVo offlineTaskVo = analysisTaskRepository.getLastTask(userId);
        if (offlineTaskVo != null) {
            enrichTaskWithBatchInfo(offlineTaskVo);
        }
        return offlineTaskVo;
    }

    @Override
    public OfflineTaskVo getTask(Integer taskId) {
        OfflineTaskVo offlineTaskVo = analysisTaskRepository.getTask(taskId);
        if (offlineTaskVo != null) {
            enrichTaskWithBatchInfo(offlineTaskVo);
        }
        return offlineTaskVo;
    }

    @Override
    public TaskPageResult pageTask(OfflineTaskQueryCondition condition) {
        Integer userId = getCurrentUserId();
        condition.setUserId(userId);

        // 使用 MyBatis-Flex 的分页功能
        List<OfflineTaskPageVo> offlineTaskPageVoList = analysisTaskRepository.pageTask(condition);

        // 模拟分页逻辑（实际应该在 Mapper 中实现）
        int currentPage = condition.getCurrentPage();
        int pageSize = condition.getPageSize();
        int startIndex = (currentPage - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, offlineTaskPageVoList.size());

        List<OfflineTaskPageVo> pagedList = offlineTaskPageVoList.subList(startIndex, endIndex);
        List<Integer> taskIdList = pagedList.stream()
                .map(OfflineTaskPageVo::getTaskId)
                .collect(Collectors.toList());

        if (!taskIdList.isEmpty()) {
            List<TaskBatch> taskBatchList = taskBatchRepository.listTaskBatchItem(taskIdList);
            Map<Integer, List<TaskBatch>> taskBatchListMap = taskBatchList.stream()
                    .collect(Collectors.groupingBy(TaskBatch::getTaskId));

            pagedList.forEach(offlineTaskPageVo -> {
                Integer taskId = offlineTaskPageVo.getTaskId();
                List<TaskBatch> taskBatches = taskBatchListMap.get(taskId);
                if (taskBatches != null) {
                    offlineTaskPageVo.setTaskStatus(calculateTaskStatus(taskBatches));
                    offlineTaskPageVo.setLastImportTime(calculateLastImportTime(
                            taskBatches.stream().map(TaskBatch::getEndTime).collect(Collectors.toList())));
                }
            });
        }

        return new TaskPageResult(pagedList, (long) offlineTaskPageVoList.size(), currentPage, pageSize);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskOperationResult addTask(OfflineTaskDto dto) {
        Integer userId = getCurrentUserId();

        // 检查任务名称是否已存在
        AnalysisTask existingTask = analysisTaskRepository.findByTaskName(dto.getTaskName());
        if (existingTask != null) {
            return new TaskOperationResult(false, "任务名称已存在，请重新输入");
        }

        AnalysisTask analysisTask = new AnalysisTask();
        analysisTask.setUserId(userId);
        // 设置为离线任务类型（2 表示离线任务）
        analysisTask.setTaskType(2);
        analysisTask.setTaskName(dto.getTaskName());
        analysisTask.setTaskRemark(dto.getTaskDescription());
        analysisTask.setCreateTime(LocalDateTime.now());

        analysisTaskRepository.save(analysisTask);
        analysisTaskRepository.updateTaskId(analysisTask.getId());

        // 创建过滤状态
        try {
            filterStateService.createFilterState(analysisTask.getTaskId(), 1, userId);
            log.info("创建过滤状态成功，任务ID: {}", analysisTask.getTaskId());
        } catch (Exception e) {
            log.error("创建过滤状态失败，任务ID: {}", analysisTask.getTaskId(), e);
        }

        return new TaskOperationResult(true, "任务创建成功", analysisTask.getTaskId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskOperationResult updateTask(OfflineTaskDto dto) {
        AnalysisTask analysisTask = new AnalysisTask();
        analysisTask.setTaskId(dto.getTaskId());
        analysisTask.setTaskName(dto.getTaskName());
        analysisTask.setTaskRemark(dto.getTaskDescription());
        analysisTask.setUpdateTime(LocalDateTime.now());

        boolean result = analysisTaskRepository.updateTask(analysisTask);
        return result ? new TaskOperationResult(true, "更新任务成功") : new TaskOperationResult(false, "更新任务失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskOperationResult deleteTask(OfflineTaskDeleteDto dto) {
        Integer count = taskBatchRepository.countIncompleteTask(dto.getTaskId());
        if (count > 0) {
            return new TaskOperationResult(false, "该任务下有" + count + "条批次未导入完成，不能删除");
        }

        // 删除任务分析记录
        analysisTaskRepository.removeById(dto.getTaskId());

        // 删除相关批次记录
        List<TaskBatch> taskBatches = taskBatchRepository.findByTaskId(dto.getTaskId());
        if (!taskBatches.isEmpty()) {
            List<Integer> batchIds = taskBatches.stream()
                    .map(TaskBatch::getBatchId)
                    .collect(Collectors.toList());
            taskBatchRepository.removeByIds(batchIds);
        }

        // 删除其他相关数据
        // 1. 删除离线任务批次文件
        try {
            offlineTaskBatchFileService.deleteTaskFiles(dto.getTaskId());
            log.info("删除任务批次文件关联成功，任务ID: {}", dto.getTaskId());
        } catch (Exception e) {
            log.error("删除任务批次文件关联失败，任务ID: {}", dto.getTaskId(), e);
        }

        // 2. 删除过滤状态
        try {
            filterStateService.deleteFilterState(dto.getTaskId());
            log.info("删除过滤状态成功，任务ID: {}", dto.getTaskId());
        } catch (Exception e) {
            log.error("删除过滤状态失败，任务ID: {}", dto.getTaskId(), e);
        }

        // 3. 删除MinIO中的PCAP文件
        try {
            MinioFileService.DeleteFilesResult deleteResult = minioFileService.deletePcapFiles(dto.getTaskId().toString());
            if (!deleteResult.isStatus()) {
                log.warn("删除MinIO中的PCAP文件失败: {}", deleteResult.getMessage());
            } else {
                log.info("删除MinIO中的PCAP文件成功: {}", deleteResult.getMessage());
            }
        } catch (Exception e) {
            log.error("删除MinIO中的PCAP文件时发生异常", e);
        }
        // 4. 删除ES索引

        return new TaskOperationResult(true, "删除任务成功");
    }

    @Override
    public TaskOperationResult startTask(Integer taskId) {
        // TODO: 实现任务启动逻辑
        return new TaskOperationResult(true, "任务启动成功");
    }

    @Override
    public TaskOperationResult stopTask(Integer taskId) {
        // TODO: 实现任务停止逻辑
        return new TaskOperationResult(true, "任务停止成功");
    }



    /**
     * 丰富任务信息，添加批次相关信息
     */
    private void enrichTaskWithBatchInfo(OfflineTaskVo offlineTaskVo) {
        List<OfflineTaskBatchProgressVo> batchProgressList = 
                taskBatchRepository.listBatchByTaskId(offlineTaskVo.getTaskId());
        
        offlineTaskVo.setLastImportTime(calculateLastImportTime(
                batchProgressList.stream().map(OfflineTaskBatchProgressVo::getEndTime).collect(Collectors.toList())));
        offlineTaskVo.setBatchNum(batchProgressList.size());
        
        List<OfflineTaskBatchProgressVo> activeBatches = batchProgressList.stream()
                .filter(vo -> vo.getBatchStatus() != null && vo.getBatchStatus() != 3 && vo.getBatchStatus() != 4)
                .collect(Collectors.toList());
        offlineTaskVo.setBatchProgressList(activeBatches);
    }

    /**
     * 计算最后导入时间
     */
    private String calculateLastImportTime(List<Integer> endTimeList) {
        int lastImportTime = -1;
        
        for (Integer endTime : endTimeList) {
            if (endTime != null) {
                if (lastImportTime == -1 || endTime > lastImportTime) {
                    lastImportTime = endTime;
                }
            }
        }
        
        if (lastImportTime == -1) {
            return "";
        }
        
        Instant instant = Instant.ofEpochSecond(lastImportTime);
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dateTime.format(formatter);
    }

    /**
     * 计算任务状态
     */
    private Integer calculateTaskStatus(List<TaskBatch> taskBatches) {
        for (TaskBatch taskBatch : taskBatches) {
            Integer taskStatus = taskBatch.getBatchStatus();
            if (taskStatus != null) {
                // 检查是否为等待导入或正在导入状态
                if (taskStatus.equals(1) || taskStatus.equals(2)) {
                    // 返回运行中状态 (1: 等待导入, 2: 正在导入)
                    return 1;
                }
            }
        }
        // 返回已完成状态
        return 2;
    }

    /**
     * 获取当前用户ID
     * TODO: 从安全上下文或Token中获取
     */
    private Integer getCurrentUserId() {
        // 临时实现，实际应该从安全上下文获取
        return 1;
    }
}
