package com.geeksec.task.model.vo;

import lombok.Data;

/**
 * 离线任务批次进度视图对象
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
public class OfflineTaskBatchProgressVo {

    /**
     * 批次ID
     */
    private Integer batchId;

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 批次状态 (1: 等待导入, 2: 正在导入, 3: 导入完成, 4: 导入失败)
     */
    private Integer batchStatus;

    /**
     * 批次进度 (0-100)
     */
    private Double batchProgress;

    /**
     * 批次描述
     */
    private String batchRemark;

    /**
     * 导入开始时间
     */
    private Integer beginTime;

    /**
     * 导入结束时间
     */
    private Integer endTime;

    /**
     * 数据开始时间
     */
    private Integer dataBeginTime;

    /**
     * 数据结束时间
     */
    private Integer dataEndTime;

    /**
     * 导入数据量（字节）
     */
    private Long batchBytes;

    /**
     * 导入会话量
     */
    private Integer batchSession;

    /**
     * 批次的告警量
     */
    private Integer batchAlarm;

    /**
     * 高危目标数量
     */
    private Integer importantTarget;

    /**
     * 批次数据路径
     */
    private String batchDir;

    /**
     * 批次运行状态 (1: 正在运行, 0: 关闭)
     */
    private Integer state;
}
