package com.geeksec.task.infrastructure.repository;

import com.geeksec.task.infrastructure.mapper.AnalysisTaskMapper;
import com.geeksec.task.model.entity.AnalysisTask;
import com.geeksec.task.model.condition.OfflineTaskQueryCondition;
import com.geeksec.task.model.vo.OfflineTaskVo;
import com.geeksec.task.model.vo.OfflineTaskPageVo;
import com.geeksec.task.repository.AnalysisTaskRepository;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 分析任务仓储实现类
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Repository
public class AnalysisTaskRepositoryImpl extends ServiceImpl<AnalysisTaskMapper, AnalysisTask> implements AnalysisTaskRepository {

    @Override
    public OfflineTaskVo getLastTask(Integer userId) {
        return getMapper().getLastTask(userId);
    }

    @Override
    public OfflineTaskVo getTask(Integer taskId) {
        return getMapper().getTask(taskId);
    }

    @Override
    public List<OfflineTaskPageVo> pageTask(OfflineTaskQueryCondition condition) {
        return getMapper().pageTask(condition);
    }

    @Override
    public List<AnalysisTask> findByUserId(Integer userId) {
        return list(QueryWrapper.create()
                .where("user_id = ?", userId)
                .and("deleted = ?", 0)
                .orderBy("create_time DESC"));
    }

    @Override
    public AnalysisTask findByTaskName(String taskName) {
        return getOne(QueryWrapper.create()
                .where("task_name = ?", taskName)
                .and("deleted = ?", 0));
    }

    @Override
    public boolean updateTaskId(Integer id) {
        return getMapper().updateTaskId(id);
    }

    @Override
    public boolean updateTask(AnalysisTask analysisTask) {
        return updateById(analysisTask);
    }

    @Override
    public List<AnalysisTask> findByTaskState(Integer taskState) {
        return list(QueryWrapper.create()
                .where("task_state = ?", taskState)
                .and("deleted = ?", 0)
                .orderBy("create_time DESC"));
    }

    @Override
    public List<AnalysisTask> findByTaskType(Integer taskType) {
        return list(QueryWrapper.create()
                .where("task_type = ?", taskType)
                .and("deleted = ?", 0)
                .orderBy("create_time DESC"));
    }
}
