package com.geeksec.task.config;

import io.minio.MinioClient;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MinIO 配置类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "minio")
@Data
public class MinioConfig {

    /**
     * MinIO 服务端点
     */
    private String endpoint = "http://localhost:9000";

    /**
     * 访问密钥
     */
    private String accessKey = "minioadmin";

    /**
     * 秘密密钥
     */
    private String secretKey = "minioadmin";

    /**
     * PCAP 文件存储桶名称
     */
    private String pcapBucket = "nta-pcap-files";

    /**
     * 默认存储桶名称
     */
    private String defaultBucket = "nta-data";

    /**
     * 是否启用路径样式访问
     */
    private boolean pathStyleAccess = true;

    /**
     * 区域
     */
    private String region = "us-east-1";

    /**
     * 创建 MinIO 客户端 Bean
     * 
     * @return MinIO 客户端
     */
    @Bean
    public MinioClient minioClient() {
        try {
            log.info("初始化 MinIO 客户端，端点: {}, 存储桶: {}", endpoint, pcapBucket);
            
            MinioClient client = MinioClient.builder()
                    .endpoint(endpoint)
                    .credentials(accessKey, secretKey)
                    .build();
            
            log.info("MinIO 客户端初始化成功");
            return client;
        } catch (Exception e) {
            log.error("MinIO 客户端初始化失败", e);
            throw new RuntimeException("MinIO 客户端初始化失败", e);
        }
    }
}
