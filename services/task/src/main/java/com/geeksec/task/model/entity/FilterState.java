package com.geeksec.task.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 过滤状态实体类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Table("tb_filter_state")
public class FilterState implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id(keyType = KeyType.Auto)
    @Column("id")
    private Integer id;

    /**
     * 任务ID
     */
    @Column("task_id")
    private Integer taskId;

    /**
     * 过滤状态 (0: 未启用, 1: 已启用, 2: 暂停, 3: 错误)
     */
    @Column("state")
    private Integer state;

    /**
     * 过滤配置JSON
     */
    @Column("config")
    private String config;

    /**
     * 过滤规则版本
     */
    @Column("rule_version")
    private String ruleVersion;

    /**
     * 最后同步时间
     */
    @Column("last_sync_time")
    private LocalDateTime lastSyncTime;

    /**
     * 同步状态 (0: 未同步, 1: 同步成功, 2: 同步失败)
     */
    @Column("sync_status")
    private Integer syncStatus;

    /**
     * 同步错误信息
     */
    @Column("sync_error")
    private String syncError;

    /**
     * 创建时间
     */
    @Column("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 创建者用户ID
     */
    @Column("created_by")
    private Integer createdBy;

    /**
     * 更新者用户ID
     */
    @Column("updated_by")
    private Integer updatedBy;
}
