package com.geeksec.task.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务响应DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Schema(description = "任务信息")
public class TaskResponse {
    
    @Schema(description = "任务ID", example = "1")
    private Integer taskId;
    
    @Schema(description = "任务名称", example = "网络流量分析任务")
    private String taskName;
    
    @Schema(description = "任务描述", example = "分析企业内网流量")
    private String taskDescription;
    
    @Schema(description = "任务状态", example = "RUNNING")
    private String taskStatus;
    
    @Schema(description = "任务类型", example = "ANALYSIS")
    private String taskType;
    
    @Schema(description = "创建时间", example = "2024-01-01T12:00:00")
    private LocalDateTime createTime;
    
    @Schema(description = "开始时间", example = "2024-01-01T12:00:00")
    private LocalDateTime startTime;
    
    @Schema(description = "结束时间", example = "2024-01-01T18:00:00")
    private LocalDateTime endTime;
    
    @Schema(description = "会话数量", example = "10000")
    private Long sessionCount;
    
    @Schema(description = "数据大小(字节)", example = "1073741824")
    private Long dataSize;
    
    @Schema(description = "进度百分比", example = "75")
    private Integer progressPercent;
    
    @Schema(description = "错误信息")
    private String errorMessage;
    
    @Schema(description = "创建用户ID", example = "1")
    private Integer userId;
    
    @Schema(description = "创建用户名", example = "admin")
    private String userName;
}
