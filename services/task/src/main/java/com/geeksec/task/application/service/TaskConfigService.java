package com.geeksec.task.application.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.geeksec.task.model.vo.TaskInfoVo;

import java.util.List;

/**
 * 任务配置应用服务接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface TaskConfigService {

    /**
     * 查询任务详情信息
     * 
     * @param taskId 任务ID
     * @return 任务详情
     */
    JsonNode configTaskInfo(Integer taskId);

    /**
     * 任务配置编辑
     * 
     * @param condition 配置条件
     * @return 编辑结果
     */
    boolean configTaskEdit(Object condition);

    /**
     * 获取任务编辑信息列表
     * 
     * @return 任务编辑信息列表
     */
    List<TaskInfoVo> taskEditVoList();

    /**
     * 获取任务基本信息
     * 
     * @param taskId 任务ID
     * @return 任务基本信息
     */
    TaskInfoVo getTaskBasicInfo(Integer taskId);

    /**
     * 更新任务配置
     * 
     * @param taskId 任务ID
     * @param config 配置信息
     * @return 更新结果
     */
    boolean updateTaskConfig(Integer taskId, String config);

    /**
     * 获取任务配置
     * 
     * @param taskId 任务ID
     * @return 配置信息
     */
    String getTaskConfig(Integer taskId);

    /**
     * 验证任务配置
     * 
     * @param config 配置信息
     * @return 验证结果
     */
    boolean validateTaskConfig(String config);
}
