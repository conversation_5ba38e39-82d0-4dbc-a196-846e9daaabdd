package com.geeksec.task.interfaces.controller;

import com.geeksec.task.application.service.OfflineTaskBatchService;
import com.geeksec.task.model.vo.FileTreeNodeVo;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.entity.PageResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 离线任务批次控制器
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/offline-task-batch")
@RequiredArgsConstructor
@Validated
@Tag(name = "离线任务批次管理", description = "离线任务批次的增删改查操作")
public class OfflineTaskBatchController {

    private final OfflineTaskBatchService offlineTaskBatchService;

    /**
     * 数据管理列表
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询批次列表", description = "根据条件分页查询离线任务批次列表")
    public ApiResponse<PageResultVo<OfflineTaskBatchService.OfflineTaskBatchPageVo>> pageBatch(
            @Parameter(description = "查询条件", required = true)
            @RequestBody @Valid OfflineTaskBatchService.OfflineTaskBatchQueryCondition condition) {
        try {
            OfflineTaskBatchService.BatchPageResult batchResult =
                    offlineTaskBatchService.pageBatch(condition);

            // 转换为 PageResultVo
            PageResultVo<OfflineTaskBatchService.OfflineTaskBatchPageVo> result =
                    new PageResultVo<>(batchResult.getData(), batchResult.getTotal(),
                                     batchResult.getCurrentPage(), batchResult.getPageSize());

            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("分页查询批次列表失败", e);
            return ApiResponse.error("分页查询批次列表失败：" + e.getMessage());
        }
    }

    /**
     * 添加批次
     */
    @PostMapping("/add")
    @Operation(summary = "添加批次", description = "创建新的离线任务批次")
    public ApiResponse<Void> addBatch(
            @Parameter(description = "批次信息", required = true)
            @RequestBody @Valid OfflineTaskBatchService.OfflineTaskBatchDto dto) {
        try {
            OfflineTaskBatchService.BatchOperationResult result = offlineTaskBatchService.addBatch(dto);
            if (result.isSuccess()) {
                return ApiResponse.success((Void) null);
            } else {
                return ApiResponse.error(result.getMessage());
            }
        } catch (Exception e) {
            log.error("添加批次失败", e);
            return ApiResponse.error("添加批次失败：" + e.getMessage());
        }
    }

    /**
     * 取消批次
     */
    @PostMapping("/cancel")
    @Operation(summary = "取消批次", description = "取消指定的离线任务批次")
    public ApiResponse<Void> cancelBatch(
            @Parameter(description = "取消参数", required = true)
            @RequestBody @Valid OfflineTaskBatchService.OfflineTaskBatchCancelDto dto) {
        try {
            OfflineTaskBatchService.BatchOperationResult result = offlineTaskBatchService.cancelBatch(dto);
            if (result.isSuccess()) {
                return ApiResponse.success((Void) null);
            } else {
                return ApiResponse.error(result.getMessage());
            }
        } catch (Exception e) {
            log.error("取消批次失败", e);
            return ApiResponse.error("取消批次失败：" + e.getMessage());
        }
    }

    /**
     * 查询服务器文件路径
     */
    @GetMapping("/listServerPath")
    @Operation(summary = "查询服务器文件路径", description = "浏览MinIO存储桶中的PCAP文件")
    public ApiResponse<List<FileTreeNodeVo>> listServerPath(
            @Parameter(description = "目录路径", required = false)
            @RequestParam(value = "directory_path", required = false, defaultValue = "") String directoryPath) {
        try {
            List<FileTreeNodeVo> result = offlineTaskBatchService.listServerPath(directoryPath);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("查询服务器文件路径失败", e);
            return ApiResponse.error("查询服务器文件路径失败：" + e.getMessage());
        }
    }

    /**
     * 检查文件路径是否存在
     */
    @PostMapping("/checkFilePaths")
    @Operation(summary = "检查文件路径", description = "检查MinIO中指定文件路径是否存在")
    public ResponseEntity<Map<String, Object>> checkFilePaths(
            @Parameter(description = "文件路径检查请求", required = true)
            @RequestBody CheckFilePathsRequest request) {
        try {
            OfflineTaskBatchService.CheckFilePathsResult result = 
                    offlineTaskBatchService.checkFilePaths(request.getFilePathList());
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", result.isStatus());
            response.put("non_exist_files", result.getNonExistFiles());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("检查文件路径失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 删除批次下的PCAP文件
     */
    @PostMapping("/deletePcapFiles")
    @Operation(summary = "删除PCAP文件", description = "删除MinIO中指定任务的PCAP文件")
    public ResponseEntity<Map<String, Object>> deletePcapFiles(
            @Parameter(description = "删除文件请求", required = true)
            @RequestBody DeletePcapFilesRequest request) {
        try {
            OfflineTaskBatchService.DeleteFilesResult result = 
                    offlineTaskBatchService.deletePcapFiles(request.getTaskId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", result.isStatus());
            response.put("message", result.getMessage());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("删除PCAP文件失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 检查文件路径请求
     */
    public static class CheckFilePathsRequest {
        private List<String> filePathList;

        public List<String> getFilePathList() {
            return filePathList;
        }

        public void setFilePathList(List<String> filePathList) {
            this.filePathList = filePathList;
        }
    }

    /**
     * 删除PCAP文件请求
     */
    public static class DeletePcapFilesRequest {
        private String taskId;

        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }
    }
}
