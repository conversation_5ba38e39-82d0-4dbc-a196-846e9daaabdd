package com.geeksec.task.model.vo;

import lombok.Data;

/**
 * 离线任务分页视图对象
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
public class OfflineTaskPageVo {

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务描述
     */
    private String taskDescription;

    /**
     * 任务备注
     */
    private String taskRemark;

    /**
     * 任务状态 (1: 运行中, 2: 已完成)
     */
    private Integer taskStatus;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * 网络流量信息
     */
    private String netFlow;

    /**
     * 最后导入时间
     */
    private String lastImportTime;

    /**
     * 批次数量
     */
    private Integer batchNum;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
