package com.geeksec.task.application.service;

import com.geeksec.task.model.entity.FilterState;

import java.util.List;

/**
 * 过滤状态服务接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface FilterStateService {

    /**
     * 创建过滤状态
     * 
     * @param taskId 任务ID
     * @param state 初始状态
     * @param createdBy 创建者用户ID
     */
    void createFilterState(Integer taskId, Integer state, Integer createdBy);

    /**
     * 根据任务ID获取过滤状态
     * 
     * @param taskId 任务ID
     * @return 过滤状态
     */
    FilterState getFilterState(Integer taskId);

    /**
     * 更新过滤状态
     * 
     * @param taskId 任务ID
     * @param state 新状态
     */
    void updateFilterState(Integer taskId, Integer state);

    /**
     * 更新过滤配置
     * 
     * @param taskId 任务ID
     * @param config 配置JSON
     */
    void updateFilterConfig(Integer taskId, String config);

    /**
     * 删除过滤状态
     * 
     * @param taskId 任务ID
     */
    void deleteFilterState(Integer taskId);

    /**
     * 同步过滤状态到探针
     * 
     * @param taskId 任务ID
     * @return 同步结果
     */
    SyncResult syncFilterStateToProbe(Integer taskId);

    /**
     * 批量同步过滤状态
     * 
     * @return 同步结果列表
     */
    List<SyncResult> batchSyncFilterStates();

    /**
     * 根据状态获取过滤状态列表
     * 
     * @param state 过滤状态
     * @return 过滤状态列表
     */
    List<FilterState> getFilterStatesByState(Integer state);

    /**
     * 获取需要同步的过滤状态
     * 
     * @return 需要同步的过滤状态列表
     */
    List<FilterState> getNeedSyncFilterStates();

    /**
     * 更新同步状态
     * 
     * @param taskId 任务ID
     * @param syncStatus 同步状态
     * @param syncError 同步错误信息
     */
    void updateSyncStatus(Integer taskId, Integer syncStatus, String syncError);

    /**
     * 检查过滤状态是否存在
     * 
     * @param taskId 任务ID
     * @return 是否存在
     */
    boolean existsFilterState(Integer taskId);

    /**
     * 同步结果
     */
    class SyncResult {
        private Integer taskId;
        private boolean success;
        private String message;
        private String errorDetails;

        public SyncResult(Integer taskId, boolean success, String message) {
            this.taskId = taskId;
            this.success = success;
            this.message = message;
        }

        public SyncResult(Integer taskId, boolean success, String message, String errorDetails) {
            this.taskId = taskId;
            this.success = success;
            this.message = message;
            this.errorDetails = errorDetails;
        }

        // Getters and Setters
        public Integer getTaskId() { return taskId; }
        public void setTaskId(Integer taskId) { this.taskId = taskId; }

        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public String getErrorDetails() { return errorDetails; }
        public void setErrorDetails(String errorDetails) { this.errorDetails = errorDetails; }
    }
}
