package com.geeksec.task.infrastructure.mapper;

import com.geeksec.task.model.entity.DownloadTask;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 下载任务数据访问映射器
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Mapper
public interface DownloadTaskMapper extends BaseMapper<DownloadTask> {

    /**
     * 根据任务ID获取PCAP文件路径
     * 
     * @param taskId 任务ID
     * @return PCAP文件路径
     */
    @Select("SELECT path FROM tb_download_task WHERE task_id = #{taskId} AND status = 1 LIMIT 1")
    String getPcapPath(@Param("taskId") Integer taskId);
}
