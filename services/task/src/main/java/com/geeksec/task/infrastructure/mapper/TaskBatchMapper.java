package com.geeksec.task.infrastructure.mapper;

import com.geeksec.task.model.entity.TaskBatch;
import com.geeksec.task.model.vo.OfflineTaskBatchProgressVo;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 任务批次数据访问映射器
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Mapper
public interface TaskBatchMapper extends BaseMapper<TaskBatch> {

    /**
     * 根据任务ID查询批次进度信息
     * 
     * @param taskId 任务ID
     * @return 批次进度列表
     */
    @Select("SELECT batch_id, task_id, batch_status, batch_progress, batch_remark, " +
            "begin_time, end_time, data_begin_time, data_end_time, " +
            "batch_bytes, batch_session, batch_alarm, importrarnt_target as important_target, " +
            "batch_dir, state " +
            "FROM tb_task_batch WHERE task_id = #{taskId} ORDER BY batch_id DESC")
    List<OfflineTaskBatchProgressVo> listBatchByTaskId(@Param("taskId") Integer taskId);

    /**
     * 根据任务ID列表查询批次信息
     *
     * @param taskIdList 任务ID列表
     * @return 批次列表
     */
    @Select("""
            <script>
            SELECT batch_id, task_id, task_type, batch_type, batch_remark, fullflow_state, flowlog_state,
                   data_type, topology_state, begin_time, end_time, data_begin_time, data_end_time,
                   batch_bytes, batch_session, batch_alarm, importrarnt_target, filter_data_total,
                   rule_hits_data_total, whitelist_filter_total, batch_status, batch_progress,
                   batch_dir, report_path, screening_conditions, avg_byte_pt_ps, max_byte_pt_ps,
                   addr, task_update, full_flow_should_log_def, parse_proto_should_log_def, state
            FROM tb_task_batch
            WHERE task_id IN
            <foreach collection="taskIdList" item="taskId" open="(" close=")" separator=",">
                #{taskId}
            </foreach>
            ORDER BY batch_id DESC
            </script>
            """)
    List<TaskBatch> listTaskBatchItem(@Param("taskIdList") List<Integer> taskIdList);

    /**
     * 统计任务下未完成的批次数量
     * 
     * @param taskId 任务ID
     * @return 未完成批次数量
     */
    @Select("SELECT COUNT(*) FROM tb_task_batch WHERE task_id = #{taskId} AND batch_status IN (1, 2)")
    Integer countIncompleteTask(@Param("taskId") Integer taskId);
}
