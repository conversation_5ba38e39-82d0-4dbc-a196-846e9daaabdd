package com.geeksec.task.repository;

import java.util.List;

import com.geeksec.task.model.condition.OfflineTaskQueryCondition;
import com.geeksec.task.model.entity.AnalysisTask;
import com.geeksec.task.model.vo.OfflineTaskPageVo;
import com.geeksec.task.model.vo.OfflineTaskVo;
import com.mybatisflex.core.service.IService;

/**
 * 分析任务仓储接口
 * 用于管理流量分析任务，包括实时分析任务（从NIC采集流量数据）和离线分析任务（读取pcap文件）
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface AnalysisTaskRepository extends IService<AnalysisTask> {

    /**
     * 获取用户最新的分析任务
     *
     * @param userId 用户ID
     * @return 最新分析任务
     */
    OfflineTaskVo getLastTask(Integer userId);

    /**
     * 根据任务ID获取分析任务详情
     *
     * @param taskId 任务ID
     * @return 分析任务详情
     */
    OfflineTaskVo getTask(Integer taskId);

    /**
     * 分页查询分析任务列表
     *
     * @param condition 查询条件
     * @return 分析任务分页列表
     */
    List<OfflineTaskPageVo> pageTask(OfflineTaskQueryCondition condition);

    /**
     * 根据用户ID查询分析任务列表
     *
     * @param userId 用户ID
     * @return 分析任务列表
     */
    List<AnalysisTask> findByUserId(Integer userId);

    /**
     * 根据任务名称查询分析任务
     *
     * @param taskName 任务名称
     * @return 分析任务
     */
    AnalysisTask findByTaskName(String taskName);

    /**
     * 更新分析任务ID
     *
     * @param id 主键ID
     * @return 更新结果
     */
    boolean updateTaskId(Integer id);

    /**
     * 更新分析任务信息
     *
     * @param analysisTask 分析任务信息
     * @return 更新结果
     */
    boolean updateTask(AnalysisTask analysisTask);

    /**
     * 根据任务状态查询分析任务列表
     *
     * @param taskState 任务状态
     * @return 分析任务列表
     */
    List<AnalysisTask> findByTaskState(Integer taskState);

    /**
     * 根据分析任务类型查询任务列表
     *
     * @param taskType 分析任务类型 (1: 实时分析任务, 2: 离线分析任务)
     * @return 分析任务列表
     */
    List<AnalysisTask> findByTaskType(Integer taskType);
}
