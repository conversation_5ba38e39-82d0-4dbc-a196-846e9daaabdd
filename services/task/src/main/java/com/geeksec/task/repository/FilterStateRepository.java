package com.geeksec.task.repository;

import com.geeksec.task.model.entity.FilterState;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

import static com.geeksec.task.model.entity.table.FilterStateTableDef.FILTER_STATE;

/**
 * 过滤状态数据访问接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Mapper
public interface FilterStateRepository extends BaseMapper<FilterState> {

    /**
     * 根据任务ID查询过滤状态
     * 
     * @param taskId 任务ID
     * @return 过滤状态
     */
    default FilterState findByTaskId(Integer taskId) {
        return selectOneByQuery(QueryWrapper.create()
                .where(FILTER_STATE.TASK_ID.eq(taskId)));
    }

    /**
     * 根据任务ID删除过滤状态
     * 
     * @param taskId 任务ID
     * @return 删除的记录数
     */
    default int deleteByTaskId(Integer taskId) {
        return deleteByQuery(QueryWrapper.create()
                .where(FILTER_STATE.TASK_ID.eq(taskId)));
    }

    /**
     * 根据状态查询过滤状态列表
     * 
     * @param state 过滤状态
     * @return 过滤状态列表
     */
    default List<FilterState> findByState(Integer state) {
        return selectListByQuery(QueryWrapper.create()
                .where(FILTER_STATE.STATE.eq(state))
                .orderBy(FILTER_STATE.CREATED_AT.asc()));
    }

    /**
     * 更新过滤状态
     * 
     * @param taskId 任务ID
     * @param state 新状态
     * @return 更新的记录数
     */
    default int updateState(Integer taskId, Integer state) {
        FilterState updateEntity = new FilterState();
        updateEntity.setState(state);
        updateEntity.setUpdatedAt(LocalDateTime.now());
        return updateByQuery(updateEntity, QueryWrapper.create()
                .where(FILTER_STATE.TASK_ID.eq(taskId)));
    }

    /**
     * 更新过滤配置
     * 
     * @param taskId 任务ID
     * @param config 配置JSON
     * @return 更新的记录数
     */
    default int updateConfig(Integer taskId, String config) {
        FilterState updateEntity = new FilterState();
        updateEntity.setConfig(config);
        updateEntity.setUpdatedAt(LocalDateTime.now());
        return updateByQuery(updateEntity, QueryWrapper.create()
                .where(FILTER_STATE.TASK_ID.eq(taskId)));
    }

    /**
     * 更新同步状态
     * 
     * @param taskId 任务ID
     * @param syncStatus 同步状态
     * @param syncError 同步错误信息
     * @return 更新的记录数
     */
    default int updateSyncStatus(Integer taskId, Integer syncStatus, String syncError) {
        FilterState updateEntity = new FilterState();
        updateEntity.setSyncStatus(syncStatus);
        updateEntity.setSyncError(syncError);
        updateEntity.setLastSyncTime(LocalDateTime.now());
        updateEntity.setUpdatedAt(LocalDateTime.now());
        return updateByQuery(updateEntity, QueryWrapper.create()
                .where(FILTER_STATE.TASK_ID.eq(taskId)));
    }

    /**
     * 查询需要同步的过滤状态
     * 
     * @return 需要同步的过滤状态列表
     */
    default List<FilterState> findNeedSync() {
        return selectListByQuery(QueryWrapper.create()
                .where(FILTER_STATE.SYNC_STATUS.eq(0)
                        .or(FILTER_STATE.SYNC_STATUS.eq(2)))
                .orderBy(FILTER_STATE.CREATED_AT.asc()));
    }

    /**
     * 创建过滤状态
     * 
     * @param taskId 任务ID
     * @param state 初始状态
     * @param createdBy 创建者用户ID
     * @return 创建的记录数
     */
    default int createFilterState(Integer taskId, Integer state, Integer createdBy) {
        FilterState filterState = new FilterState();
        filterState.setTaskId(taskId);
        filterState.setState(state);
        filterState.setSyncStatus(0); // 未同步
        filterState.setCreatedBy(createdBy);
        filterState.setUpdatedBy(createdBy);
        filterState.setCreatedAt(LocalDateTime.now());
        filterState.setUpdatedAt(LocalDateTime.now());
        return insert(filterState);
    }
}
