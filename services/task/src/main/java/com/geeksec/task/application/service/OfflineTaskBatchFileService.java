package com.geeksec.task.application.service;

import com.geeksec.task.model.dto.OfflineFilePathDto;
import com.geeksec.task.model.entity.OfflineTaskBatchFile;
import com.geeksec.task.model.entity.TaskBatch;

import java.util.List;

/**
 * 离线任务批次文件服务接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface OfflineTaskBatchFileService {

    /**
     * 添加批次文件关联
     * 
     * @param taskBatch 任务批次
     * @param filePathList 文件路径列表
     */
    void addBatchFiles(TaskBatch taskBatch, List<OfflineFilePathDto> filePathList);

    /**
     * 根据批次ID获取文件列表
     * 
     * @param batchId 批次ID
     * @return 文件列表
     */
    List<OfflineTaskBatchFile> getBatchFiles(Integer batchId);

    /**
     * 根据任务ID获取文件列表
     * 
     * @param taskId 任务ID
     * @return 文件列表
     */
    List<OfflineTaskBatchFile> getTaskFiles(Integer taskId);

    /**
     * 删除批次文件关联
     * 
     * @param batchId 批次ID
     */
    void deleteBatchFiles(Integer batchId);

    /**
     * 删除任务文件关联
     * 
     * @param taskId 任务ID
     */
    void deleteTaskFiles(Integer taskId);

    /**
     * 更新文件状态
     * 
     * @param fileId 文件ID
     * @param status 新状态
     */
    void updateFileStatus(Integer fileId, Integer status);

    /**
     * 批量更新批次文件状态
     * 
     * @param batchId 批次ID
     * @param status 新状态
     */
    void updateBatchFileStatus(Integer batchId, Integer status);

    /**
     * 获取批次文件统计信息
     * 
     * @param batchId 批次ID
     * @return 文件统计信息
     */
    BatchFileStatistics getBatchFileStatistics(Integer batchId);

    /**
     * 根据状态获取文件列表
     * 
     * @param status 文件状态
     * @return 文件列表
     */
    List<OfflineTaskBatchFile> getFilesByStatus(Integer status);

    /**
     * 开始处理文件
     * 
     * @param fileId 文件ID
     */
    void startProcessFile(Integer fileId);

    /**
     * 完成文件处理
     * 
     * @param fileId 文件ID
     * @param success 是否成功
     * @param errorMessage 错误信息（如果失败）
     */
    void finishProcessFile(Integer fileId, boolean success, String errorMessage);

    /**
     * 批次文件统计信息
     */
    class BatchFileStatistics {
        private long totalFiles;
        private long pendingFiles;
        private long processingFiles;
        private long completedFiles;
        private long failedFiles;

        public BatchFileStatistics(long totalFiles, long pendingFiles, long processingFiles, 
                                 long completedFiles, long failedFiles) {
            this.totalFiles = totalFiles;
            this.pendingFiles = pendingFiles;
            this.processingFiles = processingFiles;
            this.completedFiles = completedFiles;
            this.failedFiles = failedFiles;
        }

        // Getters
        public long getTotalFiles() { return totalFiles; }
        public long getPendingFiles() { return pendingFiles; }
        public long getProcessingFiles() { return processingFiles; }
        public long getCompletedFiles() { return completedFiles; }
        public long getFailedFiles() { return failedFiles; }

        // 计算进度百分比
        public double getProgressPercentage() {
            if (totalFiles == 0) return 0.0;
            return (double) completedFiles / totalFiles * 100.0;
        }

        // 判断是否全部完成
        public boolean isAllCompleted() {
            return totalFiles > 0 && completedFiles == totalFiles;
        }

        // 判断是否有失败的文件
        public boolean hasFailedFiles() {
            return failedFiles > 0;
        }
    }
}
