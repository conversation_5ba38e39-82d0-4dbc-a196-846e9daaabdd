package com.geeksec.task.model.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 离线文件路径数据传输对象
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
public class OfflineFilePathDto {

    /**
     * 本地文件路径
     */
    @NotBlank(message = "本地文件路径不能为空")
    @Size(max = 500, message = "本地文件路径长度不能超过500个字符")
    private String localPath;

    /**
     * 服务器文件路径
     */
    @NotBlank(message = "服务器文件路径不能为空")
    @Size(max = 500, message = "服务器文件路径长度不能超过500个字符")
    private String serverPath;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件类型 (1: PCAP文件, 2: 其他文件)
     */
    private Integer fileType = 1;

    /**
     * 构造函数
     */
    public OfflineFilePathDto() {}

    /**
     * 构造函数
     * 
     * @param localPath 本地文件路径
     * @param serverPath 服务器文件路径
     */
    public OfflineFilePathDto(String localPath, String serverPath) {
        this.localPath = localPath;
        this.serverPath = serverPath;
    }

    /**
     * 构造函数
     * 
     * @param localPath 本地文件路径
     * @param serverPath 服务器文件路径
     * @param fileSize 文件大小
     */
    public OfflineFilePathDto(String localPath, String serverPath, Long fileSize) {
        this.localPath = localPath;
        this.serverPath = serverPath;
        this.fileSize = fileSize;
    }

    /**
     * 构造函数
     * 
     * @param localPath 本地文件路径
     * @param serverPath 服务器文件路径
     * @param fileSize 文件大小
     * @param fileType 文件类型
     */
    public OfflineFilePathDto(String localPath, String serverPath, Long fileSize, Integer fileType) {
        this.localPath = localPath;
        this.serverPath = serverPath;
        this.fileSize = fileSize;
        this.fileType = fileType;
    }
}
