package com.geeksec.task.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 离线任务视图对象
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
public class OfflineTaskVo {

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务描述
     */
    private String taskDescription;

    /**
     * 任务备注
     */
    private String taskRemark;

    /**
     * 任务状态
     */
    private Integer taskState;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * 网络流量信息
     */
    private String netFlow;

    /**
     * 最后导入时间
     */
    private String lastImportTime;

    /**
     * 批次数量
     */
    private Integer batchNum;

    /**
     * 批次进度列表
     */
    private List<OfflineTaskBatchProgressVo> batchProgressList;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
