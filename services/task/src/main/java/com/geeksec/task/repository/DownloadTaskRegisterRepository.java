package com.geeksec.task.repository;

import com.geeksec.task.model.entity.DownloadTaskRegister;
import com.mybatisflex.core.service.IService;

import java.util.List;

/**
 * 下载任务注册仓储接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface DownloadTaskRegisterRepository extends IService<DownloadTaskRegister> {

    /**
     * 根据用户ID查询下载任务注册列表
     * 
     * @param userId 用户ID
     * @return 下载任务注册列表
     */
    List<DownloadTaskRegister> findByUserId(Integer userId);

    /**
     * 根据任务状态查询下载任务注册列表
     * 
     * @param status 任务状态
     * @return 下载任务注册列表
     */
    List<DownloadTaskRegister> findByStatus(Integer status);

    /**
     * 根据任务类型查询下载任务注册列表
     * 
     * @param taskType 任务类型
     * @return 下载任务注册列表
     */
    List<DownloadTaskRegister> findByTaskType(Integer taskType);

    /**
     * 获取下一个待处理任务
     * 
     * @return 待处理任务
     */
    DownloadTaskRegister getNextPendingTask();

    /**
     * 统计用户的下载任务注册数量
     * 
     * @param userId 用户ID
     * @return 任务数量
     */
    Long countByUserId(Integer userId);

    /**
     * 根据状态和类型查询任务
     * 
     * @param status 状态
     * @param type 类型
     * @return 任务列表
     */
    List<DownloadTaskRegister> findByStatusAndType(Integer status, Integer type);
}
