package com.geeksec.session.service;

import com.geeksec.session.exception.SessionNotFoundException;
import com.geeksec.session.factory.TestDataFactory;
import com.geeksec.session.model.dto.SessionListResponse;
import com.geeksec.session.model.dto.SessionQueryRequest;
import com.geeksec.session.model.entity.Session;
import com.geeksec.session.repository.SessionRepository;
import com.geeksec.session.service.impl.SessionServiceImpl;
import com.mybatisflex.core.paginate.Page;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SessionService 单元测试
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("会话服务测试")
class SessionServiceTest {

    @Mock
    private SessionRepository sessionRepository;

    @InjectMocks
    private SessionServiceImpl sessionService;

    private Session testSession;
    private SessionQueryRequest testQueryRequest;

    @BeforeEach
    void setUp() {
        testSession = TestDataFactory.createTestSession();
        testQueryRequest = TestDataFactory.createTestQueryRequest();
    }

    @Test
    @DisplayName("根据ID获取会话详情 - 成功")
    void getSessionById_Success() {
        // Given
        String sessionId = "session_test_001";
        when(sessionRepository.getSessionById(sessionId)).thenReturn(testSession);

        // When
        Session result = sessionService.getSessionById(sessionId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getSessionId()).isEqualTo(sessionId);
        assertThat(result.getSrcIp()).isEqualTo("*************");
        assertThat(result.getDstIp()).isEqualTo("********");

        verify(sessionRepository).getSessionById(sessionId);
    }

    @Test
    @DisplayName("根据ID获取会话详情 - 会话不存在")
    void getSessionById_NotFound() {
        // Given
        String sessionId = "non_existent_session";
        when(sessionRepository.getSessionById(sessionId)).thenReturn(null);

        // When & Then
        assertThatThrownBy(() -> sessionService.getSessionById(sessionId))
                .isInstanceOf(SessionNotFoundException.class)
                .hasMessageContaining(sessionId);

        verify(sessionRepository).getSessionById(sessionId);
    }

    @Test
    @DisplayName("分页查询会话列表 - 成功")
    void querySessionList_Success() {
        // Given
        Page<SessionListResponse> mockPage = new Page<>(1, 20);
        mockPage.setTotal(100L);
        mockPage.setRecords(List.of(new SessionListResponse()));

        when(sessionRepository.querySessionList(any(SessionQueryRequest.class)))
                .thenReturn(mockPage);

        // When
        Page<SessionListResponse> result = sessionService.querySessionList(testQueryRequest);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTotal()).isEqualTo(100L);
        assertThat(result.getRecords()).hasSize(1);

        verify(sessionRepository).querySessionList(testQueryRequest);
    }

    @Test
    @DisplayName("统计会话数量 - 成功")
    void countSessions_Success() {
        // Given
        long expectedCount = 1500L;
        when(sessionRepository.countSessions(any(SessionQueryRequest.class)))
                .thenReturn(expectedCount);

        // When
        long result = sessionService.countSessions(testQueryRequest);

        // Then
        assertThat(result).isEqualTo(expectedCount);

        verify(sessionRepository).countSessions(testQueryRequest);
    }

    @Test
    @DisplayName("批量查询会话 - 成功")
    void getSessionsByIds_Success() {
        // Given
        List<String> sessionIds = List.of("session_001", "session_002", "session_003");
        List<Session> mockSessions = TestDataFactory.createTestSessions(3);

        when(sessionRepository.getSessionsByIds(sessionIds)).thenReturn(mockSessions);

        // When
        List<Session> result = sessionService.getSessionsByIds(sessionIds);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(3);

        verify(sessionRepository).getSessionsByIds(sessionIds);
    }

    @Test
    @DisplayName("检查会话是否存在 - 存在")
    void existsById_True() {
        // Given
        String sessionId = "session_test_001";
        when(sessionRepository.getSessionById(sessionId)).thenReturn(testSession);

        // When
        boolean result = sessionService.existsById(sessionId);

        // Then
        assertThat(result).isTrue();

        verify(sessionRepository).getSessionById(sessionId);
    }

    @Test
    @DisplayName("检查会话是否存在 - 不存在")
    void existsById_False() {
        // Given
        String sessionId = "non_existent_session";
        when(sessionRepository.getSessionById(sessionId)).thenReturn(null);

        // When
        boolean result = sessionService.existsById(sessionId);

        // Then
        assertThat(result).isFalse();

        verify(sessionRepository).getSessionById(sessionId);
    }
}
