spring:
  datasource:
    dynamic:
      primary: doris
      strict: true
      hikari:
        connection-timeout: 30000
        max-lifetime: 1800000
        max-pool-size: 15
        min-idle: 5
        connection-test-query: SELECT 1
      doris:
        url: jdbc:mysql://${DORIS_FE_HOST:doris-fe}:${DORIS_FE_PORT:9030}/${DORIS_DATABASE:nta}?useSSL=false&useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai
        username: ${DORIS_USERNAME:root}
        password: ${DORIS_PASSWORD:}
        driver-class-name: com.mysql.cj.jdbc.Driver

# MyBatis-Flex 配置
mybatis-flex:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  global-update-time: update_time
  global-create-time: create_time
  logic-delete-field: is_deleted
  logic-delete-value: 1
  logic-not-delete-value: 0
  sql-prefix: "/*#mycat:sql=select 1 from dual*/"

# Knife4j 配置
knife4j:
  enable: true
  setting:
    language: zh-CN
    enable-swagger-models: true
    enable-document-manage: true
    enable-version-strategy: true
    enable-footer: false
    enable-footer-custom: false
    enable-request-cache: true
    enable-filter-multipart-api-method: true
    enable-filter-multipart-apis: true
    enable-host: ""
    enable-home-custom: true
    enable-home-custom-entries: true
    enable-search-tags: true
    enable-swagger-bootstrap-ui: true
    enable-document-manage: true
    swagger-model-name: 会话管理服务API文档
  cors: false
  production: false
  basic:
    enable: false
    username: test
    password: 123456
