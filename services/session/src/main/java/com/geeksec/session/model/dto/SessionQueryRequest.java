package com.geeksec.session.model.dto;

import com.mybatisflex.core.paginate.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会话查询请求DTO
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Schema(description = "会话查询请求参数")
public class SessionQueryRequest {

    @Schema(description = "源IP地址", example = "*************")
    private String srcIp;

    @Schema(description = "目标IP地址", example = "********")
    private String dstIp;

    @Schema(description = "源端口", example = "8080")
    @Min(value = 1, message = "端口号必须大于0")
    @Max(value = 65535, message = "端口号必须小于等于65535")
    private Integer srcPort;

    @Schema(description = "目标端口", example = "80")
    @Min(value = 1, message = "端口号必须大于0")
    @Max(value = 65535, message = "端口号必须小于等于65535")
    private Integer dstPort;

    @Schema(description = "协议号 (1:ICMP, 6:TCP, 17:UDP)", example = "6")
    private Integer protocol;

    @Schema(description = "应用ID", example = "1001")
    private Integer appId;

    @Schema(description = "应用名称", example = "HTTP")
    private String appName;

    @Schema(description = "任务ID列表", example = "[1, 2, 3]")
    private List<Integer> taskIds;

    @Schema(description = "标签ID列表", example = "[10, 20, 30]")
    private List<Integer> labelIds;

    @Schema(description = "开始时间", example = "2024-01-01T00:00:00")
    private LocalDateTime startTime;

    @Schema(description = "结束时间", example = "2024-01-01T23:59:59")
    private LocalDateTime endTime;

    @Schema(description = "是否只查询内部IP", example = "true")
    private Boolean internalOnly;

    @Schema(description = "是否只查询外部IP", example = "false")
    private Boolean externalOnly;

    @Schema(description = "最小会话持续时间(秒)", example = "10")
    @Min(value = 0, message = "持续时间不能为负数")
    private Integer minDuration;

    @Schema(description = "最大会话持续时间(秒)", example = "3600")
    @Min(value = 0, message = "持续时间不能为负数")
    private Integer maxDuration;

    @Schema(description = "最小负载字节数", example = "1024")
    @Min(value = 0, message = "字节数不能为负数")
    private Long minPayloadBytes;

    @Schema(description = "最大负载字节数", example = "1048576")
    @Min(value = 0, message = "字节数不能为负数")
    private Long maxPayloadBytes;

    @Schema(description = "协议类型过滤", example = "[\"HTTP\", \"HTTPS\", \"DNS\"]")
    private List<String> protocolTypes;

    @Schema(description = "关键字搜索", example = "example.com")
    private String keyword;

    @Schema(description = "页码 (从1开始)", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNumber = 1;

    @Schema(description = "每页记录数", example = "20")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 1000, message = "每页大小不能超过1000")
    private Integer pageSize = 20;

    @Schema(description = "排序字段", example = "sessionStartTime",
             allowableValues = {"sessionStartTime", "sessionEndTime", "srcTotalBytes", "dstTotalBytes", "sessionDuration"})
    private String sortBy = "sessionStartTime";

    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    private String sortOrder = "desc";

    /**
     * 转换为MyBatis-Flex的Page对象
     */
    public Page<Object> toPage() {
        return new Page<>(pageNumber, pageSize);
    }
}
