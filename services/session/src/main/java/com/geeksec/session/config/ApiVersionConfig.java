package com.geeksec.session.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * API 版本控制配置
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Configuration
public class ApiVersionConfig implements WebMvcConfigurer {
    
    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        // 配置API版本前缀
        configurer.addPathPrefix("/api/v3", c -> c.getPackageName().contains("com.geeksec.session.controller"));
    }
}
