package com.geeksec.session.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 会话趋势响应DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Schema(description = "会话趋势响应")
public class SessionTrendResponse {
    
    @Schema(description = "时间点", example = "2024-01-01T12:00:00")
    private LocalDateTime timestamp;
    
    @Schema(description = "时间范围标识", example = "2024-01-01 12:00-13:00")
    private String timeRange;
    
    @Schema(description = "会话数量", example = "1500")
    private Long sessionCount;
    
    @Schema(description = "总流量字节数", example = "104857600")
    private Long totalBytes;
    
    @Schema(description = "源流量字节数", example = "52428800")
    private Long srcBytes;
    
    @Schema(description = "目标流量字节数", example = "52428800")
    private Long dstBytes;
    
    @Schema(description = "平均会话持续时间(秒)", example = "120")
    private Double avgDuration;
    
    @Schema(description = "唯一源IP数量", example = "100")
    private Long uniqueSourceIps;
    
    @Schema(description = "唯一目标IP数量", example = "200")
    private Long uniqueDestinationIps;
    
    @Schema(description = "TCP会话数", example = "1200")
    private Long tcpSessions;
    
    @Schema(description = "UDP会话数", example = "250")
    private Long udpSessions;
    
    @Schema(description = "ICMP会话数", example = "50")
    private Long icmpSessions;
    
    @Schema(description = "HTTP会话数", example = "800")
    private Long httpSessions;
    
    @Schema(description = "HTTPS会话数", example = "600")
    private Long httpsSessions;
    
    @Schema(description = "DNS会话数", example = "300")
    private Long dnsSessions;
    
    @Schema(description = "异常会话数", example = "10")
    private Long anomalousSessions;
    
    @Schema(description = "高风险会话数", example = "5")
    private Long highRiskSessions;
}
