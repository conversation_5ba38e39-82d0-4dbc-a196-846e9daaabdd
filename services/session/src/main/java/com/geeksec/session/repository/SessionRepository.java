package com.geeksec.session.repository;

import com.geeksec.session.model.dto.SessionListResponse;
import com.geeksec.session.model.dto.SessionQueryRequest;
import com.geeksec.session.model.entity.Session;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.paginate.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会话数据访问接口 - 基于Doris只读查询
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Repository
public interface SessionRepository extends BaseMapper<Session> {

    /**
     * 分页查询会话列表
     *
     * @param queryRequest 查询条件
     * @return 分页结果
     */
    Page<SessionListResponse> querySessionList(SessionQueryRequest queryRequest);

    /**
     * 根据条件统计会话数量
     *
     * @param queryRequest 查询条件
     * @return 会话数量
     */
    long countSessions(SessionQueryRequest queryRequest);

    /**
     * 根据ID查询会话详情
     *
     * @param sessionId 会话ID
     * @return 会话详情
     */
    @Select("SELECT * FROM dwd_session_logs WHERE session_id = #{sessionId}")
    Session getSessionById(@Param("sessionId") String sessionId);

    /**
     * 根据多个ID批量查询会话
     *
     * @param sessionIds 会话ID列表
     * @return 会话列表
     */
    @Select("<script>" +
            "SELECT * FROM dwd_session_logs WHERE session_id IN " +
            "<foreach collection='sessionIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    List<Session> getSessionsByIds(@Param("sessionIds") List<String> sessionIds);

    /**
     * 查询指定时间范围内的会话统计信息
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param groupBy 分组字段
     * @return 统计结果
     */
    List<Map<String, Object>> getSessionStatistics(@Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime,
                                                   @Param("groupBy") String groupBy);

    /**
     * 查询会话聚合信息
     *
     * @param queryRequest 查询条件
     * @return 聚合结果
     */
    Page<Map<String, Object>> getSessionAggregation(SessionQueryRequest queryRequest);

    /**
     * 查询热门源IP统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 返回数量限制
     * @return 源IP统计列表
     */
    @Select("SELECT src_ip, COUNT(*) as session_count, SUM(src_total_bytes) as total_bytes " +
            "FROM dwd_session_logs " +
            "WHERE session_start_time >= #{startTime} AND session_start_time <= #{endTime} " +
            "GROUP BY src_ip " +
            "ORDER BY session_count DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getTopSourceIps(@Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime,
                                              @Param("limit") int limit);

    /**
     * 查询热门目标IP统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 返回数量限制
     * @return 目标IP统计列表
     */
    @Select("SELECT dst_ip, COUNT(*) as session_count, SUM(dst_total_bytes) as total_bytes " +
            "FROM dwd_session_logs " +
            "WHERE session_start_time >= #{startTime} AND session_start_time <= #{endTime} " +
            "GROUP BY dst_ip " +
            "ORDER BY session_count DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getTopDestinationIps(@Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime,
                                                   @Param("limit") int limit);

    /**
     * 查询协议分布统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 协议分布统计
     */
    @Select("SELECT protocol, app_name, COUNT(*) as session_count " +
            "FROM dwd_session_logs " +
            "WHERE session_start_time >= #{startTime} AND session_start_time <= #{endTime} " +
            "GROUP BY protocol, app_name " +
            "ORDER BY session_count DESC")
    List<Map<String, Object>> getProtocolDistribution(@Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 查询应用分布统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 应用分布统计
     */
    @Select("SELECT app_name, COUNT(*) as session_count, SUM(src_total_bytes + dst_total_bytes) as total_bytes " +
            "FROM dwd_session_logs " +
            "WHERE session_start_time >= #{startTime} AND session_start_time <= #{endTime} " +
            "AND app_name IS NOT NULL " +
            "GROUP BY app_name " +
            "ORDER BY session_count DESC")
    List<Map<String, Object>> getApplicationDistribution(@Param("startTime") LocalDateTime startTime,
                                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 查询时间范围内的会话趋势
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param interval 时间间隔(分钟)
     * @return 会话趋势数据
     */
    List<Map<String, Object>> getSessionTrend(@Param("startTime") LocalDateTime startTime,
                                             @Param("endTime") LocalDateTime endTime,
                                             @Param("interval") int interval);

    /**
     * 根据关键字搜索会话
     *
     * @param keyword 关键字
     * @param page 分页参数
     * @return 搜索结果
     */
    Page<SessionListResponse> searchSessions(@Param("keyword") String keyword, Page<SessionListResponse> page);
}
