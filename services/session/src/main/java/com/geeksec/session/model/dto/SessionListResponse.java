package com.geeksec.session.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会话列表响应DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Schema(description = "会话列表响应")
public class SessionListResponse {
    
    @Schema(description = "会话ID", example = "session_1234567890")
    private String sessionId;
    
    @Schema(description = "源IP地址", example = "*************")
    private String srcIp;
    
    @Schema(description = "目标IP地址", example = "********")
    private String dstIp;
    
    @Schema(description = "源端口", example = "8080")
    private Integer srcPort;
    
    @Schema(description = "目标端口", example = "443")
    private Integer dstPort;
    
    @Schema(description = "IP协议号", example = "6")
    private Integer protocol;
    
    @Schema(description = "协议名称", example = "TCP")
    private String protocolName;
    
    @Schema(description = "应用名称", example = "HTTPS")
    private String appName;
    
    @Schema(description = "会话开始时间", example = "2024-01-01T12:00:00")
    private LocalDateTime sessionStartTime;
    
    @Schema(description = "会话结束时间", example = "2024-01-01T12:05:30")
    private LocalDateTime sessionEndTime;
    
    @Schema(description = "会话持续时间(秒)", example = "330")
    private Integer sessionDuration;
    
    @Schema(description = "源总字节数", example = "1024000")
    private Long srcTotalBytes;
    
    @Schema(description = "目标总字节数", example = "2048000")
    private Long dstTotalBytes;
    
    @Schema(description = "源负载字节数", example = "512000")
    private Long srcPayloadBytes;
    
    @Schema(description = "目标负载字节数", example = "1024000")
    private Long dstPayloadBytes;
    
    @Schema(description = "源包数", example = "1500")
    private Integer srcPacketCount;
    
    @Schema(description = "目标包数", example = "1200")
    private Integer dstPacketCount;
    
    @Schema(description = "源有负载的包数", example = "800")
    private Integer srcPayloadPacketCount;
    
    @Schema(description = "目标有负载的包数", example = "600")
    private Integer dstPayloadPacketCount;
    
    @Schema(description = "会话标签", example = "[\"恶意软件\", \"可疑流量\"]")
    private List<String> labelNames;
    
    @Schema(description = "标签ID列表", example = "[10, 20]")
    private List<Integer> labelIds;
    
    @Schema(description = "源IP是否为内部IP", example = "true")
    private Boolean srcIsInternal;
    
    @Schema(description = "目标IP是否为内部IP", example = "false")
    private Boolean dstIsInternal;
    
    @Schema(description = "任务ID", example = "1")
    private Integer taskId;
    
    @Schema(description = "设备ID", example = "100")
    private Integer deviceId;
    
    @Schema(description = "首包发送方IP", example = "*************")
    private String firstPacketSender;
    
    @Schema(description = "协议栈信息", example = "{\"tcp\": true, \"http\": true}")
    private Map<String, Object> protocolStack;
    
    @Schema(description = "HTTP协议数量", example = "5")
    private Integer httpProtocolCount;
    
    @Schema(description = "SSL协议数量", example = "2")
    private Integer sslProtocolCount;
    
    @Schema(description = "DNS协议数量", example = "1")
    private Integer dnsProtocolCount;
    
    @Schema(description = "规则级别", example = "3")
    private Integer ruleLevel;
    
    @Schema(description = "规则数量", example = "2")
    private Integer ruleCount;
    
    @Schema(description = "创建时间", example = "2024-01-01T12:00:00")
    private LocalDateTime createTime;
    
    @Schema(description = "更新时间", example = "2024-01-01T12:05:30")
    private LocalDateTime updateTime;
}
