package com.geeksec.session.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 会话管理服务配置属性
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.session")
public class SessionProperties {
    /**
     * 会话数据保留天数（与Doris动态分区配置保持一致）
     */
    private int retentionDays = 90;



    /**
     * 批量处理大小
     */
    @Data
    @ConfigurationProperties(prefix = "app.batch")
    public static class BatchProperties {
        private int size = 1000;
    }

    /**
     * 批量处理配置
     */
    private final BatchProperties batch = new BatchProperties();
}
