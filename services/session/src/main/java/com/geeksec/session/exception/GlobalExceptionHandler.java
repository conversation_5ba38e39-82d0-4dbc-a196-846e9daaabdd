package com.geeksec.session.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.exception.BaseGlobalExceptionHandler;

import lombok.extern.slf4j.Slf4j;

/**
 * Session 服务全局异常处理器
 * 继承自基础异常处理器，添加特定于会话服务的异常处理
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler extends BaseGlobalExceptionHandler {

    /**
     * 处理会话不存在异常
     */
    @ExceptionHandler(SessionNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ApiResponse<Void> handleSessionNotFound(SessionNotFoundException e) {
        log.warn("会话不存在: {}", e.getMessage());
        return ApiResponse.error(HttpStatus.NOT_FOUND.value(), e.getMessage());
    }
}
