package com.geeksec.session.exception;

/**
 * 会话不存在异常
 */
public class SessionNotFoundException extends RuntimeException {
    
    public SessionNotFoundException(String message) {
        super(message);
    }
    
    public SessionNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public static SessionNotFoundException withId(String id) {
        return new SessionNotFoundException("Session not found with id: " + id);
    }
}
