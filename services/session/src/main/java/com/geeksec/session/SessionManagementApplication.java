package com.geeksec.session;

import com.geeksec.session.config.SessionProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 会话管理服务启动类
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@SpringBootApplication
@EnableAsync
@EnableScheduling
@EnableFeignClients
@EnableConfigurationProperties(SessionProperties.class)
@ComponentScan(basePackages = {"com.geeksec.session", "com.geeksec.common"})
public class SessionManagementApplication {
    public static void main(String[] args) {
        SpringApplication.run(SessionManagementApplication.class, args);
    }
}
