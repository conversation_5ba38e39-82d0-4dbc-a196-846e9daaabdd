server:
  port: 0  # 随机端口

spring:
  application:
    name: cert-service-test
  profiles:
    active: test
  
  # 测试数据库配置 - 使用 H2 内存数据库
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    druid:
      initial-size: 1
      min-idle: 1
      max-active: 5
      max-wait: 60000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false

  # 测试 Redis 配置 - 使用嵌入式 Redis
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: 6379
      password: 
      database: 15  # 使用测试专用数据库
      timeout: 6000ms
      lettuce:
        pool:
          max-active: 2
          max-wait: -1ms
          max-idle: 2
          min-idle: 0

  # 测试 Kafka 配置
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      group-id: cert-service-test-group
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      auto-offset-reset: earliest

# MyBatis-Flex 测试配置
mybatis-flex:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.geeksec.cert.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false  # 测试时禁用缓存
    lazy-loading-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # 测试时输出 SQL

# NebulaGraph 测试配置
nebula:
  hosts: ${NEBULA_HOSTS:127.0.0.1:9669}
  username: root
  password: nebula
  space: test_nta_graph
  pool-config:
    min-conns-size: 0
    max-conns-size: 2
    timeout: 0
    idle-time: 0
    interval-idle: -1
    wait-time: 0
    min-cluster-health-rate: 1.0
    enable-ssl: false

# Knife4j 测试配置
knife4j:
  enable: true
  openapi:
    title: NTA 3.0 证书管理服务 API (测试环境)
    description: 证书管理相关的 REST API 接口文档 - 测试环境
    version: 3.0.0-TEST

# Sa-Token 测试配置
sa-token:
  token-name: Authorization
  token-prefix: Bearer
  timeout: 3600  # 测试环境较短的超时时间
  activity-timeout: -1
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-log: false

# 测试日志配置
logging:
  level:
    root: WARN
    com.geeksec.cert: DEBUG
    org.springframework.test: INFO
    org.springframework.boot.test: INFO
    org.springframework.web: DEBUG
    com.mybatisflex: DEBUG
    org.h2: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点测试配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: false  # 测试时禁用 Prometheus

# H2 数据库控制台配置 (仅测试环境)
h2:
  console:
    enabled: true
    path: /h2-console
