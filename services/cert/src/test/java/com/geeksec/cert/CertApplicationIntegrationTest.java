package com.geeksec.cert;

import com.geeksec.cert.dto.CertificateSearchCondition;
import com.geeksec.cert.service.CertificateService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 证书服务集成测试
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.datasource.username=sa",
    "spring.datasource.password=",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "nebula.hosts=127.0.0.1:9669",
    "spring.kafka.bootstrap-servers=localhost:9092",
    "spring.data.redis.host=localhost",
    "spring.data.redis.port=6379"
})
class CertApplicationIntegrationTest {

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private CertificateService certificateService;

    @Test
    void contextLoads() {
        // 测试应用上下文是否正常加载
        assertNotNull(certificateService);
    }

    @Test
    void testHealthEndpoint() {
        // 测试健康检查端点
        ResponseEntity<Map> response = restTemplate.getForEntity(
                "http://localhost:" + port + "/cert/actuator/health", Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("UP", response.getBody().get("status"));
    }

    @Test
    void testCertificateSearchEndpoint() {
        // 测试证书搜索端点
        CertificateSearchCondition condition = new CertificateSearchCondition();
        condition.setPage(1);
        condition.setSize(10);
        
        ResponseEntity<Map> response = restTemplate.postForEntity(
                "http://localhost:" + port + "/cert/search/list", 
                condition, 
                Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().containsKey("data"));
        assertTrue(response.getBody().containsKey("total"));
    }

    @Test
    void testCertificateDetailEndpoint() {
        // 测试证书详情端点
        String certSha1 = "test-cert-sha1";
        String certSource = "test-source";
        
        ResponseEntity<String> response = restTemplate.getForEntity(
                "http://localhost:" + port + "/cert/search/detail?cert_sha1=" + certSha1 + "&cert_source=" + certSource,
                String.class);
        
        // 由于测试数据库中没有数据，期望返回404
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
    }

    @Test
    void testCertificateStatisticsEndpoint() {
        // 测试证书统计端点
        ResponseEntity<Map> response = restTemplate.getForEntity(
                "http://localhost:" + port + "/cert/statistics/overview", 
                Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().containsKey("totalCount"));
    }

    @Test
    void testCertificateFileStatusEndpoint() {
        // 测试证书文件状态端点
        ResponseEntity<Map> response = restTemplate.getForEntity(
                "http://localhost:" + port + "/cert/file/status", 
                Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().containsKey("status"));
    }

    @Test
    void testCertificateLabelRecommendEndpoint() {
        // 测试证书标签推荐端点
        ResponseEntity<Map> response = restTemplate.getForEntity(
                "http://localhost:" + port + "/cert/label/recommend", 
                Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
    }

    @Test
    void testSwaggerUIEndpoint() {
        // 测试 Swagger UI 端点
        ResponseEntity<String> response = restTemplate.getForEntity(
                "http://localhost:" + port + "/cert/doc.html", 
                String.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().contains("Knife4j"));
    }

    @Test
    void testCertificateService() {
        // 测试证书服务基本功能
        assertNotNull(certificateService);
        
        // 测试统计功能
        Map<String, Object> statistics = certificateService.getCertificateStatistics();
        assertNotNull(statistics);
        assertTrue(statistics.containsKey("totalCount"));
        
        // 测试搜索功能
        CertificateSearchCondition condition = new CertificateSearchCondition();
        condition.setPage(1);
        condition.setSize(10);
        
        Map<String, Object> searchResult = certificateService.searchCertificateList(condition);
        assertNotNull(searchResult);
        assertTrue(searchResult.containsKey("data"));
        assertTrue(searchResult.containsKey("total"));
    }
}
