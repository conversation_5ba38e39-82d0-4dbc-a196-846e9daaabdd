package com.geeksec.cert.repository;

import com.geeksec.cert.entity.DimCertificate;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

import static com.geeksec.cert.entity.table.DimCertificateTableDef.DIM_CERTIFICATE;

/**
 * Doris dim_cert 表数据访问接口
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Mapper
public interface DimCertificateRepository extends BaseMapper<DimCertificate> {

    /**
     * 根据证书SHA1哈希查询证书元数据
     *
     * @param derSha1 证书DER编码的SHA1哈希
     * @return 证书元数据
     */
    default DimCertificate selectByDerSha1(String derSha1) {
        return selectOneByQuery(QueryWrapper.create()
                .where(DIM_CERTIFICATE.DER_SHA1.eq(derSha1)));
    }

    /**
     * 根据证书SHA1哈希列表批量查询证书元数据
     *
     * @param derSha1List 证书SHA1哈希列表
     * @return 证书元数据列表
     */
    default List<DimCertificate> selectByDerSha1List(List<String> derSha1List) {
        if (derSha1List == null || derSha1List.isEmpty()) {
            return List.of();
        }
        return selectListByQuery(QueryWrapper.create()
                .where(DIM_CERTIFICATE.DER_SHA1.in(derSha1List)));
    }

    /**
     * 根据通用名称查询证书
     *
     * @param commonName 通用名称
     * @return 证书列表
     */
    default List<DimCertificate> selectByCommonName(String commonName) {
        return selectListByQuery(QueryWrapper.create()
                .where(DIM_CERTIFICATE.COMMON_NAME.eq(commonName)));
    }

    /**
     * 根据颁发者ID查询证书
     *
     * @param issuerId 颁发者ID
     * @return 证书列表
     */
    default List<DimCertificate> selectByIssuerId(String issuerId) {
        return selectListByQuery(QueryWrapper.create()
                .where(DIM_CERTIFICATE.ISSUER_ID.eq(issuerId)));
    }

    /**
     * 根据主题ID查询证书
     *
     * @param subjectId 主题ID
     * @return 证书列表
     */
    default List<DimCertificate> selectBySubjectId(String subjectId) {
        return selectListByQuery(QueryWrapper.create()
                .where(DIM_CERTIFICATE.SUBJECT_ID.eq(subjectId)));
    }

    /**
     * 查询即将过期的证书
     *
     * @param days 天数
     * @param limit 限制数量
     * @return 证书列表
     */
    @Select("SELECT * FROM dim_cert WHERE not_after BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{days} DAY) ORDER BY not_after ASC LIMIT #{limit}")
    List<DimCertificate> selectExpiringCertificates(@Param("days") Integer days, @Param("limit") Integer limit);

    /**
     * 查询最近活跃的证书
     *
     * @param days 天数
     * @param limit 限制数量
     * @return 证书列表
     */
    @Select("SELECT * FROM dim_cert WHERE last_seen >= DATE_SUB(NOW(), INTERVAL #{days} DAY) ORDER BY last_seen DESC LIMIT #{limit}")
    List<DimCertificate> selectRecentActiveCertificates(@Param("days") Integer days, @Param("limit") Integer limit);

    /**
     * 根据威胁评分查询证书
     *
     * @param minThreatScore 最小威胁评分
     * @param limit 限制数量
     * @return 证书列表
     */
    default List<DimCertificate> selectByThreatScore(Integer minThreatScore, Integer limit) {
        return selectListByQuery(QueryWrapper.create()
                .where(DIM_CERTIFICATE.THREAT_SCORE.ge(minThreatScore))
                .orderBy(DIM_CERTIFICATE.THREAT_SCORE.desc())
                .limit(limit));
    }

    /**
     * 根据关联域名查询证书
     *
     * @param domain 域名
     * @return 证书列表
     */
    @Select("SELECT * FROM dim_cert WHERE JSON_CONTAINS(associated_domains, JSON_QUOTE(#{domain}))")
    List<DimCertificate> selectByAssociatedDomain(@Param("domain") String domain);

    /**
     * 根据关联IP查询证书
     *
     * @param ip IP地址
     * @return 证书列表
     */
    @Select("SELECT * FROM dim_cert WHERE JSON_CONTAINS(associated_ips, JSON_QUOTE(#{ip}))")
    List<DimCertificate> selectByAssociatedIp(@Param("ip") String ip);

    /**
     * 统计证书总数
     *
     * @return 证书总数
     */
    @Select("SELECT COUNT(*) FROM dim_cert")
    Long countTotal();

    /**
     * 统计威胁证书数量
     *
     * @param minThreatScore 最小威胁评分
     * @return 威胁证书数量
     */
    @Select("SELECT COUNT(*) FROM dim_cert WHERE threat_score >= #{minThreatScore}")
    Long countThreatCertificates(@Param("minThreatScore") Integer minThreatScore);

    /**
     * 统计即将过期的证书数量
     *
     * @param days 天数
     * @return 即将过期的证书数量
     */
    @Select("SELECT COUNT(*) FROM dim_cert WHERE not_after BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{days} DAY)")
    Long countExpiringCertificates(@Param("days") Integer days);

    /**
     * 根据时间范围查询证书
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 证书列表
     */
    default List<DimCertificate> selectByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        return selectListByQuery(QueryWrapper.create()
                .where(DIM_CERTIFICATE.FIRST_SEEN.between(startTime, endTime))
                .orderBy(DIM_CERTIFICATE.FIRST_SEEN.desc())
                .limit(limit));
    }

    /**
     * 根据组织查询证书
     *
     * @param organization 组织名称
     * @return 证书列表
     */
    default List<DimCertificate> selectByOrganization(String organization) {
        return selectListByQuery(QueryWrapper.create()
                .where(DIM_CERTIFICATE.ORGANIZATION.like(organization)));
    }

    /**
     * 查询自签名证书
     *
     * @param limit 限制数量
     * @return 自签名证书列表
     */
    @Select("SELECT * FROM dim_cert WHERE issuer_id = subject_id LIMIT #{limit}")
    List<DimCertificate> selectSelfSignedCertificates(@Param("limit") Integer limit);
}
