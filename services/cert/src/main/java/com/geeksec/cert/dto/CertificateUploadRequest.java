package com.geeksec.cert.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotNull;

/**
 * 证书文件上传请求
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@Schema(description = "证书文件上传请求")
public class CertificateUploadRequest {

    /**
     * 证书文件
     */
    @Schema(description = "证书文件", required = true)
    @JsonProperty("cert_file")
    @NotNull(message = "证书文件不能为空")
    private MultipartFile certFile;

    /**
     * 任务ID
     */
    @Schema(description = "任务ID")
    @JsonProperty("task_id")
    private Integer taskId;

    /**
     * 上传备注
     */
    @Schema(description = "上传备注")
    @JsonProperty("remark")
    private String remark;
}
