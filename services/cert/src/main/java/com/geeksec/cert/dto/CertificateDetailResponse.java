package com.geeksec.cert.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 证书详情响应
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@Schema(description = "证书详情响应")
public class CertificateDetailResponse {

    /**
     * 证书基础信息
     */
    @Schema(description = "证书基础信息")
    @JsonProperty("cert_info")
    private CertificateDto certInfo;

    /**
     * 证书标签列表
     */
    @Schema(description = "证书标签列表")
    @JsonProperty("cert_tags")
    private List<LabelDto> certTags;

    /**
     * 证书关联信息
     */
    @Schema(description = "证书关联信息")
    @JsonProperty("related_info")
    private CertificateRelatedDto relatedInfo;

    /**
     * 证书扩展信息
     */
    @Schema(description = "证书扩展信息")
    @JsonProperty("extended_info")
    private Map<String, Object> extendedInfo;

    /**
     * 标签数据传输对象
     */
    @Data
    @Schema(description = "标签信息")
    public static class LabelDto {

        /**
         * 标签ID
         */
        @Schema(description = "标签ID")
        @JsonProperty("label_id")
        private Integer labelId;

        /**
         * 标签名称
         */
        @Schema(description = "标签名称")
        @JsonProperty("label_name")
        private String labelName;

        /**
         * 标签显示名称
         */
        @Schema(description = "标签显示名称")
        @JsonProperty("display_name")
        private String displayName;

        /**
         * 标签描述
         */
        @Schema(description = "标签描述")
        @JsonProperty("description")
        private String description;

        /**
         * 威胁等级
         */
        @Schema(description = "威胁等级")
        @JsonProperty("threat_level")
        private Integer threatLevel;

        /**
         * 信任等级
         */
        @Schema(description = "信任等级")
        @JsonProperty("trust_level")
        private Integer trustLevel;

        /**
         * 标签等级 (danger, warning, success, info)
         */
        @Schema(description = "标签等级")
        @JsonProperty("label_level")
        private String labelLevel;

        /**
         * 标签颜色
         */
        @Schema(description = "标签颜色")
        @JsonProperty("color")
        private String color;
    }
}
