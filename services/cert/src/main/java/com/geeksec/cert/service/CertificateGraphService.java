package com.geeksec.cert.service;

import com.geeksec.cert.dto.graph.GraphNextCondition;
import com.geeksec.cert.dto.graph.GraphPropertiesCondition;
import com.geeksec.cert.dto.graph.VertexEdgeDto;
import com.geeksec.cert.entity.graph.CertificateVertex;

import java.util.List;
import java.util.Map;

/**
 * 证书图数据库服务接口
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
public interface CertificateGraphService {

    /**
     * 获取证书的关联查询结果
     * 
     * @param certId 证书ID
     * @return 关联查询结果
     */
    List<VertexEdgeDto> getCertificateAssociations(String certId);

    /**
     * 获取证书的分页关联查询结果
     * 
     * @param condition 查询条件
     * @return 分页关联查询结果
     */
    List<VertexEdgeDto> getCertificateAssociationsNext(GraphNextCondition condition);

    /**
     * 根据属性条件查询证书关联
     * 
     * @param condition 属性查询条件
     * @return 关联查询结果
     */
    List<VertexEdgeDto> getCertificateAssociationsByProperties(GraphPropertiesCondition condition);

    /**
     * 获取证书的详细信息
     * 
     * @param certIds 证书ID列表
     * @return 证书详细信息列表
     */
    List<CertificateVertex> getCertificateDetails(List<String> certIds);

    /**
     * 获取证书关联的IP列表
     * 
     * @param certIds 证书ID列表
     * @return 关联IP信息
     */
    List<Map<String, Object>> getCertificateRelatedIps(List<String> certIds);

    /**
     * 统计证书关联的域名数量
     * 
     * @param certIds 证书ID列表
     * @return 域名数量统计
     */
    List<Map<String, Object>> countCertificateRelatedDomains(List<String> certIds);

    /**
     * 统计证书关联的IP数量
     * 
     * @param certIds 证书ID列表
     * @return IP数量统计
     */
    List<Map<String, Object>> countCertificateRelatedIps(List<String> certIds);

    /**
     * 获取证书信任链
     * 
     * @param certId 证书ID
     * @return 证书信任链
     */
    List<CertificateVertex> getCertificateTrustChain(String certId);

    /**
     * 获取证书的子证书
     * 
     * @param certId 证书ID
     * @return 子证书列表
     */
    List<CertificateVertex> getCertificateChildren(String certId);

    /**
     * 查找相似证书
     * 
     * @param certId 证书ID
     * @param similarity 相似度阈值
     * @param limit 限制数量
     * @return 相似证书列表
     */
    List<CertificateVertex> findSimilarCertificates(String certId, Double similarity, Integer limit);

    /**
     * 查找证书间的信任路径
     * 
     * @param fromCertId 起始证书ID
     * @param toCertId 目标证书ID
     * @param maxHops 最大跳数
     * @return 信任路径
     */
    List<List<CertificateVertex>> findTrustPaths(String fromCertId, String toCertId, Integer maxHops);

    /**
     * 分析证书风险传播
     * 
     * @param certId 证书ID
     * @param maxHops 最大跳数
     * @param threatThreshold 威胁阈值
     * @return 风险传播路径
     */
    List<VertexEdgeDto> analyzeRiskPropagation(String certId, Integer maxHops, Integer threatThreshold);

    /**
     * 获取证书关联实体统计
     * 
     * @param certId 证书ID
     * @return 关联实体统计
     */
    Map<String, Long> getCertificateRelationStatistics(String certId);

    /**
     * 获取证书时间线关联
     * 
     * @param certId 证书ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 时间线关联数据
     */
    List<VertexEdgeDto> getCertificateTimelineAssociations(String certId, Long startTime, Long endTime);

    /**
     * 同步证书到图数据库
     * 
     * @param certificates 证书列表
     * @return 同步结果
     */
    boolean syncCertificatesToGraph(List<CertificateVertex> certificates);

    /**
     * 创建证书关联关系
     * 
     * @param fromCertId 起始证书ID
     * @param toCertId 目标证书ID
     * @param relationType 关系类型
     * @param properties 关系属性
     * @return 创建结果
     */
    boolean createCertificateRelation(String fromCertId, String toCertId, String relationType, Map<String, Object> properties);

    /**
     * 删除证书关联关系
     * 
     * @param fromCertId 起始证书ID
     * @param toCertId 目标证书ID
     * @param relationType 关系类型
     * @return 删除结果
     */
    boolean deleteCertificateRelation(String fromCertId, String toCertId, String relationType);

    /**
     * 更新证书图数据库属性
     * 
     * @param certId 证书ID
     * @param properties 属性映射
     * @return 更新结果
     */
    boolean updateCertificateGraphProperties(String certId, Map<String, Object> properties);

    /**
     * 从图数据库删除证书
     * 
     * @param certId 证书ID
     * @return 删除结果
     */
    boolean deleteCertificateFromGraph(String certId);

    /**
     * 检查证书是否存在于图数据库
     * 
     * @param certId 证书ID
     * @return 是否存在
     */
    boolean existsInGraph(String certId);

    /**
     * 获取图数据库中的证书总数
     * 
     * @return 证书总数
     */
    long countCertificatesInGraph();

    /**
     * 获取证书的邻居节点
     * 
     * @param certId 证书ID
     * @param edgeTypes 边类型列表
     * @param limit 限制数量
     * @return 邻居节点列表
     */
    List<VertexEdgeDto> getCertificateNeighbors(String certId, List<String> edgeTypes, Integer limit);

    /**
     * 执行证书图谱分析
     * 
     * @param certId 证书ID
     * @param analysisType 分析类型
     * @param parameters 分析参数
     * @return 分析结果
     */
    Map<String, Object> performGraphAnalysis(String certId, String analysisType, Map<String, Object> parameters);
}
