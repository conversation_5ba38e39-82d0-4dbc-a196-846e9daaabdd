package com.geeksec.cert.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 证书实体类 - 对应图数据库中的证书节点
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table("cert")
public class Certificate {

    /**
     * 证书ID (SHA1哈希值)
     */
    @Id
    @Column("cert_id")
    private String certId;

    /**
     * 首次出现时间
     */
    @Column("first_seen")
    private LocalDateTime firstSeen;

    /**
     * 末次出现时间
     */
    @Column("last_seen")
    private LocalDateTime lastSeen;

    /**
     * 威胁等级 (0-100)
     */
    @Column("threat_level")
    private Integer threatLevel;

    /**
     * 信任等级 (0-100)
     */
    @Column("trust_level")
    private Integer trustLevel;

    /**
     * 备注信息
     */
    @Column("remark")
    private String remark;

    /**
     * 创建时间
     */
    @Column("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column("updated_at")
    private LocalDateTime updatedAt;
}
