package com.geeksec.cert.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Doris dim_cert 表实体类 - 证书元数据
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table("dim_cert")
public class DimCertificate {

    /**
     * 证书DER编码的SHA1哈希 (主键)
     */
    @Id
    @Column("der_sha1")
    private String derSha1;

    /**
     * 证书DER编码的MD5哈希
     */
    @Column("der_md5")
    private String derMd5;

    /**
     * 证书DER编码的SHA256哈希
     */
    @Column("der_sha256")
    private String derSha256;

    /**
     * PEM格式MD5哈希
     */
    @Column("pem_md5")
    private String pemMd5;

    /**
     * PEM格式SHA256哈希
     */
    @Column("pem_sha256")
    private String pemSha256;

    /**
     * PEM格式SHA1哈希
     */
    @Column("pem_sha1")
    private String pemSha1;

    /**
     * 证书版本
     */
    @Column("version")
    private String version;

    /**
     * 证书序列号
     */
    @Column("serial_number")
    private String serialNumber;

    /**
     * 证书格式
     */
    @Column("format")
    private String format;

    /**
     * 颁发者ID
     */
    @Column("issuer_id")
    private String issuerId;

    /**
     * 主题ID
     */
    @Column("subject_id")
    private String subjectId;

    /**
     * 证书生效时间
     */
    @Column("not_before")
    private LocalDateTime notBefore;

    /**
     * 证书过期时间
     */
    @Column("not_after")
    private LocalDateTime notAfter;

    /**
     * 证书有效期长度
     */
    @Column("duration")
    private Long duration;

    /**
     * 导入时间
     */
    @Column("import_time")
    private LocalDateTime importTime;

    /**
     * 通用名称(CN)
     */
    @Column("common_name")
    private String commonName;

    /**
     * 主题备用名称(SAN)
     */
    @Column("subject_alt_names")
    private List<String> subjectAltNames;

    /**
     * 颁发者备用名称
     */
    @Column("issuer_alt_names")
    private List<String> issuerAltNames;

    /**
     * 公钥信息
     */
    @Column("public_key")
    private String publicKey;

    /**
     * 公钥算法
     */
    @Column("public_key_algorithm")
    private String publicKeyAlgorithm;

    /**
     * 公钥长度
     */
    @Column("public_key_length")
    private String publicKeyLength;

    /**
     * 公钥参数
     */
    @Column("public_key_parameter")
    private String publicKeyParameter;

    /**
     * SPKI SHA256
     */
    @Column("spki_sha256")
    private String spkiSha256;

    /**
     * 签名算法
     */
    @Column("signature_algorithm")
    private String signatureAlgorithm;

    /**
     * 签名算法名称
     */
    @Column("signature_alg_name")
    private String signatureAlgName;

    /**
     * 签名算法OID
     */
    @Column("signature_alg_oid")
    private String signatureAlgOid;

    /**
     * 密钥用途
     */
    @Column("key_usage")
    private String keyUsage;

    /**
     * 扩展密钥用途
     */
    @Column("extended_key_usage")
    private List<String> extendedKeyUsage;

    /**
     * 基本约束
     */
    @Column("basic_constraints")
    private String basicConstraints;

    /**
     * 授权密钥标识符
     */
    @Column("authority_key_identifier")
    private String authorityKeyIdentifier;

    /**
     * 主题密钥标识符
     */
    @Column("subject_key_identifier")
    private String subjectKeyIdentifier;

    /**
     * CRL分发点
     */
    @Column("crl_distribution_points")
    private List<String> crlDistributionPoints;

    /**
     * 授权信息访问
     */
    @Column("authority_info_access")
    private List<String> authorityInfoAccess;

    /**
     * 主题信息访问
     */
    @Column("subject_info_access")
    private List<String> subjectInfoAccess;

    /**
     * 证书策略
     */
    @Column("cert_policies")
    private List<String> certPolicies;

    /**
     * 证书来源
     */
    @Column("source")
    private Integer source;

    /**
     * 用户类型
     */
    @Column("user_type")
    private String userType;

    /**
     * 业务类型
     */
    @Column("business_type")
    private String businessType;

    /**
     * CA类型
     */
    @Column("ca_type")
    private String caType;

    /**
     * 行业类型
     */
    @Column("industry_type")
    private String industryType;

    /**
     * 主题地区
     */
    @Column("subject_area")
    private String subjectArea;

    /**
     * 颁发者地区
     */
    @Column("issuer_area")
    private String issuerArea;

    /**
     * 是否受信任证书
     */
    @Column("is_trusted")
    private Boolean isTrusted;

    /**
     * 是否成功解析
     */
    @Column("is_parsed_successfully")
    private Boolean isParsedSuccessfully;

    /**
     * 是否为损坏证书
     */
    @Column("is_corrupted")
    private Boolean isCorrupted;

    /**
     * 证书出现次数
     */
    @Column("cert_occurrence_count")
    private Integer certOccurrenceCount;

    /**
     * 威胁评分
     */
    @Column("threat_score")
    private Integer threatScore;

    /**
     * 信任评分
     */
    @Column("trust_score")
    private Integer trustScore;

    /**
     * 威胁等级
     */
    @Column("threat_level")
    private String threatLevel;

    /**
     * 标签ID数组
     */
    @Column("labels")
    private List<Integer> labels;

    /**
     * 关联域名
     */
    @Column("associated_domains")
    private List<String> associatedDomains;

    /**
     * 关联IP
     */
    @Column("associated_ips")
    private List<String> associatedIps;

    /**
     * 关联组织
     */
    @Column("organization")
    private String organization;

    /**
     * 首次出现时间
     */
    @Column("first_seen")
    private LocalDateTime firstSeen;

    /**
     * 最后出现时间
     */
    @Column("last_seen")
    private LocalDateTime lastSeen;

    /**
     * 记录创建时间
     */
    @Column("create_time")
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    @Column("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Column("remark")
    private String remark;
}
