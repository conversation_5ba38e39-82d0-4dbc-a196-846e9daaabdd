package com.geeksec.cert.dto.graph;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;
import java.util.Map;

/**
 * 图数据库分页查询条件
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@Schema(description = "图数据库分页查询条件")
public class GraphNextCondition {

    /**
     * 起始顶点ID
     */
    @Schema(description = "起始顶点ID", required = true)
    @JsonProperty("start_vertex_id")
    private String startVertexId;

    /**
     * 起始顶点类型
     */
    @Schema(description = "起始顶点类型")
    @JsonProperty("start_vertex_type")
    private String startVertexType;

    /**
     * 边类型列表
     */
    @Schema(description = "边类型列表")
    @JsonProperty("edge_types")
    private List<String> edgeTypes;

    /**
     * 目标顶点类型列表
     */
    @Schema(description = "目标顶点类型列表")
    @JsonProperty("target_vertex_types")
    private List<String> targetVertexTypes;

    /**
     * 查询方向 (IN/OUT/BOTH)
     */
    @Schema(description = "查询方向", allowableValues = {"IN", "OUT", "BOTH"})
    @JsonProperty("direction")
    private String direction = "BOTH";

    /**
     * 跳数限制
     */
    @Schema(description = "跳数限制", example = "2")
    @JsonProperty("max_hops")
    @Min(1) @Max(5)
    private Integer maxHops = 1;

    /**
     * 页码
     */
    @Schema(description = "页码", example = "1")
    @JsonProperty("page")
    @Min(1)
    private Integer page = 1;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "20")
    @JsonProperty("size")
    @Min(1) @Max(1000)
    private Integer size = 20;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段")
    @JsonProperty("sort_field")
    private String sortField;

    /**
     * 排序方向 (ASC/DESC)
     */
    @Schema(description = "排序方向", allowableValues = {"ASC", "DESC"})
    @JsonProperty("sort_order")
    private String sortOrder = "DESC";

    /**
     * 过滤条件
     */
    @Schema(description = "过滤条件")
    @JsonProperty("filters")
    private Map<String, Object> filters;

    /**
     * 是否包含边属性
     */
    @Schema(description = "是否包含边属性")
    @JsonProperty("include_edge_properties")
    private Boolean includeEdgeProperties = true;

    /**
     * 是否包含顶点属性
     */
    @Schema(description = "是否包含顶点属性")
    @JsonProperty("include_vertex_properties")
    private Boolean includeVertexProperties = true;

    /**
     * 权重阈值
     */
    @Schema(description = "权重阈值")
    @JsonProperty("weight_threshold")
    private Double weightThreshold;

    /**
     * 时间范围开始
     */
    @Schema(description = "时间范围开始")
    @JsonProperty("time_start")
    private Long timeStart;

    /**
     * 时间范围结束
     */
    @Schema(description = "时间范围结束")
    @JsonProperty("time_end")
    private Long timeEnd;

    /**
     * 是否去重
     */
    @Schema(description = "是否去重")
    @JsonProperty("distinct")
    private Boolean distinct = true;

    /**
     * 聚合类型
     */
    @Schema(description = "聚合类型")
    @JsonProperty("aggregation_type")
    private String aggregationType;

    /**
     * 聚合字段
     */
    @Schema(description = "聚合字段")
    @JsonProperty("aggregation_field")
    private String aggregationField;
}
