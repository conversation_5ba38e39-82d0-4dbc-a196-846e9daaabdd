package com.geeksec.cert.service;

import com.geeksec.cert.dto.CertificateDetailResponse;

import java.util.List;
import java.util.Map;

/**
 * 证书标签服务接口
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
public interface CertificateLabelService {

    /**
     * 获取证书标签搜索列表
     * 
     * @param params 查询参数
     * @return 搜索结果
     */
    Map<String, Object> getCertificateLabelSearchList(Map<String, Object> params);

    /**
     * 获取标签详情
     * 
     * @param labelId 标签ID
     * @return 标签详情
     */
    Map<String, Object> getLabelDetail(Integer labelId);

    /**
     * 获取推荐标签列表
     * 
     * @return 推荐标签列表
     */
    Map<String, Object> getRecommendLabelList();

    /**
     * 添加证书标签
     * 
     * @param params 标签参数
     * @return 添加结果
     */
    Map<String, Object> addCertificateLabel(Map<String, Object> params);

    /**
     * 更新证书标签推荐
     * 
     * @param queryConditions 查询条件
     */
    void updateCertificateLabelRecommend(List<Object> queryConditions);

    /**
     * 获取证书的所有标签
     * 
     * @param certId 证书ID
     * @return 标签列表
     */
    List<CertificateDetailResponse.LabelDto> getCertificateLabels(String certId);

    /**
     * 为证书添加标签
     * 
     * @param certId 证书ID
     * @param labelIds 标签ID列表
     * @return 添加结果
     */
    boolean addLabelsToCertificate(String certId, List<Integer> labelIds);

    /**
     * 从证书移除标签
     * 
     * @param certId 证书ID
     * @param labelIds 标签ID列表
     * @return 移除结果
     */
    boolean removeLabelsFromCertificate(String certId, List<Integer> labelIds);

    /**
     * 获取标签的证书数量统计
     * 
     * @param labelId 标签ID
     * @return 证书数量
     */
    long getCertificateCountByLabel(Integer labelId);

    /**
     * 获取热门标签列表
     * 
     * @param limit 限制数量
     * @return 热门标签列表
     */
    List<CertificateDetailResponse.LabelDto> getPopularLabels(Integer limit);

    /**
     * 根据标签类别获取标签列表
     * 
     * @param category 标签类别
     * @return 标签列表
     */
    List<CertificateDetailResponse.LabelDto> getLabelsByCategory(String category);

    /**
     * 搜索标签
     * 
     * @param keyword 关键字
     * @param limit 限制数量
     * @return 标签列表
     */
    List<CertificateDetailResponse.LabelDto> searchLabels(String keyword, Integer limit);
}
