package com.geeksec.cert.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 证书实体表定义类
 *
 * 由 MyBatis-Flex 注解处理器生成，用于类型安全的查询构建
 *
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
public class CertificateTableDef extends TableDef {

    /**
     * 证书实体表定义实例
     */
    public static final CertificateTableDef CERTIFICATE = new CertificateTableDef();

    /**
     * 证书ID - 主键
     */
    public final QueryColumn CERT_ID = new QueryColumn(this, "cert_id");

    /**
     * 首次发现时间
     */
    public final QueryColumn FIRST_SEEN = new QueryColumn(this, "first_seen");

    /**
     * 最后发现时间
     */
    public final QueryColumn LAST_SEEN = new QueryColumn(this, "last_seen");

    /**
     * 威胁等级
     */
    public final QueryColumn THREAT_LEVEL = new QueryColumn(this, "threat_level");

    /**
     * 信任等级
     */
    public final QueryColumn TRUST_LEVEL = new QueryColumn(this, "trust_level");

    /**
     * 备注信息
     */
    public final QueryColumn REMARK = new QueryColumn(this, "remark");

    /**
     * 创建时间
     */
    public final QueryColumn CREATED_AT = new QueryColumn(this, "created_at");

    /**
     * 更新时间
     */
    public final QueryColumn UPDATED_AT = new QueryColumn(this, "updated_at");

    /**
     * 构造函数
     */
    public CertificateTableDef() {
        super("", "cert");
    }

    /**
     * 带别名的构造函数
     */
    public CertificateTableDef(String alias) {
        super("", "cert", alias);
    }
}
