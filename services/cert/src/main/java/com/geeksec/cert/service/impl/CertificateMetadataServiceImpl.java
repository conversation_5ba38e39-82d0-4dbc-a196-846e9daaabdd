package com.geeksec.cert.service.impl;

import com.geeksec.cert.enums.CertificateType;
import com.geeksec.cert.service.CertificateMetadataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 证书元数据服务实现类
 * 专注于证书类型、情况类型、标签等元数据，不包含统计数据
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
public class CertificateMetadataServiceImpl implements CertificateMetadataService {

    // ==================== 证书类型相关实现 ====================

    @Override
    @Cacheable(value = "certificateMetadata", key = "'types'")
    public List<CertificateType> getAllCertificateTypes() {
        log.debug("获取所有证书类型");
        return Arrays.asList(CertificateType.values());
    }

    @Override
    public CertificateType getCertificateTypeByCode(int code) {
        log.debug("根据代码获取证书类型: {}", code);
        return CertificateType.fromCode(code);
    }

    // ==================== 证书情况类型相关实现 ====================

    @Override
    @Cacheable(value = "certificateMetadata", key = "'situationTypes'")
    public Map<String, String> getAllCertificateSituationTypes() {
        log.debug("获取所有证书情况类型");
        Map<String, String> situationTypes = new LinkedHashMap<>();
        situationTypes.put("0", "证书数量");
        situationTypes.put("1", "用户导入证书标签");
        situationTypes.put("2", "证书算法和密钥长度数量分布");
        situationTypes.put("3", "基于签发机构的证书分布");
        situationTypes.put("4", "基于黑白名单权值的证书分布");
        return situationTypes;
    }

    @Override
    public String getCertificateSituationTypeByCode(String code) {
        log.debug("根据代码获取证书情况类型: {}", code);
        String type = getAllCertificateSituationTypes().get(code);
        if (type == null) {
            throw new IllegalArgumentException("未知的证书情况类型代码: " + code);
        }
        return type;
    }

    // ==================== 证书标签相关实现 ====================

    @Override
    @Cacheable(value = "certificateMetadata", key = "'certificateSigns'")
    public Map<String, Object> getAllCertificateSigns() {
        log.debug("获取所有证书标签");
        Map<String, Object> certSigns = new LinkedHashMap<>();

        // 这里可以从数据库或其他数据源获取证书标签
        // 暂时返回空映射，实际实现时需要根据业务需求填充

        return certSigns;
    }

    @Override
    public Object getCertificateSignByKey(String key) {
        log.debug("根据键获取证书标签: {}", key);
        return getAllCertificateSigns().get(key);
    }

    // ==================== 缓存管理实现 ====================

    @Override
    @CacheEvict(value = "certificateMetadata", allEntries = true)
    public void clearCertificateCache() {
        log.info("清除证书元数据缓存");
    }

    @Override
    public void initCertificateCache() {
        log.info("初始化证书元数据缓存");
        // 预热缓存
        getAllCertificateTypes();
        getAllCertificateSituationTypes();
        getAllCertificateSigns();
    }
}
