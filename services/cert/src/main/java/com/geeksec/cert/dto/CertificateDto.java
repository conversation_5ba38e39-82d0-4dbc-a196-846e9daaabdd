package com.geeksec.cert.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 证书数据传输对象
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@Schema(description = "证书信息")
public class CertificateDto {

    /**
     * 证书ID (SHA1哈希值)
     */
    @Schema(description = "证书ID (SHA1哈希值)")
    @JsonProperty("cert_id")
    private String certId;

    /**
     * 首次出现时间
     */
    @Schema(description = "首次出现时间")
    @JsonProperty("first_seen")
    private LocalDateTime firstSeen;

    /**
     * 末次出现时间
     */
    @Schema(description = "末次出现时间")
    @JsonProperty("last_seen")
    private LocalDateTime lastSeen;

    /**
     * 威胁等级 (0-100)
     */
    @Schema(description = "威胁等级 (0-100)")
    @JsonProperty("threat_level")
    private Integer threatLevel;

    /**
     * 信任等级 (0-100)
     */
    @Schema(description = "信任等级 (0-100)")
    @JsonProperty("trust_level")
    private Integer trustLevel;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    @JsonProperty("remark")
    private String remark;

    /**
     * 备注列表
     */
    @Schema(description = "备注列表")
    @JsonProperty("remarks")
    private List<String> remarks;

    /**
     * 父证书集合
     */
    @Schema(description = "父证书集合")
    @JsonProperty("father_id_list")
    private List<String> fatherIdList;

    /**
     * 标签ID列表
     */
    @Schema(description = "标签ID列表")
    @JsonProperty("labels")
    private List<Integer> labels;

    /**
     * 服务器热度
     */
    @Schema(description = "服务器热度")
    @JsonProperty("server_heat")
    private Integer serverHeat;

    /**
     * 客户端热度
     */
    @Schema(description = "客户端热度")
    @JsonProperty("client_heat")
    private Integer clientHeat;

    /**
     * 签发机构
     */
    @Schema(description = "签发机构")
    @JsonProperty("issuer_o")
    private String issuerO;

    /**
     * 所有者机构
     */
    @Schema(description = "所有者机构")
    @JsonProperty("subject_o")
    private String subjectO;

    /**
     * 颁发时间
     */
    @Schema(description = "颁发时间")
    @JsonProperty("not_before")
    private String notBefore;

    /**
     * 有效时间
     */
    @Schema(description = "有效时间")
    @JsonProperty("not_after")
    private String notAfter;

    /**
     * 出现任务名称集合
     */
    @Schema(description = "出现任务名称集合")
    @JsonProperty("task_names")
    private List<String> taskNames;

    /**
     * 证书来源
     */
    @Schema(description = "证书来源")
    @JsonProperty("cert_source")
    private String certSource;

    /**
     * 是否为错误证书
     */
    @Schema(description = "是否为错误证书")
    @JsonProperty("error_cert")
    private Boolean errorCert;

    /**
     * 纠错之后正确证书的SHA1
     */
    @Schema(description = "纠错之后正确证书的SHA1")
    @JsonProperty("correct_asn1_sha1")
    private String correctAsn1Sha1;
}
