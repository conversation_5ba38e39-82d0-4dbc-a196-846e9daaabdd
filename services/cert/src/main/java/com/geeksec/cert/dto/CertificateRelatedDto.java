package com.geeksec.cert.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 证书关联查询数据传输对象
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@Schema(description = "证书关联查询信息")
public class CertificateRelatedDto {

    /**
     * 证书ID
     */
    @Schema(description = "证书ID")
    @JsonProperty("cert_id")
    private String certId;

    /**
     * 关联IP列表
     */
    @Schema(description = "关联IP列表")
    @JsonProperty("ip_list")
    private List<String> ipList;

    /**
     * 关联域名数量
     */
    @Schema(description = "关联域名数量")
    @JsonProperty("domain_count")
    private Integer domainCount;

    /**
     * 首次出现时间
     */
    @Schema(description = "首次出现时间")
    @JsonProperty("first_time")
    private Long firstTime;

    /**
     * 末次出现时间
     */
    @Schema(description = "末次出现时间")
    @JsonProperty("last_time")
    private Long lastTime;
}
