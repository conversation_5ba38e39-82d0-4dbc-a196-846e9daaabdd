package com.geeksec.cert.controller;

import com.geeksec.cert.service.CertificateLabelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.Map;

/**
 * 证书标签控制器
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/label")
@RequiredArgsConstructor
@Validated
@Tag(name = "证书标签", description = "证书标签管理相关接口")
public class CertificateLabelController {

    private final CertificateLabelService certificateLabelService;

    /**
     * 证书标签列表选择库列表
     */
    @PostMapping("/search")
    @Operation(summary = "证书标签搜索", description = "获取证书标签搜索列表")
    public ResponseEntity<Map<String, Object>> getLabelSearchList(
            @Valid @RequestBody Map<String, Object> params) {
        log.info("证书标签搜索请求, params: {}", params);
        
        if (params == null || params.isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        
        try {
            Map<String, Object> result = certificateLabelService.getCertificateLabelSearchList(params);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("证书标签搜索失败, error: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取标签详情
     */
    @GetMapping("/detail")
    @Operation(summary = "获取标签详情", description = "根据标签ID获取详细信息")
    public ResponseEntity<Map<String, Object>> getLabelDetail(
            @Parameter(description = "标签ID", required = true)
            @RequestParam("label_id") @NotNull Integer labelId) {
        log.info("获取标签详情请求, labelId: {}", labelId);
        
        try {
            Map<String, Object> result = certificateLabelService.getLabelDetail(labelId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取标签详情失败, labelId: {}, error: ", labelId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取标签推荐数据
     */
    @GetMapping("/recommend")
    @Operation(summary = "获取推荐标签", description = "获取推荐的标签列表")
    public ResponseEntity<Map<String, Object>> getRecommendLabelList() {
        log.info("获取推荐标签请求");
        
        try {
            Map<String, Object> result = certificateLabelService.getRecommendLabelList();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取推荐标签失败, error: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 添加证书标签
     */
    @PostMapping("/add")
    @Operation(summary = "添加证书标签", description = "创建新的证书标签")
    public ResponseEntity<Map<String, Object>> addCertificateLabel(
            @Valid @RequestBody Map<String, Object> params) {
        log.info("添加证书标签请求, params: {}", params);
        
        if (params == null || params.isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        
        // 验证必需参数
        if (!params.containsKey("label_name") || !params.containsKey("label_remark")) {
            Map<String, Object> errorResult = Map.of(
                "success", false,
                "message", "缺少必需参数: label_name, label_remark"
            );
            return ResponseEntity.badRequest().body(errorResult);
        }
        
        try {
            Map<String, Object> result = certificateLabelService.addCertificateLabel(params);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("添加证书标签失败, error: ", e);
            Map<String, Object> errorResult = Map.of(
                "success", false,
                "message", "添加标签失败: " + e.getMessage()
            );
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取热门标签
     */
    @GetMapping("/popular")
    @Operation(summary = "获取热门标签", description = "获取使用频率最高的标签列表")
    public ResponseEntity<Map<String, Object>> getPopularLabels(
            @Parameter(description = "限制数量")
            @RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        log.info("获取热门标签请求, limit: {}", limit);
        
        try {
            var labels = certificateLabelService.getPopularLabels(limit);
            Map<String, Object> result = Map.of(
                "success", true,
                "data", labels,
                "total", labels.size()
            );
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取热门标签失败, error: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据类别获取标签
     */
    @GetMapping("/category/{category}")
    @Operation(summary = "根据类别获取标签", description = "根据标签类别获取标签列表")
    public ResponseEntity<Map<String, Object>> getLabelsByCategory(
            @Parameter(description = "标签类别", required = true)
            @PathVariable String category) {
        log.info("根据类别获取标签请求, category: {}", category);
        
        try {
            var labels = certificateLabelService.getLabelsByCategory(category);
            Map<String, Object> result = Map.of(
                "success", true,
                "data", labels,
                "total", labels.size()
            );
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("根据类别获取标签失败, category: {}, error: ", category, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 搜索标签
     */
    @GetMapping("/search/{keyword}")
    @Operation(summary = "搜索标签", description = "根据关键字搜索标签")
    public ResponseEntity<Map<String, Object>> searchLabels(
            @Parameter(description = "搜索关键字", required = true)
            @PathVariable String keyword,
            @Parameter(description = "限制数量")
            @RequestParam(value = "limit", defaultValue = "20") Integer limit) {
        log.info("搜索标签请求, keyword: {}, limit: {}", keyword, limit);
        
        try {
            var labels = certificateLabelService.searchLabels(keyword, limit);
            Map<String, Object> result = Map.of(
                "success", true,
                "data", labels,
                "total", labels.size()
            );
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("搜索标签失败, keyword: {}, error: ", keyword, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取标签统计信息
     */
    @GetMapping("/statistics/{labelId}")
    @Operation(summary = "获取标签统计", description = "获取指定标签的使用统计信息")
    public ResponseEntity<Map<String, Object>> getLabelStatistics(
            @Parameter(description = "标签ID", required = true)
            @PathVariable Integer labelId) {
        log.info("获取标签统计请求, labelId: {}", labelId);
        
        try {
            long certificateCount = certificateLabelService.getCertificateCountByLabel(labelId);
            Map<String, Object> result = Map.of(
                "success", true,
                "data", Map.of(
                    "labelId", labelId,
                    "certificateCount", certificateCount
                )
            );
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取标签统计失败, labelId: {}, error: ", labelId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
