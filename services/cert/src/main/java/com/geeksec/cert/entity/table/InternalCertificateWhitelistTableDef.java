package com.geeksec.cert.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 内部证书白名单实体表定义类
 *
 * 由 MyBatis-Flex 注解处理器生成，用于类型安全的查询构建
 *
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
public class InternalCertificateWhitelistTableDef extends TableDef {

    /**
     * 内部证书白名单实体表定义实例
     */
    public static final InternalCertificateWhitelistTableDef INTERNAL_CERTIFICATE_WHITELIST = new InternalCertificateWhitelistTableDef();

    /**
     * 证书SHA1 - 主键
     */
    public final QueryColumn CERT_SHA1 = new QueryColumn(this, "cert_sha1");

    /**
     * 任务ID
     */
    public final QueryColumn TASK_ID = new QueryColumn(this, "task_id");

    /**
     * 关联IP
     */
    public final QueryColumn LINK_IP = new QueryColumn(this, "link_ip");

    /**
     * 备注信息
     */
    public final QueryColumn REMARK = new QueryColumn(this, "remark");

    /**
     * 构造函数
     */
    public InternalCertificateWhitelistTableDef() {
        super("", "internal_certificate_whitelist");
    }

    /**
     * 带别名的构造函数
     */
    public InternalCertificateWhitelistTableDef(String alias) {
        super("", "internal_certificate_whitelist", alias);
    }
}
