package com.geeksec.cert.dto.graph;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 图数据库顶点边查询结果
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@Schema(description = "图数据库顶点边查询结果")
public class VertexEdgeDto {

    /**
     * 顶点ID
     */
    @Schema(description = "顶点ID")
    @JsonProperty("vertex_id")
    private String vertexId;

    /**
     * 顶点类型
     */
    @Schema(description = "顶点类型")
    @JsonProperty("vertex_type")
    private String vertexType;

    /**
     * 顶点属性
     */
    @Schema(description = "顶点属性")
    @JsonProperty("vertex_properties")
    private Map<String, Object> vertexProperties;

    /**
     * 关联的边信息
     */
    @Schema(description = "关联的边信息")
    @JsonProperty("edges")
    private List<EdgeInfo> edges;

    /**
     * 边信息
     */
    @Data
    @Schema(description = "边信息")
    public static class EdgeInfo {

        /**
         * 边类型
         */
        @Schema(description = "边类型")
        @JsonProperty("edge_type")
        private String edgeType;

        /**
         * 目标顶点ID
         */
        @Schema(description = "目标顶点ID")
        @JsonProperty("target_vertex_id")
        private String targetVertexId;

        /**
         * 目标顶点类型
         */
        @Schema(description = "目标顶点类型")
        @JsonProperty("target_vertex_type")
        private String targetVertexType;

        /**
         * 边属性
         */
        @Schema(description = "边属性")
        @JsonProperty("edge_properties")
        private Map<String, Object> edgeProperties;

        /**
         * 目标顶点属性
         */
        @Schema(description = "目标顶点属性")
        @JsonProperty("target_vertex_properties")
        private Map<String, Object> targetVertexProperties;

        /**
         * 边方向 (IN/OUT)
         */
        @Schema(description = "边方向")
        @JsonProperty("direction")
        private String direction;

        /**
         * 权重
         */
        @Schema(description = "权重")
        @JsonProperty("weight")
        private Double weight;

        /**
         * 创建时间
         */
        @Schema(description = "创建时间")
        @JsonProperty("created_time")
        private Long createdTime;

        /**
         * 更新时间
         */
        @Schema(description = "更新时间")
        @JsonProperty("updated_time")
        private Long updatedTime;
    }
}
