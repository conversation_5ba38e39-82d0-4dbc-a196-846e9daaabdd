package com.geeksec.cert.repository;

import com.geeksec.cert.entity.Certificate;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

import static com.geeksec.cert.entity.table.CertificateTableDef.CERTIFICATE;

/**
 * 证书数据访问接口
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Mapper
public interface CertificateRepository extends BaseMapper<Certificate> {

    /**
     * 根据证书ID列表查询证书信息
     *
     * @param certIds 证书ID列表
     * @return 证书列表
     */
    default List<Certificate> selectByCertIds(List<String> certIds) {
        if (certIds == null || certIds.isEmpty()) {
            return List.of();
        }
        return selectListByQuery(QueryWrapper.create()
                .where(CERTIFICATE.CERT_ID.in(certIds)));
    }

    /**
     * 根据威胁等级范围查询证书
     *
     * @param minThreatLevel 最小威胁等级
     * @param maxThreatLevel 最大威胁等级
     * @param limit 限制数量
     * @return 证书列表
     */
    default List<Certificate> selectByThreatLevelRange(Integer minThreatLevel, Integer maxThreatLevel, Integer limit) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(CERTIFICATE.THREAT_LEVEL.between(minThreatLevel, maxThreatLevel))
                .orderBy(CERTIFICATE.THREAT_LEVEL, false)
                .orderBy(CERTIFICATE.LAST_SEEN, false);

        if (limit != null && limit > 0) {
            queryWrapper.limit(limit);
        }

        return selectListByQuery(queryWrapper);
    }

    /**
     * 根据信任等级范围查询证书
     *
     * @param minTrustLevel 最小信任等级
     * @param maxTrustLevel 最大信任等级
     * @param limit 限制数量
     * @return 证书列表
     */
    default List<Certificate> selectByTrustLevelRange(Integer minTrustLevel, Integer maxTrustLevel, Integer limit) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(CERTIFICATE.TRUST_LEVEL.between(minTrustLevel, maxTrustLevel))
                .orderBy(CERTIFICATE.TRUST_LEVEL, false)
                .orderBy(CERTIFICATE.LAST_SEEN, false);

        if (limit != null && limit > 0) {
            queryWrapper.limit(limit);
        }

        return selectListByQuery(queryWrapper);
    }

    /**
     * 根据时间范围查询证书
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 证书列表
     */
    @Select("SELECT cert_id, first_seen, last_seen, threat_level, trust_level, remark, created_at, updated_at " +
            "FROM cert WHERE last_seen BETWEEN #{startTime} AND #{endTime} ORDER BY last_seen DESC")
    List<Certificate> selectByTimeRange(@Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 更新证书的威胁等级
     *
     * @param certId 证书ID
     * @param threatLevel 威胁等级
     * @return 更新行数
     */
    @Update("UPDATE cert SET threat_level = #{threatLevel}, updated_at = CURRENT_TIMESTAMP WHERE cert_id = #{certId}")
    int updateThreatLevel(@Param("certId") String certId, @Param("threatLevel") Integer threatLevel);

    /**
     * 更新证书的信任等级
     *
     * @param certId 证书ID
     * @param trustLevel 信任等级
     * @return 更新行数
     */
    @Update("UPDATE cert SET trust_level = #{trustLevel}, updated_at = CURRENT_TIMESTAMP WHERE cert_id = #{certId}")
    int updateTrustLevel(@Param("certId") String certId, @Param("trustLevel") Integer trustLevel);

    /**
     * 更新证书备注
     *
     * @param certId 证书ID
     * @param remark 备注
     * @return 更新行数
     */
    @Update("UPDATE cert SET remark = #{remark}, updated_at = CURRENT_TIMESTAMP WHERE cert_id = #{certId}")
    int updateRemark(@Param("certId") String certId, @Param("remark") String remark);

    /**
     * 批量插入或更新证书信息
     *
     * @param certificates 证书列表
     * @return 影响行数
     */
    default int batchInsertOrUpdate(List<Certificate> certificates) {
        return insertBatch(certificates);
    }

    /**
     * 统计证书总数
     *
     * @return 证书总数
     */
    @Select("SELECT COUNT(*) FROM cert")
    long countTotal();

    /**
     * 根据威胁等级统计证书数量
     *
     * @param threatLevel 威胁等级
     * @return 证书数量
     */
    @Select("SELECT COUNT(*) FROM cert WHERE threat_level = #{threatLevel}")
    long countByThreatLevel(@Param("threatLevel") Integer threatLevel);

    /**
     * 根据信任等级统计证书数量
     *
     * @param trustLevel 信任等级
     * @return 证书数量
     */
    @Select("SELECT COUNT(*) FROM cert WHERE trust_level = #{trustLevel}")
    long countByTrustLevel(@Param("trustLevel") Integer trustLevel);

    /**
     * 查询最近活跃的证书
     *
     * @param days 天数
     * @param limit 限制数量
     * @return 证书列表
     */
    @Select("""
            <script>
            SELECT cert_id, first_seen, last_seen, threat_level, trust_level, remark, created_at, updated_at
            FROM cert
            WHERE last_seen >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
            ORDER BY last_seen DESC
            <if test="limit != null and limit > 0">
                LIMIT #{limit}
            </if>
            </script>
            """)
    List<Certificate> selectRecentActive(@Param("days") Integer days, @Param("limit") Integer limit);

    /**
     * 根据备注关键字搜索证书
     *
     * @param keyword 关键字
     * @param limit 限制数量
     * @return 证书列表
     */
    @Select("""
            <script>
            SELECT cert_id, first_seen, last_seen, threat_level, trust_level, remark, created_at, updated_at
            FROM cert
            WHERE remark LIKE CONCAT('%', #{keyword}, '%')
            ORDER BY updated_at DESC
            <if test="limit != null and limit > 0">
                LIMIT #{limit}
            </if>
            </script>
            """)
    List<Certificate> searchByRemarkKeyword(@Param("keyword") String keyword, @Param("limit") Integer limit);
}
