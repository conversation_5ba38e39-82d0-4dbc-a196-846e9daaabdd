package com.geeksec.cert.controller;

import com.geeksec.cert.dto.CertificateFileStatusResponse;
import com.geeksec.cert.dto.CertificateSearchCondition;
import com.geeksec.cert.dto.CertificateUploadRequest;
import com.geeksec.cert.service.CertificateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.Map;

/**
 * 证书文件管理控制器
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/file")
@RequiredArgsConstructor
@Validated
@Tag(name = "证书文件管理", description = "证书文件上传、下载、状态管理相关接口")
public class CertificateFileController {

    private final CertificateService certificateService;

    /**
     * 证书导入进度&状态
     */
    @GetMapping("/status")
    @Operation(summary = "获取证书文件状态", description = "获取证书文件上传和处理状态")
    public ResponseEntity<CertificateFileStatusResponse> getCertificateFileStatus() {
        log.info("获取证书文件状态请求");
        
        try {
            CertificateFileStatusResponse response = certificateService.getCertificateFileStatus();
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取证书文件状态失败, error: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 查询上传证书文件列表
     */
    @PostMapping("/list")
    @Operation(summary = "获取证书文件列表", description = "查询已上传的证书文件列表")
    public ResponseEntity<Map<String, Object>> getCertificateFileList(
            @Valid @RequestBody CertificateSearchCondition condition) {
        log.info("获取证书文件列表请求, condition: {}", condition);
        
        try {
            Map<String, Object> result = certificateService.getCertificateFileList(condition);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取证书文件列表失败, error: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 导入证书
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "上传证书文件", description = "上传证书文件进行导入")
    public ResponseEntity<Map<String, Object>> uploadCertificateFile(
            @Parameter(description = "证书文件", required = true)
            @RequestParam("cert_file") MultipartFile certFile,
            @Parameter(description = "任务ID")
            @RequestParam(value = "task_id", required = false) Integer taskId,
            @Parameter(description = "备注")
            @RequestParam(value = "remark", required = false) String remark) {
        log.info("上传证书文件请求, fileName: {}, taskId: {}", 
                certFile.getOriginalFilename(), taskId);
        
        try {
            // 验证文件
            if (certFile.isEmpty()) {
                Map<String, Object> errorResult = Map.of(
                    "success", false,
                    "message", "证书文件不能为空"
                );
                return ResponseEntity.badRequest().body(errorResult);
            }
            
            // 验证文件类型
            String fileName = certFile.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".crt") && !fileName.endsWith(".cer") 
                    && !fileName.endsWith(".pem") && !fileName.endsWith(".der"))) {
                Map<String, Object> errorResult = Map.of(
                    "success", false,
                    "message", "不支持的证书文件格式，请上传 .crt, .cer, .pem 或 .der 格式的文件"
                );
                return ResponseEntity.badRequest().body(errorResult);
            }
            
            // 验证文件大小 (限制为10MB)
            if (certFile.getSize() > 10 * 1024 * 1024) {
                Map<String, Object> errorResult = Map.of(
                    "success", false,
                    "message", "证书文件大小不能超过10MB"
                );
                return ResponseEntity.badRequest().body(errorResult);
            }
            
            // 构建上传请求
            CertificateUploadRequest request = new CertificateUploadRequest();
            request.setCertFile(certFile);
            request.setTaskId(taskId);
            request.setRemark(remark);
            
            Map<String, Object> result = certificateService.uploadCertificateFile(request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("上传证书文件失败, fileName: {}, error: ", certFile.getOriginalFilename(), e);
            Map<String, Object> errorResult = Map.of(
                "success", false,
                "message", "上传失败: " + e.getMessage()
            );
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 批量上传证书
     */
    @PostMapping(value = "/batch-upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "批量上传证书文件", description = "批量上传多个证书文件")
    public ResponseEntity<Map<String, Object>> batchUploadCertificateFiles(
            @Parameter(description = "证书文件列表", required = true)
            @RequestParam("cert_files") MultipartFile[] certFiles,
            @Parameter(description = "任务ID")
            @RequestParam(value = "task_id", required = false) Integer taskId,
            @Parameter(description = "备注")
            @RequestParam(value = "remark", required = false) String remark) {
        log.info("批量上传证书文件请求, fileCount: {}, taskId: {}", certFiles.length, taskId);
        
        try {
            // 验证文件数量
            if (certFiles.length == 0) {
                Map<String, Object> errorResult = Map.of(
                    "success", false,
                    "message", "请选择要上传的证书文件"
                );
                return ResponseEntity.badRequest().body(errorResult);
            }
            
            if (certFiles.length > 100) {
                Map<String, Object> errorResult = Map.of(
                    "success", false,
                    "message", "单次最多只能上传100个文件"
                );
                return ResponseEntity.badRequest().body(errorResult);
            }
            
            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMessages = new StringBuilder();
            
            // 逐个处理文件
            for (MultipartFile certFile : certFiles) {
                try {
                    if (!certFile.isEmpty()) {
                        CertificateUploadRequest request = new CertificateUploadRequest();
                        request.setCertFile(certFile);
                        request.setTaskId(taskId);
                        request.setRemark(remark);
                        
                        Map<String, Object> result = certificateService.uploadCertificateFile(request);
                        if ((Boolean) result.get("success")) {
                            successCount++;
                        } else {
                            failCount++;
                            errorMessages.append(certFile.getOriginalFilename())
                                    .append(": ").append(result.get("message")).append("; ");
                        }
                    }
                } catch (Exception e) {
                    failCount++;
                    errorMessages.append(certFile.getOriginalFilename())
                            .append(": ").append(e.getMessage()).append("; ");
                }
            }
            
            Map<String, Object> result = Map.of(
                "success", successCount > 0,
                "message", String.format("上传完成，成功: %d, 失败: %d", successCount, failCount),
                "successCount", successCount,
                "failCount", failCount,
                "errorMessages", errorMessages.toString()
            );
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量上传证书文件失败, error: ", e);
            Map<String, Object> errorResult = Map.of(
                "success", false,
                "message", "批量上传失败: " + e.getMessage()
            );
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 删除上传的证书文件
     */
    @DeleteMapping("/{fileName}")
    @Operation(summary = "删除证书文件", description = "删除已上传的证书文件")
    public ResponseEntity<Map<String, Object>> deleteCertificateFile(
            @Parameter(description = "文件名", required = true)
            @PathVariable String fileName) {
        log.info("删除证书文件请求, fileName: {}", fileName);
        
        try {
            // TODO: 实现删除证书文件的逻辑
            Map<String, Object> result = Map.of(
                "success", true,
                "message", "文件删除成功"
            );
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("删除证书文件失败, fileName: {}, error: ", fileName, e);
            Map<String, Object> errorResult = Map.of(
                "success", false,
                "message", "删除失败: " + e.getMessage()
            );
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 清理所有上传文件
     */
    @DeleteMapping("/cleanup")
    @Operation(summary = "清理所有文件", description = "清理所有已上传的证书文件")
    public ResponseEntity<Map<String, Object>> cleanupAllFiles() {
        log.info("清理所有证书文件请求");
        
        try {
            // TODO: 实现清理所有文件的逻辑
            Map<String, Object> result = Map.of(
                "success", true,
                "message", "文件清理成功"
            );
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("清理所有证书文件失败, error: ", e);
            Map<String, Object> errorResult = Map.of(
                "success", false,
                "message", "清理失败: " + e.getMessage()
            );
            return ResponseEntity.ok(errorResult);
        }
    }
}
