package com.geeksec.cert.service;

import com.geeksec.cert.enums.CertificateType;

import java.util.List;
import java.util.Map;

/**
 * 证书元数据服务接口
 * 提供证书模块专用的元数据查询功能
 * 专注于证书类型、情况类型、标签等元数据，不包含统计数据
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public interface CertificateMetadataService {

    // ==================== 证书类型相关 ====================

    /**
     * 获取所有证书类型
     *
     * @return 证书类型列表
     */
    List<CertificateType> getAllCertificateTypes();

    /**
     * 根据代码获取证书类型
     *
     * @param code 类型代码
     * @return 证书类型
     */
    CertificateType getCertificateTypeByCode(int code);

    // ==================== 证书情况类型相关 ====================

    /**
     * 获取所有证书情况类型
     *
     * @return 证书情况类型映射 (代码 -> 名称)
     */
    Map<String, String> getAllCertificateSituationTypes();

    /**
     * 根据代码获取证书情况类型名称
     *
     * @param code 证书情况类型代码
     * @return 证书情况类型名称
     */
    String getCertificateSituationTypeByCode(String code);

    // ==================== 证书标签相关 ====================

    /**
     * 获取所有证书标签
     *
     * @return 证书标签映射
     */
    Map<String, Object> getAllCertificateSigns();

    /**
     * 根据键获取证书标签
     *
     * @param key 标签键
     * @return 证书标签
     */
    Object getCertificateSignByKey(String key);
    
    // ==================== 缓存管理相关 ====================

    /**
     * 清除证书元数据缓存
     */
    void clearCertificateCache();

    /**
     * 初始化证书元数据缓存
     */
    void initCertificateCache();
}
