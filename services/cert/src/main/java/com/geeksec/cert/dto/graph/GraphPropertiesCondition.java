package com.geeksec.cert.dto.graph;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;
import java.util.Map;

/**
 * 图数据库属性查询条件
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@Schema(description = "图数据库属性查询条件")
public class GraphPropertiesCondition {

    /**
     * 起始顶点ID
     */
    @Schema(description = "起始顶点ID", required = true)
    @JsonProperty("start_vertex_id")
    private String startVertexId;

    /**
     * 起始顶点类型
     */
    @Schema(description = "起始顶点类型")
    @JsonProperty("start_vertex_type")
    private String startVertexType;

    /**
     * 顶点属性过滤条件
     */
    @Schema(description = "顶点属性过滤条件")
    @JsonProperty("vertex_properties")
    private Map<String, PropertyFilter> vertexProperties;

    /**
     * 边属性过滤条件
     */
    @Schema(description = "边属性过滤条件")
    @JsonProperty("edge_properties")
    private Map<String, PropertyFilter> edgeProperties;

    /**
     * 边类型列表
     */
    @Schema(description = "边类型列表")
    @JsonProperty("edge_types")
    private List<String> edgeTypes;

    /**
     * 目标顶点类型列表
     */
    @Schema(description = "目标顶点类型列表")
    @JsonProperty("target_vertex_types")
    private List<String> targetVertexTypes;

    /**
     * 查询方向 (IN/OUT/BOTH)
     */
    @Schema(description = "查询方向", allowableValues = {"IN", "OUT", "BOTH"})
    @JsonProperty("direction")
    private String direction = "BOTH";

    /**
     * 跳数限制
     */
    @Schema(description = "跳数限制", example = "2")
    @JsonProperty("max_hops")
    @Min(1) @Max(5)
    private Integer maxHops = 1;

    /**
     * 页码
     */
    @Schema(description = "页码", example = "1")
    @JsonProperty("page")
    @Min(1)
    private Integer page = 1;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "20")
    @JsonProperty("size")
    @Min(1) @Max(1000)
    private Integer size = 20;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段")
    @JsonProperty("sort_field")
    private String sortField;

    /**
     * 排序方向 (ASC/DESC)
     */
    @Schema(description = "排序方向", allowableValues = {"ASC", "DESC"})
    @JsonProperty("sort_order")
    private String sortOrder = "DESC";

    /**
     * 属性过滤器
     */
    @Data
    @Schema(description = "属性过滤器")
    public static class PropertyFilter {

        /**
         * 操作符 (EQ/NE/GT/GE/LT/LE/IN/NOT_IN/LIKE/NOT_LIKE)
         */
        @Schema(description = "操作符", allowableValues = {
            "EQ", "NE", "GT", "GE", "LT", "LE", "IN", "NOT_IN", "LIKE", "NOT_LIKE"
        })
        @JsonProperty("operator")
        private String operator = "EQ";

        /**
         * 过滤值
         */
        @Schema(description = "过滤值")
        @JsonProperty("value")
        private Object value;

        /**
         * 过滤值列表 (用于IN/NOT_IN操作符)
         */
        @Schema(description = "过滤值列表")
        @JsonProperty("values")
        private List<Object> values;

        /**
         * 数据类型 (STRING/INTEGER/DOUBLE/BOOLEAN/DATETIME)
         */
        @Schema(description = "数据类型", allowableValues = {
            "STRING", "INTEGER", "DOUBLE", "BOOLEAN", "DATETIME"
        })
        @JsonProperty("data_type")
        private String dataType = "STRING";

        /**
         * 是否忽略大小写 (仅对字符串类型有效)
         */
        @Schema(description = "是否忽略大小写")
        @JsonProperty("ignore_case")
        private Boolean ignoreCase = false;
    }
}
