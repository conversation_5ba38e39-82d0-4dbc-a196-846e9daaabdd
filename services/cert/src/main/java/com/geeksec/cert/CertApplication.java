package com.geeksec.cert;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.kafka.annotation.EnableKafka;

/**
 * 证书管理服务启动类
 *
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.geeksec")
@ComponentScan(basePackages = {"com.geeksec.cert", "com.geeksec.common"})
@EnableKafka
public class CertApplication {

    public static void main(String[] args) {
        SpringApplication.run(CertApplication.class, args);
    }
}
