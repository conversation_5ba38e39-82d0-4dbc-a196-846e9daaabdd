package com.geeksec.cert.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.audit.AuditManager;
import com.mybatisflex.core.audit.ConsoleMessageCollector;
import com.mybatisflex.core.audit.MessageCollector;
import com.mybatisflex.spring.boot.MyBatisFlexCustomizer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * 数据库配置类
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Slf4j
@Configuration
public class DatabaseConfig {

    /**
     * 配置主数据源
     */
    @Bean
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSource dataSource() {
        log.info("初始化主数据源");
        return new DruidDataSource();
    }

    // MyBatis-Flex 配置已统一到 common 模块中，此处移除重复配置

    /**
     * 证书实体类 - 用于类型引用
     */
    private static class Certificate {
        // 占位符类，用于泛型引用
    }
}
