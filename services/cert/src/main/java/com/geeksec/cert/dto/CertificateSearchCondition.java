package com.geeksec.cert.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.util.List;
import java.util.Map;

/**
 * 证书搜索条件
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@Schema(description = "证书搜索条件")
public class CertificateSearchCondition {

    /**
     * 当前查询用户ID
     */
    @Schema(description = "当前查询用户ID")
    @JsonProperty("user_id")
    private String userId;

    /**
     * 查询条件
     */
    @Schema(description = "查询条件")
    @JsonProperty("query")
    private List<QueryObject> query;

    /**
     * 快速检索查询条件 (输入框中内容)
     */
    @Schema(description = "快速检索查询条件")
    @JsonProperty("fast_query")
    private Map<String, Object> fastQuery;

    /**
     * 是否只展示错误证书
     */
    @Schema(description = "是否只展示错误证书")
    @JsonProperty("only_error")
    private Boolean onlyError;

    /**
     * 证书备注检索
     */
    @Schema(description = "证书备注检索")
    @JsonProperty("query_remark")
    private List<String> queryRemark;

    /**
     * 证书来源
     */
    @Schema(description = "证书来源")
    @JsonProperty("cert_source")
    private List<Integer> certSource;

    /**
     * 创建查询模板时的备注
     */
    @Schema(description = "创建查询模板时的备注")
    @JsonProperty("template_remark")
    private String templateRemark;

    /**
     * 是否只查询用户导入证书
     */
    @Schema(description = "是否只查询用户导入证书")
    @JsonProperty("only_import")
    private Boolean onlyImport;

    /**
     * 是否为导出操作
     */
    @Schema(description = "是否为导出操作")
    @JsonProperty("export")
    private Boolean export;

    /**
     * 页码
     */
    @Schema(description = "页码", example = "1")
    @JsonProperty("page")
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "20")
    @JsonProperty("size")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 1000, message = "每页大小不能超过1000")
    private Integer size = 20;

    /**
     * 查询对象
     */
    @Data
    @Schema(description = "查询对象")
    public static class QueryObject {

        /**
         * 布尔搜索类型
         */
        @Schema(description = "布尔搜索类型")
        @JsonProperty("bool_search")
        private String boolSearch;

        /**
         * 查询集合
         */
        @Schema(description = "查询集合")
        @JsonProperty("search")
        private List<SearchInfo> search;
    }

    /**
     * 搜索信息
     */
    @Data
    @Schema(description = "搜索信息")
    public static class SearchInfo {

        /**
         * 查询key
         */
        @Schema(description = "查询key")
        @JsonProperty("target")
        private String target;

        /**
         * 查询内容
         */
        @Schema(description = "查询内容")
        @JsonProperty("val")
        private List<String> val;

        /**
         * 是否为ES字段查询
         */
        @Schema(description = "是否为ES字段查询")
        @JsonProperty("es_field")
        private Boolean esField;
    }
}
