package com.geeksec.cert.controller;

import com.geeksec.cert.enums.CertificateType;
import com.geeksec.cert.service.CertificateMetadataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 证书元数据控制器
 * 提供证书模块专用的元数据查询接口
 * 专注于证书类型、情况类型、标签等元数据，不包含统计数据
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/cert/metadata")
@RequiredArgsConstructor
public class CertificateMetadataController {

    private final CertificateMetadataService certificateMetadataService;

    // ==================== 证书类型相关接口 ====================

    @GetMapping("/types")
    public ResponseEntity<List<CertificateType>> getAllCertificateTypes() {
        log.info("获取所有证书类型");
        List<CertificateType> result = certificateMetadataService.getAllCertificateTypes();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/types/{code}")
    public ResponseEntity<CertificateType> getCertificateTypeByCode(@PathVariable int code) {
        log.info("根据代码获取证书类型: {}", code);
        CertificateType result = certificateMetadataService.getCertificateTypeByCode(code);
        return ResponseEntity.ok(result);
    }



    // ==================== 证书情况类型相关接口 ====================

    @GetMapping("/situation-types")
    public ResponseEntity<Map<String, String>> getAllCertificateSituationTypes() {
        log.info("获取所有证书情况类型");
        Map<String, String> result = certificateMetadataService.getAllCertificateSituationTypes();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/situation-types/{code}")
    public ResponseEntity<String> getCertificateSituationTypeByCode(@PathVariable String code) {
        log.info("根据代码获取证书情况类型: {}", code);
        String result = certificateMetadataService.getCertificateSituationTypeByCode(code);
        return ResponseEntity.ok(result);
    }

    // ==================== 证书标签相关接口 ====================

    @GetMapping("/signs")
    public ResponseEntity<Map<String, Object>> getAllCertificateSigns() {
        log.info("获取所有证书标签");
        Map<String, Object> result = certificateMetadataService.getAllCertificateSigns();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/signs/{key}")
    public ResponseEntity<Object> getCertificateSignByKey(@PathVariable String key) {
        log.info("根据键获取证书标签: {}", key);
        Object result = certificateMetadataService.getCertificateSignByKey(key);
        return ResponseEntity.ok(result);
    }

    // ==================== 缓存管理接口 ====================

    @PostMapping("/cache/clear")
    public ResponseEntity<Void> clearCertificateCache() {
        log.info("清除证书元数据缓存");
        certificateMetadataService.clearCertificateCache();
        return ResponseEntity.ok().build();
    }

    @PostMapping("/cache/init")
    public ResponseEntity<Void> initCertificateCache() {
        log.info("初始化证书元数据缓存");
        certificateMetadataService.initCertificateCache();
        return ResponseEntity.ok().build();
    }
}
