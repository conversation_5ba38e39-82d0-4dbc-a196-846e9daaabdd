package com.geeksec.cert.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * DimCertificate 实体表定义类
 *
 * 由 MyBatis-Flex 注解处理器生成，用于类型安全的查询构建
 *
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
public class DimCertificateTableDef extends TableDef {

    /**
     * DimCertificate 实体表定义实例
     */
    public static final DimCertificateTableDef DIM_CERTIFICATE = new DimCertificateTableDef();

    /**
     * 证书DER编码的SHA1哈希
     */
    public final QueryColumn DER_SHA1 = new QueryColumn(this, "der_sha1");

    /**
     * 证书DER编码的MD5哈希
     */
    public final QueryColumn DER_MD5 = new QueryColumn(this, "der_md5");

    /**
     * 证书DER编码的SHA256哈希
     */
    public final QueryColumn DER_SHA256 = new QueryColumn(this, "der_sha256");

    /**
     * PEM格式MD5哈希
     */
    public final QueryColumn PEM_MD5 = new QueryColumn(this, "pem_md5");

    /**
     * PEM格式SHA256哈希
     */
    public final QueryColumn PEM_SHA256 = new QueryColumn(this, "pem_sha256");

    /**
     * PEM格式SHA1哈希
     */
    public final QueryColumn PEM_SHA1 = new QueryColumn(this, "pem_sha1");

    /**
     * 证书版本
     */
    public final QueryColumn VERSION = new QueryColumn(this, "version");

    /**
     * 证书序列号
     */
    public final QueryColumn SERIAL_NUMBER = new QueryColumn(this, "serial_number");

    /**
     * 证书格式
     */
    public final QueryColumn FORMAT = new QueryColumn(this, "format");

    /**
     * 颁发者ID
     */
    public final QueryColumn ISSUER_ID = new QueryColumn(this, "issuer_id");

    /**
     * 主题ID
     */
    public final QueryColumn SUBJECT_ID = new QueryColumn(this, "subject_id");

    /**
     * 证书生效时间
     */
    public final QueryColumn NOT_BEFORE = new QueryColumn(this, "not_before");

    /**
     * 证书过期时间
     */
    public final QueryColumn NOT_AFTER = new QueryColumn(this, "not_after");

    /**
     * 证书有效期长度
     */
    public final QueryColumn DURATION = new QueryColumn(this, "duration");

    /**
     * 导入时间
     */
    public final QueryColumn IMPORT_TIME = new QueryColumn(this, "import_time");

    /**
     * 通用名称(CN)
     */
    public final QueryColumn COMMON_NAME = new QueryColumn(this, "common_name");

    /**
     * 主题备用名称(SAN)
     */
    public final QueryColumn SUBJECT_ALT_NAMES = new QueryColumn(this, "subject_alt_names");

    /**
     * 颁发者备用名称
     */
    public final QueryColumn ISSUER_ALT_NAMES = new QueryColumn(this, "issuer_alt_names");

    /**
     * 公钥信息
     */
    public final QueryColumn PUBLIC_KEY = new QueryColumn(this, "public_key");

    /**
     * 公钥算法
     */
    public final QueryColumn PUBLIC_KEY_ALGORITHM = new QueryColumn(this, "public_key_algorithm");

    /**
     * 公钥长度
     */
    public final QueryColumn PUBLIC_KEY_LENGTH = new QueryColumn(this, "public_key_length");

    /**
     * 公钥参数
     */
    public final QueryColumn PUBLIC_KEY_PARAMETER = new QueryColumn(this, "public_key_parameter");

    /**
     * SPKI SHA256
     */
    public final QueryColumn SPKI_SHA256 = new QueryColumn(this, "spki_sha256");

    /**
     * 签名算法
     */
    public final QueryColumn SIGNATURE_ALGORITHM = new QueryColumn(this, "signature_algorithm");

    /**
     * 签名算法名称
     */
    public final QueryColumn SIGNATURE_ALG_NAME = new QueryColumn(this, "signature_alg_name");

    /**
     * 签名算法OID
     */
    public final QueryColumn SIGNATURE_ALG_OID = new QueryColumn(this, "signature_alg_oid");

    /**
     * 密钥用途
     */
    public final QueryColumn KEY_USAGE = new QueryColumn(this, "key_usage");

    /**
     * 扩展密钥用途
     */
    public final QueryColumn EXTENDED_KEY_USAGE = new QueryColumn(this, "extended_key_usage");

    /**
     * 基本约束
     */
    public final QueryColumn BASIC_CONSTRAINTS = new QueryColumn(this, "basic_constraints");

    /**
     * 授权密钥标识符
     */
    public final QueryColumn AUTHORITY_KEY_IDENTIFIER = new QueryColumn(this, "authority_key_identifier");

    /**
     * 主题密钥标识符
     */
    public final QueryColumn SUBJECT_KEY_IDENTIFIER = new QueryColumn(this, "subject_key_identifier");

    /**
     * CRL分发点
     */
    public final QueryColumn CRL_DISTRIBUTION_POINTS = new QueryColumn(this, "crl_distribution_points");

    /**
     * 授权信息访问
     */
    public final QueryColumn AUTHORITY_INFO_ACCESS = new QueryColumn(this, "authority_info_access");

    /**
     * 主题信息访问
     */
    public final QueryColumn SUBJECT_INFO_ACCESS = new QueryColumn(this, "subject_info_access");

    /**
     * 证书策略
     */
    public final QueryColumn CERT_POLICIES = new QueryColumn(this, "cert_policies");

    /**
     * 证书来源
     */
    public final QueryColumn SOURCE = new QueryColumn(this, "source");

    /**
     * 用户类型
     */
    public final QueryColumn USER_TYPE = new QueryColumn(this, "user_type");

    /**
     * 业务类型
     */
    public final QueryColumn BUSINESS_TYPE = new QueryColumn(this, "business_type");

    /**
     * CA类型
     */
    public final QueryColumn CA_TYPE = new QueryColumn(this, "ca_type");

    /**
     * 行业类型
     */
    public final QueryColumn INDUSTRY_TYPE = new QueryColumn(this, "industry_type");

    /**
     * 主题地区
     */
    public final QueryColumn SUBJECT_AREA = new QueryColumn(this, "subject_area");

    /**
     * 颁发者地区
     */
    public final QueryColumn ISSUER_AREA = new QueryColumn(this, "issuer_area");

    /**
     * 是否受信任证书
     */
    public final QueryColumn IS_TRUSTED = new QueryColumn(this, "is_trusted");

    /**
     * 是否成功解析
     */
    public final QueryColumn IS_PARSED_SUCCESSFULLY = new QueryColumn(this, "is_parsed_successfully");

    /**
     * 是否为损坏证书
     */
    public final QueryColumn IS_CORRUPTED = new QueryColumn(this, "is_corrupted");

    /**
     * 证书出现次数
     */
    public final QueryColumn CERT_OCCURRENCE_COUNT = new QueryColumn(this, "cert_occurrence_count");

    /**
     * 威胁评分
     */
    public final QueryColumn THREAT_SCORE = new QueryColumn(this, "threat_score");

    /**
     * 信任评分
     */
    public final QueryColumn TRUST_SCORE = new QueryColumn(this, "trust_score");

    /**
     * 威胁等级
     */
    public final QueryColumn THREAT_LEVEL = new QueryColumn(this, "threat_level");

    /**
     * 标签ID数组
     */
    public final QueryColumn LABELS = new QueryColumn(this, "labels");

    /**
     * 关联域名
     */
    public final QueryColumn ASSOCIATED_DOMAINS = new QueryColumn(this, "associated_domains");

    /**
     * 关联IP
     */
    public final QueryColumn ASSOCIATED_IPS = new QueryColumn(this, "associated_ips");

    /**
     * 关联组织
     */
    public final QueryColumn ORGANIZATION = new QueryColumn(this, "organization");

    /**
     * 首次出现时间
     */
    public final QueryColumn FIRST_SEEN = new QueryColumn(this, "first_seen");

    /**
     * 最后出现时间
     */
    public final QueryColumn LAST_SEEN = new QueryColumn(this, "last_seen");

    /**
     * 记录创建时间
     */
    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    /**
     * 记录更新时间
     */
    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    /**
     * 备注
     */
    public final QueryColumn REMARK = new QueryColumn(this, "remark");

    /**
     * 所有字段
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含所有字段
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{
            DER_SHA1, DER_MD5, DER_SHA256, COMMON_NAME, ISSUER_ID, SUBJECT_ID,
            NOT_BEFORE, NOT_AFTER, THREAT_SCORE, TRUST_SCORE, FIRST_SEEN, LAST_SEEN
    };

    /**
     * 表名
     */
    public DimCertificateTableDef() {
        super("", "dim_cert");
    }
}
