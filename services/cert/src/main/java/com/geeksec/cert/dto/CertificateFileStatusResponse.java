package com.geeksec.cert.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 证书文件状态响应
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@Schema(description = "证书文件状态响应")
public class CertificateFileStatusResponse {

    /**
     * 上传状态 (UPLOADING, PROCESSING, COMPLETED, FAILED)
     */
    @Schema(description = "上传状态")
    @JsonProperty("status")
    private String status;

    /**
     * 处理进度 (0-100)
     */
    @Schema(description = "处理进度")
    @JsonProperty("progress")
    private Integer progress;

    /**
     * 总文件数
     */
    @Schema(description = "总文件数")
    @JsonProperty("total_files")
    private Integer totalFiles;

    /**
     * 已处理文件数
     */
    @Schema(description = "已处理文件数")
    @JsonProperty("processed_files")
    private Integer processedFiles;

    /**
     * 成功处理文件数
     */
    @Schema(description = "成功处理文件数")
    @JsonProperty("success_files")
    private Integer successFiles;

    /**
     * 失败文件数
     */
    @Schema(description = "失败文件数")
    @JsonProperty("failed_files")
    private Integer failedFiles;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @JsonProperty("start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    @JsonProperty("end_time")
    private LocalDateTime endTime;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    @JsonProperty("error_message")
    private String errorMessage;

    /**
     * 文件列表
     */
    @Schema(description = "文件列表")
    @JsonProperty("file_list")
    private List<CertificateFileInfo> fileList;

    /**
     * 证书文件信息
     */
    @Data
    @Schema(description = "证书文件信息")
    public static class CertificateFileInfo {

        /**
         * 文件名
         */
        @Schema(description = "文件名")
        @JsonProperty("file_name")
        private String fileName;

        /**
         * 文件大小
         */
        @Schema(description = "文件大小")
        @JsonProperty("file_size")
        private Long fileSize;

        /**
         * 处理状态
         */
        @Schema(description = "处理状态")
        @JsonProperty("status")
        private String status;

        /**
         * 证书SHA1
         */
        @Schema(description = "证书SHA1")
        @JsonProperty("cert_sha1")
        private String certSha1;

        /**
         * 上传时间
         */
        @Schema(description = "上传时间")
        @JsonProperty("upload_time")
        private LocalDateTime uploadTime;

        /**
         * 错误信息
         */
        @Schema(description = "错误信息")
        @JsonProperty("error_message")
        private String errorMessage;
    }
}
