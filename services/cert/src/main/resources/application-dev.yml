spring:
  # 开发环境数据库配置 - 使用环境变量
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:nta}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:nta_user}
    password: ${DB_PASSWORD:nta_password}
    druid:
      # 开发环境启用监控
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"

  # 开发环境 Redis 配置 - 使用环境变量
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:0}

  # 开发环境 Kafka 配置 - 使用环境变量
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092}

# 开发环境 NebulaGraph 配置 - 使用环境变量
nebula:
  hosts: ${NEBULA_HOSTS:127.0.0.1:9669}
  username: ${NEBULA_USERNAME:root}
  password: ${NEBULA_PASSWORD:nebula}
  space: ${NEBULA_SPACE:nta_graph}

# 开发环境日志配置
logging:
  level:
    root: INFO
    com.geeksec.cert: DEBUG
    org.springframework.web: DEBUG
    org.springframework.kafka: DEBUG
    com.mybatisflex: DEBUG
    org.nebula: DEBUG
  file:
    name: logs/cert-service-dev.log
    max-size: 100MB
    max-history: 30

# 开发环境 Knife4j 配置
knife4j:
  enable: true
  setting:
    enable-version: true
    enable-reload-cache-parameter: true
