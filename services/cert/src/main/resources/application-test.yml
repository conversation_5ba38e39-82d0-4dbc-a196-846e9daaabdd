# Cert Service 测试环境配置
# 继承通用测试环境配置

spring:
  profiles:
    active: test
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8083}
  servlet:
    context-path: /cert

# 证书服务特定配置
cert:
  # 测试环境证书处理配置
  processing:
    # 证书解析配置
    parsing:
      max-cert-size: 10MB  # 最大证书文件大小
      timeout: 30000  # 30秒解析超时
      enable-validation: true
    
    # 证书存储配置
    storage:
      base-path: ${CERT_STORAGE_PATH:/data/nta-test/certs}
      backup-path: ${CERT_BACKUP_PATH:/data/nta-test/certs/backup}
      enable-compression: true
    
    # 证书分析配置
    analysis:
      enable-fingerprint: true
      enable-chain-validation: true
      enable-revocation-check: false  # 测试环境关闭吊销检查
  
  # 测试环境 Doris 数据源配置
  doris:
    datasource:
      url: jdbc:mysql://${DORIS_HOST:test-doris.nta.local}:${DORIS_PORT:9030}/${DORIS_DATABASE:nta_test}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
      username: ${DORIS_USERNAME:nta_test}
      password: ${DORIS_PASSWORD:nta_test123}
  
  # 测试环境 NebulaGraph 配置
  nebula:
    hosts: ${NEBULA_HOSTS:test-nebula.nta.local:9669}
    username: ${NEBULA_USERNAME:nta_test}
    password: ${NEBULA_PASSWORD:nta_test123}
    space: ${NEBULA_SPACE:cert_graph_test}
  
  # 测试环境缓存配置
  cache:
    cert-cache:
      max-size: 10000
      ttl: 3600  # 1小时
    fingerprint-cache:
      max-size: 50000
      ttl: 7200  # 2小时

# 日志配置
logging:
  level:
    '[com.geeksec.cert]': INFO
