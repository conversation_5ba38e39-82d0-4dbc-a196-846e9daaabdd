# Cert Service 生产环境配置
# 继承通用生产环境配置

spring:
  profiles:
    active: prod
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8083}
  servlet:
    context-path: /cert

# 证书服务特定配置
cert:
  # 生产环境证书处理配置
  processing:
    # 证书解析配置
    parsing:
      max-cert-size: 50MB  # 最大证书文件大小
      timeout: 60000  # 60秒解析超时
      enable-validation: true
      enable-strict-mode: true  # 生产环境启用严格模式
    
    # 证书存储配置
    storage:
      base-path: ${CERT_STORAGE_PATH:/data/nta-prod/certs}
      backup-path: ${CERT_BACKUP_PATH:/backup/nta-prod/certs}
      archive-path: ${CERT_ARCHIVE_PATH:/archive/nta-prod/certs}
      enable-compression: true
      enable-encryption: true
    
    # 证书分析配置
    analysis:
      enable-fingerprint: true
      enable-chain-validation: true
      enable-revocation-check: true  # 生产环境启用吊销检查
      enable-threat-detection: true
  
  # 生产环境 Doris 数据源配置
  doris:
    datasource:
      url: jdbc:mysql://${DORIS_HOST:prod-doris.nta.local}:${DORIS_PORT:9030}/${DORIS_DATABASE:nta_prod}?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=Asia/Shanghai
      username: ${DORIS_USERNAME:nta_prod}
      password: ${DORIS_PASSWORD}
  
  # 生产环境 NebulaGraph 配置
  nebula:
    hosts: ${NEBULA_HOSTS:prod-nebula-1.nta.local:9669,prod-nebula-2.nta.local:9669}
    username: ${NEBULA_USERNAME:nta_prod}
    password: ${NEBULA_PASSWORD}
    space: ${NEBULA_SPACE:cert_graph_prod}
    ssl-enabled: true
  
  # 生产环境缓存配置
  cache:
    cert-cache:
      max-size: 100000
      ttl: 7200  # 2小时
    fingerprint-cache:
      max-size: 500000
      ttl: 14400  # 4小时
  
  # 生产环境安全配置
  security:
    enable-cert-validation: true
    enable-access-control: true
    enable-audit-log: true
  
  # 生产环境监控配置
  monitoring:
    enable-cert-metrics: true
    enable-performance-tracking: true

# 日志配置
logging:
  level:
    '[com.geeksec.cert]': INFO
