server:
  port: 8088
  servlet:
    context-path: /api/config

spring:
  application:
    name: config-service

  profiles:
    # 激活Kubernetes配置
    include: kubernetes

  cloud:
    # 启用Kubernetes服务发现
    kubernetes:
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        namespace: ${KUBERNETES_NAMESPACE:nta}
      config:
        enabled: true
        sources:
          - name: ${spring.application.name}-config
            namespace: ${KUBERNETES_NAMESPACE:nta}
          - name: common-config
            namespace: ${KUBERNETES_NAMESPACE:nta}
    config:
      import:
        - "kubernetes:"
        - "classpath:application-kubernetes.yml"

  # 数据源配置 - 统一使用PostgreSQL + Druid
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:nta}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:nta}
    password: ${DB_PASSWORD:nta123}

    # Druid 连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

# MyBatis-Flex 配置
mybatis-flex:
  type-aliases-package: com.geeksec.nta.config.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    logic-delete-column: deleted
    version-column: version

# Knife4j 配置
knife4j:
  enable: true
  production: false
  basic:
    enable: false
  setting:
    language: zh-CN

# Sa-Token 配置
sa-token:
  token-name: Authorization
  token-prefix: Bearer
  timeout: 2592000
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-log: false

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
