# Model Service 生产环境配置
# 继承通用生产环境配置

spring:
  profiles:
    active: prod
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8085}
  servlet:
    context-path: /model

# 模型管理服务特定配置
model:
  # Kafka主题配置
  kafka:
    topics:
      model-state-changed: model_state_changed_prod
      model-config: model_config_prod
  
  # 生产环境模型文件存储配置
  storage:
    base-path: ${MODEL_STORAGE_PATH:/data/nta-prod/models}
    max-file-size: ${MODEL_MAX_FILE_SIZE:500MB}  # 生产环境大文件限制
    allowed-extensions: .model,.pkl,.pt,.onnx,.pb,.h5,.joblib
    backup-enabled: true  # 生产环境启用备份
    backup-path: ${MODEL_BACKUP_PATH:/backup/nta-prod/models}
    backup-retention-days: 90
  
  # 生产环境模型验证配置
  validation:
    enable-config-validation: true
    enable-file-validation: true
    max-config-size: 100KB
    enable-signature-check: true  # 生产环境必须检查签名
    enable-virus-scan: true  # 生产环境启用病毒扫描
  
  # 生产环境模型执行配置
  execution:
    timeout: 300000  # 5分钟超时
    max-memory: 8GB
    enable-gpu: ${MODEL_GPU_ENABLED:true}  # 生产环境可启用GPU
    thread-pool-size: 10
    enable-resource-monitoring: true
  
  # 生产环境模型缓存配置
  cache:
    enable: true
    max-size: 200
    ttl: 1800  # 30分钟缓存
    enable-distributed-cache: true
  
  # 生产环境模型监控配置
  monitoring:
    enable-metrics: true
    enable-performance-tracking: true
    enable-usage-analytics: true
    enable-error-tracking: true
  
  # 生产环境安全配置
  security:
    enable-access-control: true
    enable-audit-log: true
    enable-encryption: true
    encryption-key: ${MODEL_ENCRYPTION_KEY}

# 日志配置
logging:
  level:
    '[com.geeksec.model]': INFO
    '[org.springframework.kafka]': WARN
    '[com.mybatisflex]': WARN
