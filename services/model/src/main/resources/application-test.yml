# Model Service 测试环境配置
# 继承通用测试环境配置

spring:
  profiles:
    active: test
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8085}
  servlet:
    context-path: /model

# 模型管理服务特定配置
model:
  # Kafka主题配置
  kafka:
    topics:
      model-state-changed: model_state_changed_test
      model-config: model_config_test
  
  # 测试环境模型文件存储配置
  storage:
    base-path: ${MODEL_STORAGE_PATH:/data/nta-test/models}
    max-file-size: ${MODEL_MAX_FILE_SIZE:100MB}  # 测试环境中等文件限制
    allowed-extensions: .model,.pkl,.pt,.onnx,.pb,.h5,.joblib
    backup-enabled: true  # 测试环境启用备份
    backup-path: ${MODEL_BACKUP_PATH:/data/nta-test/models/backup}
  
  # 测试环境模型验证配置
  validation:
    enable-config-validation: true
    enable-file-validation: true
    max-config-size: 50KB
    enable-signature-check: true  # 测试环境检查签名
  
  # 测试环境模型执行配置
  execution:
    timeout: 60000  # 60秒超时
    max-memory: 2GB
    enable-gpu: false  # 测试环境不启用GPU
    thread-pool-size: 4
  
  # 测试环境模型缓存配置
  cache:
    enable: true
    max-size: 50
    ttl: 600  # 10分钟缓存
  
  # 测试环境模型监控配置
  monitoring:
    enable-metrics: true
    enable-performance-tracking: true

# 日志配置
logging:
  level:
    '[com.geeksec.model]': INFO
    '[org.springframework.kafka]': INFO
    '[com.mybatisflex]': INFO
