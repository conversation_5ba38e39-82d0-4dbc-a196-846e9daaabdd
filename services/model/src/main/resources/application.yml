server:
  port: 8087
  servlet:
    context-path: /model

spring:
  application:
    name: model-service
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
    include: common
  
  # 数据库配置 - PostgreSQL (模型管理数据)
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:nta}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:nta_user}
    password: ${DB_PASSWORD:nta_password}
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

  # Redis 配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:0}
      timeout: 6000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  # Kafka 配置
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: all
      retries: 3
    consumer:
      group-id: model-service-group
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      auto-offset-reset: earliest
      enable-auto-commit: false

# MyBatis-Flex 配置
mybatis-flex:
  type-aliases-package: com.geeksec.model.entity
  mapper-locations: classpath:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    default-executor-type: simple
    default-statement-timeout: 25
    default-fetch-size: 100
    safe-row-bounds-enabled: false
    local-cache-scope: session
    jdbc-type-for-null: other
    lazy-load-trigger-methods: equals,clone,hashCode,toString
    default-scripting-language: org.apache.ibatis.scripting.xmltags.XMLLanguageDriver
    call-setters-on-nulls: false
    return-instance-for-empty-row: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# Knife4j 配置
knife4j:
  enable: true
  openapi:
    title: NTA 3.0 模型管理服务 API
    description: 检测模型管理相关的 REST API 接口文档
    version: 3.0.0
    concat-contact: true
    contact:
      name: NTA 3.0 Team
      email: <EMAIL>
    license:
      name: Apache 2.0
      url: https://www.apache.org/licenses/LICENSE-2.0
  setting:
    language: zh_cn
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 实体类列表

# Sa-Token 配置
sa-token:
  token-name: Authorization
  token-prefix: Bearer
  timeout: 2592000
  activity-timeout: -1
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-log: false

# 日志配置
logging:
  level:
    com.geeksec.model: DEBUG
    org.springframework.kafka: INFO
    org.apache.kafka: WARN
    com.mybatisflex: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when_authorized
  metrics:
    export:
      prometheus:
        enabled: true

# 模型管理服务特定配置
model:
  # Kafka主题配置
  kafka:
    topics:
      model-state-changed: model_state_changed
      model-config: model_config
  
  # 模型文件存储配置
  storage:
    base-path: ${MODEL_STORAGE_PATH:/data/models}
    max-file-size: ${MODEL_MAX_FILE_SIZE:100MB}
    allowed-extensions: .model,.pkl,.pt,.onnx,.pb
  
  # 模型验证配置
  validation:
    enable-config-validation: true
    enable-file-validation: true
    max-config-size: 10KB
