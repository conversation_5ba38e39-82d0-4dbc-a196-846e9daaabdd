<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.model.repository.ModelInfoMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.geeksec.model.entity.ModelInfo">
        <id column="model_id" property="modelId"/>
        <result column="model_name" property="modelName"/>
        <result column="model_algorithm" property="modelAlgorithm"/>
        <result column="model_type" property="modelType"/>
        <result column="model_version" property="modelVersion"/>
        <result column="remark" property="remark"/>
        <result column="state" property="state"/>
        <result column="model_config" property="modelConfig"/>
        <result column="model_path" property="modelPath"/>
        <result column="model_hash" property="modelHash"/>
        <result column="priority" property="priority"/>
        <result column="tags" property="tags"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        model_id, model_name, model_algorithm, model_type, model_version, remark, state,
        model_config, model_path, model_hash, priority, tags, created_time, updated_time,
        created_by, updated_by, is_deleted
    </sql>

    <!-- 查询条件 -->
    <sql id="Query_Condition">
        <where>
            is_deleted = 0
            <if test="condition.modelName != null and condition.modelName != ''">
                AND model_name LIKE CONCAT('%', #{condition.modelName}, '%')
            </if>
            <if test="condition.modelAlgorithm != null and condition.modelAlgorithm != ''">
                AND model_algorithm = #{condition.modelAlgorithm}
            </if>
            <if test="condition.modelType != null and condition.modelType != ''">
                AND model_type = #{condition.modelType}
            </if>
            <if test="condition.state != null">
                AND state = #{condition.state}
            </if>
            <if test="condition.tags != null and condition.tags != ''">
                AND tags LIKE CONCAT('%', #{condition.tags}, '%')
            </if>
            <if test="condition.startTime != null and condition.startTime != ''">
                AND created_time >= #{condition.startTime}
            </if>
            <if test="condition.endTime != null and condition.endTime != ''">
                AND created_time &lt;= #{condition.endTime}
            </if>
        </where>
    </sql>

    <!-- 分页查询模型列表 -->
    <select id="selectModelPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_model_info
        <include refid="Query_Condition"/>
        <if test="condition.orderField != null and condition.orderField != ''">
            ORDER BY ${condition.orderField}
            <if test="condition.sortOrder != null and condition.sortOrder != ''">
                ${condition.sortOrder}
            </if>
        </if>
    </select>

    <!-- 根据条件查询模型列表 -->
    <select id="selectModelList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_model_info
        <include refid="Query_Condition"/>
        <if test="condition.orderField != null and condition.orderField != ''">
            ORDER BY ${condition.orderField}
            <if test="condition.sortOrder != null and condition.sortOrder != ''">
                ${condition.sortOrder}
            </if>
        </if>
    </select>

    <!-- 根据模型名称查询模型 -->
    <select id="selectByModelName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_model_info
        WHERE model_name = #{modelName} AND is_deleted = 0
        LIMIT 1
    </select>

    <!-- 根据模型ID更新模型状态 -->
    <update id="updateModelState">
        UPDATE tb_model_info
        SET state = #{state},
            updated_time = NOW(),
            updated_by = #{updatedBy}
        WHERE model_id = #{modelId} AND is_deleted = 0
    </update>

    <!-- 根据状态查询模型列表 -->
    <select id="selectByState" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_model_info
        WHERE state = #{state} AND is_deleted = 0
        ORDER BY priority DESC, updated_time DESC
    </select>

    <!-- 根据算法类型查询模型列表 -->
    <select id="selectByAlgorithm" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_model_info
        WHERE model_algorithm = #{algorithm} AND is_deleted = 0
        ORDER BY priority DESC, updated_time DESC
    </select>

    <!-- 根据模型类型查询模型列表 -->
    <select id="selectByModelType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_model_info
        WHERE model_type = #{modelType} AND is_deleted = 0
        ORDER BY priority DESC, updated_time DESC
    </select>

    <!-- 根据标签查询模型列表 -->
    <select id="selectByTag" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_model_info
        WHERE tags LIKE CONCAT('%', #{tag}, '%') AND is_deleted = 0
        ORDER BY priority DESC, updated_time DESC
    </select>

    <!-- 统计模型总数 -->
    <select id="countModels" resultType="long">
        SELECT COUNT(*)
        FROM tb_model_info
        <include refid="Query_Condition"/>
    </select>

    <!-- 根据状态统计模型数量 -->
    <select id="countByState" resultType="long">
        SELECT COUNT(*)
        FROM tb_model_info
        WHERE state = #{state} AND is_deleted = 0
    </select>

    <!-- 软删除模型 -->
    <update id="softDeleteModel">
        UPDATE tb_model_info
        SET is_deleted = 1,
            updated_time = NOW(),
            updated_by = #{updatedBy}
        WHERE model_id = #{modelId} AND is_deleted = 0
    </update>

    <!-- 批量更新模型状态 -->
    <update id="batchUpdateModelState">
        UPDATE tb_model_info
        SET state = #{state},
            updated_time = NOW(),
            updated_by = #{updatedBy}
        WHERE model_id IN
        <foreach collection="modelIds" item="modelId" open="(" separator="," close=")">
            #{modelId}
        </foreach>
        AND is_deleted = 0
    </update>

</mapper>
