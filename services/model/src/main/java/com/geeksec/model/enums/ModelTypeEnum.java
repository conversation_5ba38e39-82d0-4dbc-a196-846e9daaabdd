package com.geeksec.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模型类型枚举
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Getter
@AllArgsConstructor
public enum ModelTypeEnum {

    /**
     * 挖矿检测模型
     */
    MINING_DETECTION("挖矿检测", "检测网络中的挖矿行为"),

    /**
     * APT检测模型
     */
    APT_DETECTION("APT检测", "检测高级持续性威胁攻击"),

    /**
     * RAT检测模型
     */
    RAT_DETECTION("RAT检测", "检测远程访问木马"),

    /**
     * Tor检测模型
     */
    TOR_DETECTION("Tor检测", "检测Tor匿名网络流量"),

    /**
     * 序列行为检测模型
     */
    SEQUENCE_BEHAVIOR_DETECTION("序列行为检测", "检测异常序列行为模式"),

    /**
     * 基线检测模型
     */
    BASELINE_DETECTION("基线检测", "基于基线的异常检测"),

    /**
     * 指纹检测模型
     */
    FINGERPRINT_DETECTION("指纹检测", "基于指纹特征的检测"),

    /**
     * 证书检测模型
     */
    CERTIFICATE_DETECTION("证书检测", "检测异常证书"),

    /**
     * DNS检测模型
     */
    DNS_DETECTION("DNS检测", "检测DNS异常行为"),

    /**
     * 网络属性检测模型
     */
    NETWORK_PROPERTY_DETECTION("网络属性检测", "检测网络属性异常"),

    /**
     * 域名检测模型
     */
    DOMAIN_DETECTION("域名检测", "检测恶意域名"),

    /**
     * 内网检测模型
     */
    INTERNAL_NETWORK_DETECTION("内网检测", "检测内网异常行为"),

    /**
     * 暴力破解检测模型
     */
    BRUTE_FORCE_DETECTION("暴力破解检测", "检测暴力破解攻击"),

    /**
     * 隧道检测模型
     */
    TUNNEL_DETECTION("隧道检测", "检测隐蔽隧道通信"),

    /**
     * 恶意软件检测模型
     */
    MALWARE_DETECTION("恶意软件检测", "检测恶意软件行为");

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据类型名称获取枚举
     * 
     * @param name 类型名称
     * @return 模型类型枚举
     */
    public static ModelTypeEnum getByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        
        for (ModelTypeEnum type : values()) {
            if (type.getName().equals(name.trim())) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查类型名称是否有效
     * 
     * @param name 类型名称
     * @return 是否有效
     */
    public static boolean isValidName(String name) {
        return getByName(name) != null;
    }
}
