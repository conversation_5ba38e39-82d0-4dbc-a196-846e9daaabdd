package com.geeksec.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 批量更新模型状态请求
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Schema(description = "批量更新模型状态请求")
public class BatchUpdateStateRequest {

    /**
     * 模型ID列表
     */
    @NotEmpty(message = "模型ID列表不能为空")
    @JsonProperty("model_ids")
    @Schema(description = "模型ID列表", example = "[99001, 99002, 99003]", required = true)
    private List<Integer> modelIds;

    /**
     * 目标状态：0-失效，1-生效
     */
    @NotNull(message = "目标状态不能为空")
    @JsonProperty("state")
    @Schema(description = "目标状态", example = "1", required = true)
    private Integer state;
}
