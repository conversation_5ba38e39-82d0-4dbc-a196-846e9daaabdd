package com.geeksec.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 模型状态更新请求
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Schema(description = "模型状态更新请求")
public class ModelStateUpdateRequest {

    /**
     * 模型ID
     */
    @NotNull(message = "模型ID不能为空")
    @JsonProperty("model_id")
    @Schema(description = "模型ID", example = "99001", required = true)
    private Integer modelId;

    /**
     * 模型状态：0-失效，1-生效
     */
    @NotNull(message = "模型状态不能为空")
    @JsonProperty("state")
    @Schema(description = "模型状态", example = "1", required = true)
    private Integer state;
}
