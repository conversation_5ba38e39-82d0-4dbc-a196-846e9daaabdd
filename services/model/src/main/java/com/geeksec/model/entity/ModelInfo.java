package com.geeksec.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 模型信息实体类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table("tb_model_info")
public class ModelInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模型ID
     */
    @Id("model_id")
    private Integer modelId;

    /**
     * 模型名称
     */
    @Column("model_name")
    private String modelName;

    /**
     * 模型算法类型
     */
    @Column("model_algorithm")
    private String modelAlgorithm;

    /**
     * 模型类型
     */
    @Column("model_type")
    private String modelType;

    /**
     * 模型版本
     */
    @Column("model_version")
    private String modelVersion;

    /**
     * 模型描述
     */
    @Column("remark")
    private String remark;

    /**
     * 模型状态：0-失效，1-生效
     */
    @Column("state")
    private Integer state;

    /**
     * 模型配置参数（JSON格式）
     */
    @Column("model_config")
    private String modelConfig;

    /**
     * 模型文件路径
     */
    @Column("model_path")
    private String modelPath;

    /**
     * 模型文件哈希值
     */
    @Column("model_hash")
    private String modelHash;

    /**
     * 模型优先级
     */
    @Column("priority")
    private Integer priority;

    /**
     * 模型标签（多个标签用逗号分隔）
     */
    @Column("tags")
    private String tags;

    /**
     * 创建时间
     */
    @Column("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Column("updated_time")
    private LocalDateTime updatedTime;

    /**
     * 创建者
     */
    @Column("created_by")
    private String createdBy;

    /**
     * 更新者
     */
    @Column("updated_by")
    private String updatedBy;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @Column("is_deleted")
    private Integer isDeleted;
}
