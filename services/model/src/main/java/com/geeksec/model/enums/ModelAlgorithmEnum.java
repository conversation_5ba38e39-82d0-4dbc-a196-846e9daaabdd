package com.geeksec.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模型算法类型枚举
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Getter
@AllArgsConstructor
public enum ModelAlgorithmEnum {

    /**
     * 协议识别算法
     */
    PROTOCOL_RECOGNITION("协议识别", "基于网络协议特征的识别算法"),

    /**
     * 特征识别算法
     */
    FEATURE_RECOGNITION("特征识别", "基于特征模式匹配的识别算法"),

    /**
     * 行为识别算法
     */
    BEHAVIOR_RECOGNITION("行为识别", "基于行为模式分析的识别算法"),

    /**
     * 机器学习算法
     */
    MACHINE_LEARNING("机器学习", "基于机器学习模型的识别算法"),

    /**
     * 深度学习算法
     */
    DEEP_LEARNING("深度学习", "基于深度神经网络的识别算法"),

    /**
     * 规则引擎算法
     */
    RULE_ENGINE("规则引擎", "基于专家规则的识别算法"),

    /**
     * 统计分析算法
     */
    STATISTICAL_ANALYSIS("统计分析", "基于统计学方法的分析算法"),

    /**
     * 图分析算法
     */
    GRAPH_ANALYSIS("图分析", "基于图结构分析的算法"),

    /**
     * 时序分析算法
     */
    TIME_SERIES_ANALYSIS("时序分析", "基于时间序列分析的算法"),

    /**
     * 异常检测算法
     */
    ANOMALY_DETECTION("异常检测", "基于异常模式检测的算法");

    /**
     * 算法名称
     */
    private final String name;

    /**
     * 算法描述
     */
    private final String description;

    /**
     * 根据算法名称获取枚举
     * 
     * @param name 算法名称
     * @return 模型算法枚举
     */
    public static ModelAlgorithmEnum getByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        
        for (ModelAlgorithmEnum algorithm : values()) {
            if (algorithm.getName().equals(name.trim())) {
                return algorithm;
            }
        }
        return null;
    }

    /**
     * 检查算法名称是否有效
     * 
     * @param name 算法名称
     * @return 是否有效
     */
    public static boolean isValidName(String name) {
        return getByName(name) != null;
    }
}
