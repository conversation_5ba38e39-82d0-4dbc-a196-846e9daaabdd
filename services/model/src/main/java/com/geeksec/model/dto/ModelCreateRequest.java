package com.geeksec.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 模型创建请求
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Schema(description = "模型创建请求")
public class ModelCreateRequest {

    /**
     * 模型名称
     */
    @NotBlank(message = "模型名称不能为空")
    @JsonProperty("model_name")
    @Schema(description = "模型名称", example = "SSL指纹检测模型", required = true)
    private String modelName;

    /**
     * 模型算法类型
     */
    @NotBlank(message = "模型算法类型不能为空")
    @JsonProperty("model_algorithm")
    @Schema(description = "模型算法类型", example = "特征识别", required = true)
    private String modelAlgorithm;

    /**
     * 模型类型
     */
    @JsonProperty("model_type")
    @Schema(description = "模型类型", example = "指纹检测")
    private String modelType;

    /**
     * 模型版本
     */
    @JsonProperty("model_version")
    @Schema(description = "模型版本", example = "1.0.0")
    private String modelVersion;

    /**
     * 模型描述
     */
    @JsonProperty("remark")
    @Schema(description = "模型描述", example = "基于SSL指纹特征的检测模型")
    private String remark;

    /**
     * 模型状态：0-失效，1-生效
     */
    @NotNull(message = "模型状态不能为空")
    @JsonProperty("state")
    @Schema(description = "模型状态", example = "1", required = true)
    private Integer state;

    /**
     * 模型配置参数（JSON格式）
     */
    @JsonProperty("model_config")
    @Schema(description = "模型配置参数", example = "{\"threshold\": 0.8, \"timeout\": 30}")
    private String modelConfig;

    /**
     * 模型文件路径
     */
    @JsonProperty("model_path")
    @Schema(description = "模型文件路径", example = "/models/ssl_fingerprint_v1.0.0.model")
    private String modelPath;

    /**
     * 模型优先级
     */
    @JsonProperty("priority")
    @Schema(description = "模型优先级", example = "100")
    private Integer priority;

    /**
     * 模型标签（多个标签用逗号分隔）
     */
    @JsonProperty("tags")
    @Schema(description = "模型标签", example = "SSL,指纹,检测")
    private String tags;
}
