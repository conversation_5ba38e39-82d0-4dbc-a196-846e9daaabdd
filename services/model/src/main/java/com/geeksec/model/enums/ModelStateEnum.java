package com.geeksec.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模型状态枚举
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Getter
@AllArgsConstructor
public enum ModelStateEnum {

    /**
     * 失效状态
     */
    DISABLED(0, "失效"),

    /**
     * 生效状态
     */
    ENABLED(1, "生效");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 模型状态枚举
     */
    public static ModelStateEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (ModelStateEnum state : values()) {
            if (state.getCode().equals(code)) {
                return state;
            }
        }
        return null;
    }

    /**
     * 检查状态码是否有效
     * 
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}
