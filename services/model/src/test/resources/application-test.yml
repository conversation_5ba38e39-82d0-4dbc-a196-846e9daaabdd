# 测试环境配置
spring:
  # 数据库配置 - 使用H2内存数据库
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    
  # 禁用Redis
  data:
    redis:
      host: localhost
      port: 6379
      database: 15

  # 禁用Kafka
  kafka:
    bootstrap-servers: localhost:9092

# MyBatis-Flex 配置
mybatis-flex:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 日志配置
logging:
  level:
    com.geeksec.model: DEBUG
    org.springframework.kafka: WARN
    org.apache.kafka: WARN
