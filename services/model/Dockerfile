FROM openjdk:17-jdk-slim

LABEL maintainer="NTA 3.0 Team <<EMAIL>>"
LABEL description="NTA 3.0 Detection Model Management Service"
LABEL version="3.0.0"

# 设置工作目录
WORKDIR /app

# 创建应用用户
RUN groupadd -r nta && useradd -r -g nta nta

# 复制应用 JAR 文件
COPY target/model-*.jar app.jar

# 创建日志目录
RUN mkdir -p /app/logs && chown -R nta:nta /app

# 切换到应用用户
USER nta

# 暴露端口
EXPOSE 8087

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8087/model/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-jar", "-Dspring.profiles.active=prod", "app.jar"]
