package com.geeksec.nta.system.application.service;

import com.alibaba.fastjson2.JSONObject;
import com.geeksec.nta.system.domain.service.DataReadService;
import com.geeksec.nta.system.infrastructure.external.CommandExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据读取服务实现类
 * 实现 Colony 数据、PB 会话数据读取和数据库统计信息获取功能
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DataReadServiceImpl implements DataReadService {

    @Autowired
    private CommandExecutor commandExecutor;



    @Override
    public JSONObject readColonyData(String dataPath) {
        log.info("读取Colony数据，路径: {}", dataPath);

        try {
            JSONObject result = new JSONObject();
            
            // 检查数据路径
            if (dataPath == null || dataPath.trim().isEmpty()) {
                dataPath = "/data/colony";
            }

            Path path = Paths.get(dataPath);
            if (!Files.exists(path)) {
                return createErrorResponse("Colony数据路径不存在: " + dataPath);
            }

            // 读取Colony数据文件
            List<JSONObject> colonyData = new ArrayList<>();
            
            if (Files.isDirectory(path)) {
                // 扫描目录中的数据文件
                Files.walk(path)
                     .filter(Files::isRegularFile)
                     .filter(p -> p.toString().endsWith(".dat") || p.toString().endsWith(".pb"))
                     .forEach(p -> {
                         try {
                             JSONObject fileData = readColonyFile(p.toString());
                             colonyData.add(fileData);
                         } catch (Exception e) {
                             log.warn("读取Colony文件失败: {}", p.toString());
                         }
                     });
            } else {
                // 读取单个文件
                JSONObject fileData = readColonyFile(dataPath);
                colonyData.add(fileData);
            }

            result.put("status", "true");
            result.put("data_path", dataPath);
            result.put("colony_data", colonyData);
            result.put("file_count", colonyData.size());

            return result;

        } catch (Exception e) {
            log.error("读取Colony数据失败: {}", e.getMessage());
            return createErrorResponse("读取Colony数据失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject readPbSessionData(String sessionPath) {
        log.info("读取PB会话数据，路径: {}", sessionPath);

        try {
            JSONObject result = new JSONObject();
            
            // 检查会话路径
            if (sessionPath == null || sessionPath.trim().isEmpty()) {
                sessionPath = "/data/pbfiles";
            }

            Path path = Paths.get(sessionPath);
            if (!Files.exists(path)) {
                return createErrorResponse("PB会话数据路径不存在: " + sessionPath);
            }

            // 读取PB会话数据
            List<JSONObject> sessionData = new ArrayList<>();
            
            if (Files.isDirectory(path)) {
                Files.walk(path)
                     .filter(Files::isRegularFile)
                     .filter(p -> p.toString().endsWith(".pb"))
                     .limit(100) // 限制读取数量，避免内存溢出
                     .forEach(p -> {
                         try {
                             JSONObject fileInfo = new JSONObject();
                             fileInfo.put("file", p.toString());
                             fileInfo.put("size", Files.size(p));
                             fileInfo.put("last_modified", Files.getLastModifiedTime(p).toMillis());
                             sessionData.add(fileInfo);
                         } catch (Exception e) {
                             log.warn("读取PB文件信息失败: {}", p.toString());
                         }
                     });
            }

            result.put("status", "true");
            result.put("session_path", sessionPath);
            result.put("session_files", sessionData);
            result.put("file_count", sessionData.size());

            return result;

        } catch (Exception e) {
            log.error("读取PB会话数据失败: {}", e.getMessage());
            return createErrorResponse("读取PB会话数据失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject getDatabaseStats(String dbPath) {
        log.info("获取数据库统计信息，路径: {}", dbPath);

        try {
            JSONObject result = new JSONObject();
            
            Path path = Paths.get(dbPath);
            if (!Files.exists(path)) {
                return createErrorResponse("数据库路径不存在: " + dbPath);
            }

            // 获取数据库统计信息
            String statCommand = String.format("mdb_stat %s", dbPath);
            String statOutput = commandExecutor.executeCommand(statCommand);

            result.put("status", "true");
            result.put("db_path", dbPath);
            result.put("stats", parseStatOutput(statOutput));
            result.put("file_size", Files.size(path));

            return result;

        } catch (Exception e) {
            log.error("获取数据库统计信息失败: {}", e.getMessage());
            return createErrorResponse("获取数据库统计信息失败: " + e.getMessage());
        }
    }



    /**
     * 读取Colony文件
     */
    private JSONObject readColonyFile(String filePath) throws IOException {
        JSONObject fileData = new JSONObject();
        Path path = Paths.get(filePath);
        
        fileData.put("file", filePath);
        fileData.put("size", Files.size(path));
        fileData.put("last_modified", Files.getLastModifiedTime(path).toMillis());
        
        // 如果是文本文件，可以读取内容
        if (filePath.endsWith(".txt") || filePath.endsWith(".json")) {
            try {
                String content = Files.readString(path);
                fileData.put("content", content);
            } catch (Exception e) {
                fileData.put("content", "无法读取文件内容");
            }
        }
        
        return fileData;
    }

    /**
     * 解析统计输出
     */
    private JSONObject parseStatOutput(String output) {
        JSONObject stats = new JSONObject();
        String[] lines = output.split("\n");
        
        for (String line : lines) {
            if (line.contains(":")) {
                String[] parts = line.split(":", 2);
                if (parts.length == 2) {
                    String key = parts[0].trim().toLowerCase().replace(" ", "_");
                    String value = parts[1].trim();
                    stats.put(key, value);
                }
            }
        }
        
        return stats;
    }

    /**
     * 创建错误响应
     */
    private JSONObject createErrorResponse(String message) {
        JSONObject response = new JSONObject();
        response.put("status", "false");
        response.put("error", message);
        return response;
    }
}
