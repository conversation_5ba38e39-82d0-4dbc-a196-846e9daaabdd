package com.geeksec.nta.system.interfaces.rest;

import com.alibaba.fastjson2.JSONObject;
import com.geeksec.common.controller.BaseController;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.nta.system.domain.service.DataReadService;
import com.geeksec.nta.system.domain.service.LibraryCheckService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 数据管理控制器
 * 提供 PB 数据、Colony 数据读取和动态库检测等功能
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "数据管理接口", description = "数据管理接口相关操作")
@RequestMapping("/data")
public class DataController extends BaseController {

    @Autowired
    private DataReadService dataReadService;

    @Autowired
    private LibraryCheckService libraryCheckService;

    /**
     * 读取PB数据
     *
     * @param request 请求参数
     * @return 读取结果
     */
    @PostMapping("/pb")
    @Operation(summary = "读取PB数据", description = "读取PB数据操作")
    public ApiResponse<JSONObject> readPbData(@RequestBody(required = false) JSONObject request) {
        try {
            String sessionPath = null;
            if (request != null) {
                sessionPath = request.getString("session_path");
            }
            JSONObject result = dataReadService.readPbSessionData(sessionPath);
            return success(result);
        } catch (Exception e) {
            log.error("读取PB数据失败", e);
            return ApiResponse.error("读取PB数据失败: " + e.getMessage());
        }
    }

    /**
     * 读取Colony PB数据
     *
     * @param request 请求参数
     * @return 读取结果
     */
    @PostMapping("/colonypb")
    @Operation(summary = "读取Colony PB数据", description = "读取Colony PB数据操作")
    public ApiResponse<JSONObject> readColonyPbData(@RequestBody(required = false) JSONObject request) {
        try {
            String dataPath = null;
            if (request != null) {
                dataPath = request.getString("data_path");
            }
            JSONObject result = dataReadService.readColonyData(dataPath);
            return success(result);
        } catch (Exception e) {
            log.error("读取Colony PB数据失败", e);
            return ApiResponse.error("读取Colony PB数据失败: " + e.getMessage());
        }
    }



    /**
     * 获取数据库统计信息
     *
     * @param request 请求参数
     * @return 统计信息
     */
    @PostMapping("/stats")
    @Operation(summary = "获取数据库统计信息", description = "获取数据库统计信息操作")
    public ApiResponse<JSONObject> getDatabaseStats(@RequestBody JSONObject request) {
        try {
            String dbPath = request.getString("db_path");

            if (dbPath == null || dbPath.trim().isEmpty()) {
                return ApiResponse.error("数据库路径不能为空");
            }

            JSONObject result = dataReadService.getDatabaseStats(dbPath);
            return success(result);
        } catch (Exception e) {
            log.error("获取数据库统计信息失败", e);
            return ApiResponse.error("获取数据库统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 检测动态库文件
     *
     * @param request 请求参数
     * @return 检测结果
     */
    @PostMapping("/check/so")
    @Operation(summary = "检测动态库文件", description = "检测动态库文件操作")
    public ApiResponse<JSONObject> checkSoFiles(@RequestBody(required = false) JSONObject request) {
        try {
            Integer ruleId = null;
            if (request != null) {
                ruleId = request.getInteger("rule_id");
            }
            JSONObject result = libraryCheckService.checkSoFiles(ruleId);
            return success(result);
        } catch (Exception e) {
            log.error("检测动态库文件失败", e);
            return ApiResponse.error("检测动态库文件失败: " + e.getMessage());
        }
    }

    /**
     * 检测Docker动态库文件
     *
     * @param request 请求参数
     * @return 检测结果
     */
    @PostMapping("/check/docker/so")
    @Operation(summary = "检测Docker动态库文件", description = "检测Docker动态库文件操作")
    public ApiResponse<JSONObject> checkDockerSoFiles(@RequestBody JSONObject request) {
        try {
            String path = request.getString("path");

            if (path == null || path.trim().isEmpty()) {
                return ApiResponse.error("路径不能为空");
            }

            JSONObject result = libraryCheckService.checkDockerSoFiles(path);
            return success(result);
        } catch (Exception e) {
            log.error("检测Docker动态库文件失败", e);
            return ApiResponse.error("检测Docker动态库文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统已安装的动态库列表
     *
     * @return 动态库列表
     */
    @GetMapping("/libraries")
    @Operation(summary = "获取系统已安装的动态库列表", description = "获取系统已安装的动态库列表操作")
    public ApiResponse<JSONObject> getInstalledLibraries() {
        try {
            JSONObject result = libraryCheckService.getInstalledLibraries();
            return success(result);
        } catch (Exception e) {
            log.error("获取系统已安装的动态库列表失败", e);
            return ApiResponse.error("获取系统已安装的动态库列表失败: " + e.getMessage());
        }
    }

    /**
     * 检测库文件依赖
     *
     * @param request 请求参数
     * @return 检测结果
     */
    @PostMapping("/check/dependencies")
    @Operation(summary = "检测库文件依赖", description = "检测库文件依赖操作")
    public ApiResponse<JSONObject> checkLibraryDependencies(@RequestBody JSONObject request) {
        try {
            String libraryPath = request.getString("library_path");

            if (libraryPath == null || libraryPath.trim().isEmpty()) {
                return ApiResponse.error("库文件路径不能为空");
            }

            JSONObject result = libraryCheckService.checkLibraryDependencies(libraryPath);
            return success(result);
        } catch (Exception e) {
            log.error("检测库文件依赖失败", e);
            return ApiResponse.error("检测库文件依赖失败: " + e.getMessage());
        }
    }
}
