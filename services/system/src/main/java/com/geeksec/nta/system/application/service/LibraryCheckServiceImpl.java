package com.geeksec.nta.system.application.service;

import com.alibaba.fastjson2.JSONObject;
import com.geeksec.nta.system.domain.service.LibraryCheckService;
import com.geeksec.nta.system.infrastructure.external.CommandExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

/**
 * 动态库检测服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class LibraryCheckServiceImpl implements LibraryCheckService {

    @Autowired
    private CommandExecutor commandExecutor;

    @Override
    public JSONObject checkSoFiles(Integer ruleId) {
        log.info("检测动态库文件，规则ID: {}", ruleId);

        try {
            JSONObject result = new JSONObject();
            List<String> missingLibraries = new ArrayList<>();
            List<String> availableLibraries = new ArrayList<>();

            // 检测常用的动态库
            String[] commonLibraries = {
                "libc.so.6",
                "libm.so.6",
                "libpthread.so.0",
                "libdl.so.2",
                "librt.so.1",
                "libssl.so",
                "libcrypto.so",
                "libz.so.1"
            };

            for (String library : commonLibraries) {
                if (isLibraryAvailable(library)) {
                    availableLibraries.add(library);
                } else {
                    missingLibraries.add(library);
                }
            }

            // 检测特定规则相关的库
            if (ruleId != null) {
                List<String> ruleSpecificLibraries = getRuleSpecificLibraries(ruleId);
                for (String library : ruleSpecificLibraries) {
                    if (isLibraryAvailable(library)) {
                        availableLibraries.add(library);
                    } else {
                        missingLibraries.add(library);
                    }
                }
            }

            result.put("status", "true");
            result.put("available_libraries", availableLibraries);
            result.put("missing_libraries", missingLibraries);
            result.put("total_checked", availableLibraries.size() + missingLibraries.size());
            result.put("success_rate", 
                availableLibraries.size() * 100.0 / (availableLibraries.size() + missingLibraries.size()));

            return result;

        } catch (Exception e) {
            log.error("检测动态库文件失败: {}", e.getMessage());
            return createErrorResponse("检测动态库文件失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject checkDockerSoFiles(String path) {
        log.info("检测Docker动态库文件，路径: {}", path);

        try {
            JSONObject result = new JSONObject();
            
            // 检查路径是否存在
            if (path == null || path.trim().isEmpty()) {
                return createErrorResponse("路径不能为空");
            }

            Path dockerPath = Paths.get(path);
            if (!Files.exists(dockerPath)) {
                return createErrorResponse("指定路径不存在: " + path);
            }

            // 在Docker容器中查找.so文件
            String findCommand = String.format("find %s -name '*.so*' -type f", path);
            String findResult = commandExecutor.executeCommand(findCommand);

            List<String> soFiles = new ArrayList<>();
            if (!findResult.isEmpty()) {
                soFiles = Arrays.asList(findResult.split("\n"));
            }

            // 检测每个.so文件的依赖
            List<JSONObject> fileDetails = new ArrayList<>();
            for (String soFile : soFiles) {
                if (soFile.trim().isEmpty()) continue;
                
                JSONObject fileInfo = new JSONObject();
                fileInfo.put("file", soFile);
                
                try {
                    // 使用ldd检查依赖
                    String lddCommand = "ldd " + soFile;
                    String dependencies = commandExecutor.executeCommand(lddCommand);
                    fileInfo.put("dependencies", dependencies.split("\n"));
                    fileInfo.put("status", "ok");
                } catch (IOException e) {
                    fileInfo.put("status", "error");
                    fileInfo.put("error", e.getMessage());
                }
                
                fileDetails.add(fileInfo);
            }

            result.put("status", "true");
            result.put("path", path);
            result.put("so_files_count", soFiles.size());
            result.put("so_files", fileDetails);

            return result;

        } catch (Exception e) {
            log.error("检测Docker动态库文件失败: {}", e.getMessage());
            return createErrorResponse("检测Docker动态库文件失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject checkLibraryDependencies(String libraryPath) {
        log.info("检测库文件依赖: {}", libraryPath);

        try {
            JSONObject result = new JSONObject();
            
            // 检查文件是否存在
            Path libPath = Paths.get(libraryPath);
            if (!Files.exists(libPath)) {
                return createErrorResponse("库文件不存在: " + libraryPath);
            }

            // 使用ldd检查依赖
            String lddCommand = "ldd " + libraryPath;
            String dependencies = commandExecutor.executeCommand(lddCommand);

            // 解析依赖信息
            List<JSONObject> dependencyList = new ArrayList<>();
            String[] lines = dependencies.split("\n");
            
            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty()) continue;
                
                JSONObject dep = new JSONObject();
                if (line.contains("=>")) {
                    String[] parts = line.split("=>");
                    dep.put("name", parts[0].trim());
                    if (parts.length > 1) {
                        String location = parts[1].trim();
                        if (location.contains("(")) {
                            location = location.substring(0, location.indexOf("(")).trim();
                        }
                        dep.put("location", location);
                        dep.put("found", !location.equals("not found"));
                    }
                } else {
                    dep.put("name", line);
                    dep.put("found", true);
                }
                dependencyList.add(dep);
            }

            result.put("status", "true");
            result.put("library", libraryPath);
            result.put("dependencies", dependencyList);
            result.put("dependency_count", dependencyList.size());

            return result;

        } catch (Exception e) {
            log.error("检测库文件依赖失败: {}", e.getMessage());
            return createErrorResponse("检测库文件依赖失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject getInstalledLibraries() {
        log.info("获取系统已安装的动态库列表");

        try {
            JSONObject result = new JSONObject();
            List<String> libraryPaths = Arrays.asList(
                "/lib", "/lib64", "/usr/lib", "/usr/lib64", "/usr/local/lib"
            );

            List<JSONObject> libraries = new ArrayList<>();
            
            for (String libPath : libraryPaths) {
                Path path = Paths.get(libPath);
                if (!Files.exists(path)) continue;

                try (Stream<Path> files = Files.walk(path, 2)) {
                    files.filter(Files::isRegularFile)
                         .filter(p -> p.toString().endsWith(".so") || p.toString().contains(".so."))
                         .forEach(p -> {
                             JSONObject lib = new JSONObject();
                             lib.put("name", p.getFileName().toString());
                             lib.put("path", p.toString());
                             try {
                                 lib.put("size", Files.size(p));
                             } catch (IOException e) {
                                 lib.put("size", -1);
                             }
                             libraries.add(lib);
                         });
                } catch (IOException e) {
                    log.warn("扫描库目录失败: {}", libPath);
                }
            }

            result.put("status", "true");
            result.put("library_paths", libraryPaths);
            result.put("libraries", libraries);
            result.put("total_count", libraries.size());

            return result;

        } catch (Exception e) {
            log.error("获取系统动态库列表失败: {}", e.getMessage());
            return createErrorResponse("获取系统动态库列表失败: " + e.getMessage());
        }
    }

    /**
     * 检查库是否可用
     */
    private boolean isLibraryAvailable(String libraryName) {
        try {
            String command = "ldconfig -p | grep " + libraryName;
            String result = commandExecutor.executeCommand(command);
            return !result.trim().isEmpty();
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * 获取规则特定的库列表
     */
    private List<String> getRuleSpecificLibraries(Integer ruleId) {
        // 根据规则ID返回特定的库列表
        // 这里可以从数据库或配置文件中获取
        List<String> libraries = new ArrayList<>();
        
        switch (ruleId) {
            case 1:
                libraries.addAll(Arrays.asList("libpcap.so", "libnet.so"));
                break;
            case 2:
                libraries.addAll(Arrays.asList("libxml2.so", "libjson.so"));
                break;
            default:
                // 默认检测常用库
                break;
        }
        
        return libraries;
    }

    /**
     * 创建错误响应
     */
    private JSONObject createErrorResponse(String message) {
        JSONObject response = new JSONObject();
        response.put("status", "false");
        response.put("error", message);
        return response;
    }
}
