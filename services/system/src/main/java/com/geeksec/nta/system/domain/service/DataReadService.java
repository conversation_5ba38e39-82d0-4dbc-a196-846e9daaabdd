package com.geeksec.nta.system.domain.service;

import com.alibaba.fastjson2.JSONObject;

/**
 * 数据读取服务接口
 * 提供 Colony 数据、PB 会话数据读取和数据库统计信息获取功能
 *
 * <AUTHOR>
 */
public interface DataReadService {

    /**
     * 读取Colony数据
     *
     * @param dataPath 数据路径
     * @return 读取结果
     */
    JSONObject readColonyData(String dataPath);

    /**
     * 读取PB会话数据
     *
     * @param sessionPath 会话路径
     * @return 读取结果
     */
    JSONObject readPbSessionData(String sessionPath);

    /**
     * 获取数据库统计信息
     *
     * @param dbPath 数据库路径
     * @return 统计信息
     */
    JSONObject getDatabaseStats(String dbPath);
}
