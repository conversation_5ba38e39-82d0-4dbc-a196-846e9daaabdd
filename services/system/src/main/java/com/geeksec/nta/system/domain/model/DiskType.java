package com.geeksec.nta.system.domain.model;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 磁盘类型实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Table("tb_disk_type")
public class DiskType implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Auto)
    private Integer id;

    /**
     * 0 进行中，1 已完成
     */
    @Column("state")
    private Integer state;

    @Column("type")
    private Integer type;

    @Column("start_time")
    private Long startTime;

    @Column("end_time")
    private Long endTime;
}
