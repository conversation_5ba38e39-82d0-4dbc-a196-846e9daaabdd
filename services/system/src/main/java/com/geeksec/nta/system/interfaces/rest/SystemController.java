package com.geeksec.nta.system.interfaces.rest;

import com.alibaba.fastjson2.JSONObject;
import com.geeksec.common.controller.BaseController;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.nta.system.domain.model.CleanCondition;
import com.geeksec.nta.system.domain.model.ProductInfoVo;
import com.geeksec.nta.system.domain.model.SystemInfoVo;
import com.geeksec.nta.system.domain.service.SystemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统管理控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "系统控制接口", description = "系统管理相关操作")
@RequestMapping("/system")
public class SystemController extends BaseController {

    @Autowired
    private SystemService systemService;

    /**
     * 关闭主机操作
     * 
     * @param passwordJson 密码信息
     * @return 执行结果
     */
    @PostMapping("/shutdown")
    @Operation(summary = "关闭主机", description = "执行系统关机操作")
    public JSONObject shutdown(@RequestBody JSONObject passwordJson) {
        // 密码不能为null
        if (!passwordJson.containsKey("password")) {
            throw new RuntimeException("密码不能为空");
        }

        // TODO: 验证管理员密码
        String password = passwordJson.getString("password");
        if (!isAdminPassword(password)) {
            throw new RuntimeException("密码错误");
        }

        return systemService.shutdown();
    }

    /**
     * 重启主机操作
     * 
     * @param passwordJson 密码信息
     * @return 执行结果
     */
    @PostMapping("/reboot")
    @Operation(summary = "重启主机", description = "重启主机操作")
    public JSONObject reboot(@RequestBody JSONObject passwordJson) {
        // 密码不能为null
        if (!passwordJson.containsKey("password")) {
            throw new RuntimeException("密码不能为空");
        }

        // TODO: 验证管理员密码
        String password = passwordJson.getString("password");
        if (!isAdminPassword(password)) {
            throw new RuntimeException("密码错误");
        }

        return systemService.reboot();
    }

    /**
     * 修改密码
     * 
     * @param passwordJson 密码信息
     * @return 执行结果
     */
    @PostMapping("/change/password")
    @Operation(summary = "修改密码", description = "修改密码操作")
    public JSONObject changePassword(@RequestBody JSONObject passwordJson) {
        if (!passwordJson.containsKey("userName") || !passwordJson.containsKey("password")) {
            throw new RuntimeException("用户名和密码不能为空");
        }

        String userName = passwordJson.getString("userName");
        String password = passwordJson.getString("password");

        return systemService.changePassword(userName, password);
    }

    /**
     * 获取磁盘信息
     * 
     * @return 磁盘信息
     */
    @PostMapping("/disk/info")
    @Operation(summary = "获取磁盘信息", description = "获取磁盘信息操作")
    public JSONObject getDiskInfo() {
        return systemService.getDiskInfoData();
    }

    /**
     * 获取系统信息
     * 
     * @return 系统信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取系统信息", description = "获取系统信息操作")
    public SystemInfoVo getSystemInfo() {
        return systemService.getSystemInfo();
    }

    /**
     * 获取产品信息
     * 
     * @return 产品信息
     */
    @GetMapping("/product/info")
    @Operation(summary = "获取产品信息", description = "获取产品信息操作")
    public ProductInfoVo getProductInfo() {
        return systemService.getProductInfo();
    }

    /**
     * 数据清理
     * 
     * @param condition 清理条件
     * @return 执行结果
     */
    @PostMapping("/clean/data")
    @Operation(summary = "数据清理", description = "数据清理操作")
    public JSONObject cleanData(@RequestBody CleanCondition condition) {
        // 参数校验
        Integer taskId = condition.getTaskId();
        Integer userId = condition.getUserId();
        List<String> cleanList = condition.getCleanList();
        if (taskId == null || cleanList == null || cleanList.size() < 1 || userId == null) {
            throw new RuntimeException("请求参数不能为空");
        }

        return systemService.cleanData(condition);
    }

    /**
     * 系统重置
     * 
     * @param json 重置参数
     * @return 执行结果
     */
    @PostMapping("/reset")
    @Operation(summary = "系统重置", description = "系统重置操作")
    public JSONObject systemReset(@RequestBody JSONObject json) {
        if (!json.containsKey("user_id")) {
            throw new RuntimeException("用户ID不能为空");
        }
        return systemService.systemReset(json);
    }

    /**
     * 数据清理进度查询
     * 
     * @return 清理进度
     */
    @PostMapping("/clean/schedule")
    @Operation(summary = "数据清理进度查询", description = "数据清理进度查询操作")
    public JSONObject cleanDataSchedule() {
        return systemService.cleanDataSchedule();
    }

    /**
     * 更新磁盘
     * 
     * @return 执行结果
     */
    @PostMapping("/disk/change")
    @Operation(summary = "更新磁盘", description = "更新磁盘操作")
    public JSONObject diskChange() {
        return systemService.diskChange();
    }

    /**
     * 重组磁盘
     * 
     * @return 执行结果
     */
    @PostMapping("/disk/rebuild")
    @Operation(summary = "重组磁盘", description = "重组磁盘操作")
    public JSONObject diskRebuild() {
        return systemService.diskRebuild();
    }

    /**
     * 准备挂载磁盘
     * 
     * @return 执行结果
     */
    @PostMapping("/disk/mount/ready")
    @Operation(summary = "准备挂载磁盘", description = "准备挂载磁盘操作")
    public JSONObject diskMountReady() {
        return systemService.diskMountReady();
    }

    /**
     * 挂载磁盘
     * 
     * @return 执行结果
     */
    @PostMapping("/disk/mount/data")
    @Operation(summary = "挂载磁盘", description = "挂载磁盘操作")
    public JSONObject diskMountData() {
        return systemService.diskMountData();
    }

    /**
     * 动态库文件检测
     *
     * @param json 检测参数
     * @return 检测结果
     */
    @PostMapping("/check/so")
    @Operation(summary = "动态库文件检测", description = "动态库文件检测操作")
    public ApiResponse<JSONObject> checkSo(@RequestBody JSONObject json) {
        try {
            Integer ruleId = json.getInteger("rule_id");
            JSONObject result = systemService.checkSo(ruleId);
            return success(result);
        } catch (Exception e) {
            log.error("动态库文件检测失败", e);
            return ApiResponse.error("动态库文件检测失败: " + e.getMessage());
        }
    }

    /**
     * Docker动态库文件检测
     *
     * @param json 检测参数
     * @return 检测结果
     */
    @PostMapping("/docker/check/so")
    @Operation(summary = "Docker动态库文件检测", description = "Docker动态库文件检测操作")
    public ApiResponse<JSONObject> dockerCheckSo(@RequestBody JSONObject json) {
        try {
            String path = json.getString("path");
            JSONObject result = systemService.dockerCheckSo(path);
            return success(result);
        } catch (Exception e) {
            log.error("Docker动态库文件检测失败", e);
            return ApiResponse.error("Docker动态库文件检测失败: " + e.getMessage());
        }
    }

    /**
     * 查询磁盘重组状态
     * 
     * @return 磁盘状态
     */
    @PostMapping("/disk/status")
    @Operation(summary = "查询磁盘重组状态", description = "查询磁盘重组状态操作")
    public JSONObject checkDiskStatus() {
        return systemService.checkDiskStatus();
    }

    /**
     * 获取磁盘字段信息
     * 
     * @return 磁盘字段信息
     */
    @GetMapping("/disk/field")
    @Operation(summary = "获取磁盘字段信息", description = "获取磁盘字段信息操作")
    public JSONObject getDiskField() {
        return systemService.getDiskField();
    }

    /**
     * 验证管理员密码
     * 
     * @param password 密码
     * @return 是否为管理员密码
     */
    private boolean isAdminPassword(String password) {
        // TODO: 实现密码验证逻辑
        return true;
    }
}
