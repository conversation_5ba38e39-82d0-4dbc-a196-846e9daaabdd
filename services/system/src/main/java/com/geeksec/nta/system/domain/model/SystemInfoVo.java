package com.geeksec.nta.system.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统信息展示对象
 * 
 * <AUTHOR>
 */
@Data
public class SystemInfoVo {

    /**
     * 主机名称
     */
    @JsonProperty("hostname")
    private String hostname;

    /**
     * 操作系统
     */
    @JsonProperty("os_info")
    private String osinfo;

    /**
     * 系统时间
     */
    @JsonProperty("timeS")
    private String timeS;

    /**
     * 运行时间
     */
    @JsonProperty("time")
    private Long time;

    /**
     * 启动时间
     */
    @JsonProperty("start_time")
    private LocalDateTime startTime;
}
