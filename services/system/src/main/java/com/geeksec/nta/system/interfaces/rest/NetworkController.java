package com.geeksec.nta.system.interfaces.rest;

import com.alibaba.fastjson2.JSONObject;
import com.geeksec.nta.system.domain.model.NetworkConfig;
import com.geeksec.nta.system.domain.service.NetworkService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 网络管理控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "网络管理接口", description = "网络管理接口相关操作")
@RequestMapping("/network")
public class NetworkController {

    @Autowired
    private NetworkService networkService;

    /**
     * 修改IP配置
     *
     * @param config 网络配置
     * @return 操作结果
     */
    @PostMapping("/config")
    @Operation(summary = "修改网络配置", description = "修改网络配置操作")
    public JSONObject modifyNetworkConfig(@RequestBody NetworkConfig config) {
        log.info("收到网络配置修改请求: {}", config);

        if ("mod_ip".equals(config.getType())) {
            return networkService.modifyIpConfig(config);
        } else if ("set_ntp".equals(config.getType())) {
            return networkService.setNtpServer(config.getNtpServer());
        } else {
            JSONObject response = new JSONObject();
            response.put("result_code", "0");
            response.put("result_desc", "不支持的操作类型: " + config.getType());
            return response;
        }
    }

    /**
     * 获取网络配置信息
     *
     * @return 网络配置
     */
    @GetMapping("/info")
    @Operation(summary = "获取网络配置信息", description = "获取网络配置信息操作")
    public JSONObject getNetworkInfo() {
        NetworkConfig config = networkService.getNetworkConfig();
        
        JSONObject response = new JSONObject();
        response.put("result_code", "1");
        response.put("result_desc", "Success");
        response.put("data", config);
        return response;
    }

    /**
     * 获取网络设备信息
     *
     * @param deviceName 设备名称
     * @return 设备信息
     */
    @GetMapping("/device/{deviceName}")
    @Operation(summary = "获取网络设备信息", description = "获取网络设备信息操作")
    public JSONObject getNetworkDeviceInfo(@PathVariable String deviceName) {
        return networkService.getNetworkDeviceInfo(deviceName);
    }

    /**
     * 获取所有网络接口信息
     *
     * @return 网络接口列表
     */
    @GetMapping("/interfaces")
    @Operation(summary = "获取所有网络接口信息", description = "获取所有网络接口信息操作")
    public JSONObject getAllNetworkInterfaces() {
        return networkService.getAllNetworkInterfaces();
    }

    /**
     * 设置NTP服务器
     *
     * @param ntpConfig NTP配置
     * @return 操作结果
     */
    @PostMapping("/ntp")
    @Operation(summary = "设置NTP服务器", description = "设置NTP服务器操作")
    public JSONObject setNtpServer(@RequestBody JSONObject ntpConfig) {
        String ntpServer = ntpConfig.getString("ntp_server");
        if (ntpServer == null || ntpServer.trim().isEmpty()) {
            JSONObject response = new JSONObject();
            response.put("result_code", "0");
            response.put("result_desc", "NTP服务器地址不能为空");
            return response;
        }

        return networkService.setNtpServer(ntpServer);
    }
}
