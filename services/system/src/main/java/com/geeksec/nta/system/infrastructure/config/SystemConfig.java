package com.geeksec.nta.system.infrastructure.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * 系统管理服务配置类
 *
 * <AUTHOR>
 */
@Configuration
public class SystemConfig {

    /**
     * 配置RestTemplate
     */
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    /**
     * 配置OpenAPI文档
     */
    @Bean
    public OpenAPI systemOpenApi() {
        return new OpenAPI()
                .info(new Info()
                        .title("系统管理服务API")
                        .description("NTA 3.0 系统管理服务接口文档")
                        .version("3.0.0"));
    }
}
