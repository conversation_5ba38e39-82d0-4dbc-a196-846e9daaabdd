# System Service 生产环境配置
# 继承通用生产环境配置

spring:
  profiles:
    active: prod
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8089}
  servlet:
    context-path: /api/system

# 系统管理配置
system-management:
  # 命令执行超时时间（秒）
  command-timeout: 120
  # NTP配置
  ntp:
    config-file: /etc/ntp.conf
    backup-file: /etc/ntp.conf.backup
    service-name: ntpd
  # 系统权限配置
  privileges:
    use-sudo: true  # 生产环境使用sudo
    sudo-path: /usr/bin/sudo

# 生产环境外部服务配置
external:
  services:
    system-url: ${EXTERNAL_SYSTEM_URL:http://prod-system.nta.local:59000}

# 生产环境安全配置
security:
  enable-command-audit: true
  enable-privilege-check: true
  allowed-commands: ${ALLOWED_SYSTEM_COMMANDS:}

# 日志配置
logging:
  level:
    '[com.geeksec.nta.system]': INFO
