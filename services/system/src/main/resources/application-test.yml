# System Service 测试环境配置
# 继承通用测试环境配置

spring:
  profiles:
    active: test
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8089}
  servlet:
    context-path: /api/system

# 系统管理配置
system-management:
  # 命令执行超时时间（秒）
  command-timeout: 60
  # NTP配置
  ntp:
    config-file: /etc/ntp.conf
    backup-file: /etc/ntp.conf.backup
    service-name: ntpd
  # 系统权限配置
  privileges:
    use-sudo: true  # 测试环境使用sudo
    sudo-path: /usr/bin/sudo

# 测试环境外部服务配置
external:
  services:
    system-url: ${EXTERNAL_SYSTEM_URL:http://test-system.nta.local:59000}

# 日志配置
logging:
  level:
    '[com.geeksec.nta.system]': INFO
