package com.geeksec.metadata.controller;

import com.geeksec.metadata.model.dto.SystemDictResponse;
import com.geeksec.metadata.model.entity.CyberKillChainDetails;
import com.geeksec.metadata.model.enums.CyberKillChain;
import com.geeksec.metadata.service.CyberKillChainService;
import com.geeksec.metadata.service.SystemDictService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Cyber Kill Chain 控制器
 * 提供 Cyber Kill Chain 相关的查询接口
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/metadata/cyber-kill-chain")
@RequiredArgsConstructor
@Tag(name = "Cyber Kill Chain 管理", description = "提供 Cyber Kill Chain 相关的查询和管理功能")
public class CyberKillChainController {

    private final CyberKillChainService cyberKillChainService;
    private final SystemDictService systemDictService;

    @GetMapping
    @Operation(summary = "获取所有 Cyber Kill Chain 阶段详情", description = "获取系统中所有 Cyber Kill Chain 阶段的详细信息")
    public ResponseEntity<List<CyberKillChainDetails>> getAllCyberKillChainDetails() {
        log.info("获取所有 Cyber Kill Chain 阶段详情");
        List<CyberKillChainDetails> details = cyberKillChainService.getAllCyberKillChainDetails();
        return ResponseEntity.ok(details);
    }

    @GetMapping("/{cyberKillChain}")
    @Operation(summary = "根据 Cyber Kill Chain 阶段获取详情", description = "根据指定的 Cyber Kill Chain 阶段获取详细信息")
    public ResponseEntity<CyberKillChainDetails> getCyberKillChainDetails(
            @Parameter(description = "Cyber Kill Chain 阶段") @PathVariable CyberKillChain cyberKillChain) {
        log.info("根据 Cyber Kill Chain 阶段获取详情: {}", cyberKillChain);
        CyberKillChainDetails details = cyberKillChainService.getCyberKillChainDetails(cyberKillChain);
        if (details != null) {
            return ResponseEntity.ok(details);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/severity/{severityLevel}")
    @Operation(summary = "根据严重程度等级获取 Cyber Kill Chain 阶段", description = "根据严重程度等级获取相关的 Cyber Kill Chain 阶段")
    public ResponseEntity<List<CyberKillChainDetails>> getCyberKillChainsBySeverityLevel(
            @Parameter(description = "严重程度等级") @PathVariable Integer severityLevel) {
        log.info("根据严重程度等级获取 Cyber Kill Chain 阶段: {}", severityLevel);
        List<CyberKillChainDetails> details = cyberKillChainService.getCyberKillChainsBySeverityLevel(severityLevel);
        return ResponseEntity.ok(details);
    }

    @GetMapping("/severity-range")
    @Operation(summary = "根据严重程度等级范围获取 Cyber Kill Chain 阶段", description = "根据严重程度等级范围获取相关的 Cyber Kill Chain 阶段")
    public ResponseEntity<List<CyberKillChainDetails>> getCyberKillChainsBySeverityLevelRange(
            @Parameter(description = "最小严重程度等级") @RequestParam Integer minLevel,
            @Parameter(description = "最大严重程度等级") @RequestParam Integer maxLevel) {
        log.info("根据严重程度等级范围获取 Cyber Kill Chain 阶段: {} - {}", minLevel, maxLevel);
        List<CyberKillChainDetails> details = cyberKillChainService.getCyberKillChainsBySeverityLevelRange(minLevel, maxLevel);
        return ResponseEntity.ok(details);
    }

    @GetMapping("/chinese-name/{chineseName}")
    @Operation(summary = "根据中文名称获取 Cyber Kill Chain 阶段详情", description = "根据中文名称获取 Cyber Kill Chain 阶段的详细信息")
    public ResponseEntity<CyberKillChainDetails> getCyberKillChainByChineseName(
            @Parameter(description = "中文名称") @PathVariable String chineseName) {
        log.info("根据中文名称获取 Cyber Kill Chain 阶段详情: {}", chineseName);
        CyberKillChainDetails details = cyberKillChainService.getCyberKillChainByChineseName(chineseName);
        if (details != null) {
            return ResponseEntity.ok(details);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/english-name/{englishName}")
    @Operation(summary = "根据英文名称获取 Cyber Kill Chain 阶段详情", description = "根据英文名称获取 Cyber Kill Chain 阶段的详细信息")
    public ResponseEntity<CyberKillChainDetails> getCyberKillChainByEnglishName(
            @Parameter(description = "英文名称") @PathVariable String englishName) {
        log.info("根据英文名称获取 Cyber Kill Chain 阶段详情: {}", englishName);
        CyberKillChainDetails details = cyberKillChainService.getCyberKillChainByEnglishName(englishName);
        if (details != null) {
            return ResponseEntity.ok(details);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/high-risk")
    @Operation(summary = "获取高危 Cyber Kill Chain 阶段", description = "获取严重程度>=7的高危 Cyber Kill Chain 阶段")
    public ResponseEntity<List<CyberKillChainDetails>> getHighRiskCyberKillChains() {
        log.info("获取高危 Cyber Kill Chain 阶段");
        List<CyberKillChainDetails> details = cyberKillChainService.getHighRiskCyberKillChains();
        return ResponseEntity.ok(details);
    }

    @GetMapping("/medium-risk")
    @Operation(summary = "获取中危 Cyber Kill Chain 阶段", description = "获取严重程度4-6的中危 Cyber Kill Chain 阶段")
    public ResponseEntity<List<CyberKillChainDetails>> getMediumRiskCyberKillChains() {
        log.info("获取中危 Cyber Kill Chain 阶段");
        List<CyberKillChainDetails> details = cyberKillChainService.getMediumRiskCyberKillChains();
        return ResponseEntity.ok(details);
    }

    @GetMapping("/low-risk")
    @Operation(summary = "获取低危 Cyber Kill Chain 阶段", description = "获取严重程度1-3的低危 Cyber Kill Chain 阶段")
    public ResponseEntity<List<CyberKillChainDetails>> getLowRiskCyberKillChains() {
        log.info("获取低危 Cyber Kill Chain 阶段");
        List<CyberKillChainDetails> details = cyberKillChainService.getLowRiskCyberKillChains();
        return ResponseEntity.ok(details);
    }

    @GetMapping("/ordered-by-severity")
    @Operation(summary = "获取按严重程度排序的 Cyber Kill Chain 阶段", description = "获取所有 Cyber Kill Chain 阶段按严重程度排序")
    public ResponseEntity<List<CyberKillChainDetails>> getAllCyberKillChainsOrderBySeverity() {
        log.info("获取按严重程度排序的 Cyber Kill Chain 阶段");
        List<CyberKillChainDetails> details = cyberKillChainService.getAllCyberKillChainsOrderBySeverity();
        return ResponseEntity.ok(details);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取 Cyber Kill Chain 阶段统计信息", description = "获取各严重程度等级的 Cyber Kill Chain 阶段数量统计")
    public ResponseEntity<Map<String, Long>> getCyberKillChainStatistics() {
        log.info("获取 Cyber Kill Chain 阶段统计信息");
        Map<String, Long> statistics = cyberKillChainService.getCyberKillChainStatistics();
        return ResponseEntity.ok(statistics);
    }

    @GetMapping("/{cyberKillChain}/defense-recommendations")
    @Operation(summary = "获取防护建议", description = "根据 Cyber Kill Chain 阶段获取相应的防护建议")
    public ResponseEntity<List<String>> getDefenseRecommendations(
            @Parameter(description = "Cyber Kill Chain 阶段") @PathVariable CyberKillChain cyberKillChain) {
        log.info("获取 Cyber Kill Chain 阶段防护建议: {}", cyberKillChain);
        List<String> recommendations = cyberKillChainService.getDefenseRecommendations(cyberKillChain);
        return ResponseEntity.ok(recommendations);
    }

    @GetMapping("/{cyberKillChain}/typical-techniques")
    @Operation(summary = "获取典型攻击技术", description = "根据 Cyber Kill Chain 阶段获取典型的攻击技术")
    public ResponseEntity<List<String>> getTypicalTechniques(
            @Parameter(description = "Cyber Kill Chain 阶段") @PathVariable CyberKillChain cyberKillChain) {
        log.info("获取 Cyber Kill Chain 阶段典型技术: {}", cyberKillChain);
        List<String> techniques = cyberKillChainService.getTypicalTechniques(cyberKillChain);
        return ResponseEntity.ok(techniques);
    }

    @GetMapping("/{cyberKillChain}/severity-description")
    @Operation(summary = "获取严重程度描述", description = "根据 Cyber Kill Chain 阶段获取严重程度描述")
    public ResponseEntity<String> getSeverityDescription(
            @Parameter(description = "Cyber Kill Chain 阶段") @PathVariable CyberKillChain cyberKillChain) {
        log.info("获取 Cyber Kill Chain 阶段严重程度描述: {}", cyberKillChain);
        String description = cyberKillChainService.getSeverityDescription(cyberKillChain);
        return ResponseEntity.ok(description);
    }

    @GetMapping("/{cyberKillChain}/is-high-risk")
    @Operation(summary = "判断是否为高危 Cyber Kill Chain 阶段", description = "判断指定的 Cyber Kill Chain 阶段是否为高危")
    public ResponseEntity<Boolean> isHighRisk(
            @Parameter(description = "Cyber Kill Chain 阶段") @PathVariable CyberKillChain cyberKillChain) {
        log.info("判断 Cyber Kill Chain 阶段是否为高危: {}", cyberKillChain);
        boolean isHighRisk = cyberKillChainService.isHighRisk(cyberKillChain);
        return ResponseEntity.ok(isHighRisk);
    }

    @GetMapping("/{cyberKillChain}/urgency-description")
    @Operation(summary = "获取紧急程度描述", description = "根据 Cyber Kill Chain 阶段获取紧急程度描述")
    public ResponseEntity<String> getUrgencyDescription(
            @Parameter(description = "Cyber Kill Chain 阶段") @PathVariable CyberKillChain cyberKillChain) {
        log.info("获取 Cyber Kill Chain 阶段紧急程度描述: {}", cyberKillChain);
        String description = cyberKillChainService.getUrgencyDescription(cyberKillChain);
        return ResponseEntity.ok(description);
    }

    // ==================== 缓存管理接口 ====================

    @PostMapping("/cache/init")
    @Operation(summary = "初始化 Cyber Kill Chain 缓存", description = "初始化 Cyber Kill Chain 的缓存")
    public ResponseEntity<String> initCyberKillChainCache() {
        log.info("初始化 Cyber Kill Chain 缓存");
        cyberKillChainService.initCyberKillChainCache();
        return ResponseEntity.ok("Cyber Kill Chain 缓存初始化成功");
    }

    @DeleteMapping("/cache")
    @Operation(summary = "清除 Cyber Kill Chain 缓存", description = "清除 Cyber Kill Chain 的缓存")
    public ResponseEntity<String> clearCyberKillChainCache() {
        log.info("清除 Cyber Kill Chain 缓存");
        cyberKillChainService.clearCyberKillChainCache();
        return ResponseEntity.ok("Cyber Kill Chain 缓存清除成功");
    }

    // ==================== 攻击链元数据接口 ====================

    @GetMapping("/metadata/attack-chain")
    @Operation(summary = "获取攻击链模型", description = "获取攻击链模型数据，兼容 NTA 2.0 的 /dict/alarm 接口")
    public ResponseEntity<List<Map<String, Object>>> getAttackChain() {
        log.info("获取攻击链模型");
        List<Map<String, Object>> result = systemDictService.getAttackChainDict();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/metadata/alarm-types")
    @Operation(summary = "获取告警类型", description = "获取系统中所有告警类型")
    public ResponseEntity<SystemDictResponse> getAlarmTypes() {
        log.info("获取告警类型");
        SystemDictResponse result = systemDictService.getAlarmTypeDict();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/metadata/alarm-natures")
    @Operation(summary = "获取告警性质", description = "获取系统中所有告警性质")
    public ResponseEntity<SystemDictResponse> getAlarmNatures() {
        log.info("获取告警性质");
        SystemDictResponse result = systemDictService.getAlarmNatureDict();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/metadata/alarm-states")
    @Operation(summary = "获取告警状态", description = "获取系统中所有告警状态")
    public ResponseEntity<SystemDictResponse> getAlarmStates() {
        log.info("获取告警状态");
        SystemDictResponse result = systemDictService.getAlarmStateDict();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/metadata/alarm-target-types")
    @Operation(summary = "获取告警目标类型", description = "获取系统中所有告警目标类型")
    public ResponseEntity<SystemDictResponse> getAlarmTargetTypes() {
        log.info("获取告警目标类型");
        SystemDictResponse result = systemDictService.getAlarmTargetTypeDict();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/metadata/alarm-target-belongs")
    @Operation(summary = "获取告警目标归属", description = "获取系统中所有告警目标归属")
    public ResponseEntity<SystemDictResponse> getAlarmTargetBelongs() {
        log.info("获取告警目标归属");
        SystemDictResponse result = systemDictService.getAlarmTargetBelongDict();
        return ResponseEntity.ok(result);
    }
}
