package com.geeksec.metadata.service;

import com.geeksec.metadata.model.dto.Label;
import com.geeksec.metadata.model.enums.LabelCategory;
import com.geeksec.metadata.model.enums.LabelSource;
import com.geeksec.metadata.model.enums.LabelTargetType;

import java.util.List;
import java.util.Map;

/**
 * 标签元数据服务接口
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface LabelMetadataService {
    
    // ==================== 标签分类相关 ====================
    
    /**
     * 获取所有标签分类
     * 
     * @return 标签分类列表
     */
    List<LabelCategory> getAllLabelCategories();
    
    /**
     * 获取标签分类映射
     * 
     * @return 标签分类映射
     */
    Map<String, Object> getLabelCategoryMapping();
    
    /**
     * 获取所有标签分类（包括规则标签）
     * 
     * @return 所有标签分类列表
     */
    List<Label> getAllLabelCategoryList();
    
    /**
     * 获取基础标签分类列表
     * 
     * @return 基础标签分类列表
     */
    List<Label> getBaseLabelCategoryList();
    
    // ==================== 标签目标类型相关 ====================
    
    /**
     * 获取所有标签目标类型
     * 
     * @return 标签目标类型列表
     */
    List<LabelTargetType> getAllLabelTargetTypes();
    
    /**
     * 获取标签目标类型映射
     * 
     * @return 标签目标类型映射 (代码 -> 名称)
     */
    Map<Integer, String> getLabelTargetTypeMapping();
    
    /**
     * 根据代码获取标签目标类型
     * 
     * @param code 目标类型代码
     * @return 标签目标类型
     */
    LabelTargetType getLabelTargetTypeByCode(Integer code);
    
    // ==================== 标签来源相关 ====================
    
    /**
     * 获取所有标签来源
     * 
     * @return 标签来源列表
     */
    List<LabelSource> getAllLabelSources();
    
    /**
     * 根据值获取标签来源
     * 
     * @param value 来源值
     * @return 标签来源
     */
    LabelSource getLabelSourceByValue(String value);
    
    // ==================== 分析标签相关 ====================
    
    /**
     * 获取分析标签映射
     * 
     * @return 分析标签映射
     */
    Map<String, Object> getAnalysisSignMapping();
    
    /**
     * 获取证书标签映射
     * 
     * @return 证书标签映射
     */
    Map<String, Object> getCertificateSignMapping();
    
    // ==================== 标签边关系相关 ====================
    
    /**
     * 获取标签边关系映射
     * 
     * @return 标签边关系映射
     */
    Map<String, Object> getTagEdgeRelationMapping();
    
    // ==================== 标签查询相关 ====================
    
    /**
     * 根据分类获取标签列表
     * 
     * @param category 标签分类
     * @return 标签列表
     */
    List<com.geeksec.metadata.model.entity.Label> getLabelsByCategory(LabelCategory category);
    
    /**
     * 根据目标类型获取标签列表
     * 
     * @param targetType 目标类型
     * @return 标签列表
     */
    List<com.geeksec.metadata.model.entity.Label> getLabelsByTargetType(LabelTargetType targetType);
    
    /**
     * 根据来源获取标签列表
     * 
     * @param source 标签来源
     * @return 标签列表
     */
    List<com.geeksec.metadata.model.entity.Label> getLabelsBySource(LabelSource source);
    
    /**
     * 搜索标签
     * 
     * @param keyword 关键字
     * @return 标签列表
     */
    List<com.geeksec.metadata.model.entity.Label> searchLabels(String keyword);
    
    // ==================== 缓存管理 ====================
    
    /**
     * 初始化标签缓存
     */
    void initLabelCache();
    
    /**
     * 清除标签缓存
     */
    void clearLabelCache();
    
    /**
     * 刷新标签缓存
     */
    void refreshLabelCache();
}
