package com.geeksec.metadata.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.geeksec.metadata.model.entity.InternetProtocol;
import com.geeksec.metadata.model.entity.NetworkProtocol;
import com.geeksec.metadata.repository.InternetProtocolRepository;
import com.geeksec.metadata.repository.NetworkProtocolRepository;
import com.geeksec.metadata.service.ProtocolService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 协议服务实现类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProtocolServiceImpl implements ProtocolService {

    /**
     * 网络协议Repository
     */
    private final NetworkProtocolRepository networkProtocolRepository;
    

    
    /**
     * 互联网协议Repository
     */
    private final InternetProtocolRepository internetProtocolRepository;

    /**
     * 协议缓存
     */
    private final Map<String, Map<String, Object>> protocolCache = new ConcurrentHashMap<>();

    // ==================== 网络协议实现 ====================

    @Override
    @Cacheable(value = "networkProtocols", key = "'all'")
    public List<NetworkProtocol> getAllNetworkProtocols() {
        log.debug("获取所有网络协议");
        return networkProtocolRepository.selectAll();
    }

    @Override
    public NetworkProtocol getNetworkProtocolById(Integer protocolId) {
        log.debug("根据协议ID获取网络协议: {}", protocolId);
        return networkProtocolRepository.findById(protocolId);
    }

    @Override
    @Cacheable(value = "networkProtocols", key = "'name:' + #protocolName")
    public List<NetworkProtocol> getNetworkProtocolsByName(String protocolName) {
        log.debug("根据协议名称获取网络协议: {}", protocolName);
        return networkProtocolRepository.findByProtocolName(protocolName);
    }

    @Override
    @Cacheable(value = "networkProtocols", key = "'displayName:' + #displayName")
    public List<NetworkProtocol> getNetworkProtocolsByDisplayName(String displayName) {
        log.debug("根据显示名称获取网络协议: {}", displayName);
        return networkProtocolRepository.findByDisplayName(displayName);
    }

    @Override
    @Cacheable(value = "networkProtocols", key = "'category:' + #category")
    public List<NetworkProtocol> getNetworkProtocolsByCategory(String category) {
        log.debug("根据协议分类获取网络协议: {}", category);
        return networkProtocolRepository.findByCategory(category);
    }

    @Override
    @Cacheable(value = "networkProtocols", key = "'protocolType:' + #protocolType")
    public List<NetworkProtocol> getNetworkProtocolsByProtocolType(Integer protocolType) {
        log.debug("根据协议类型获取网络协议: {}", protocolType);
        return networkProtocolRepository.findByProtocolType(protocolType);
    }

    @Override
    public List<NetworkProtocol> searchNetworkProtocols(String keyword) {
        log.debug("搜索网络协议: {}", keyword);
        List<NetworkProtocol> results = new ArrayList<>();
        
        // 按协议值搜索
        results.addAll(NetworkProtocolRepository.findByProValueContaining(keyword));
        
        // 按协议名称搜索
        results.addAll(NetworkProtocolRepository.findByProNameContaining(keyword));
        
        // 去重并返回
        return results.stream()
                .collect(Collectors.toMap(
                        NetworkProtocol::getProId,
                        p -> p,
                        (existing, replacement) -> existing))
                .values()
                .stream()
                .toList();
    }

    @Override
    @Cacheable(value = "NetworkProtocols", key = "'connection'")
    public List<NetworkProtocol> getAllConnectionProtocols() {
        log.debug("获取所有连接类型协议");
        return NetworkProtocolRepository.findAllConnectionProtocols();
    }

    @Override
    @Cacheable(value = "NetworkProtocols", key = "'singlePacket'")
    public List<NetworkProtocol> getAllSinglePacketProtocols() {
        log.debug("获取所有单包类型协议");
        return NetworkProtocolRepository.findAllSinglePacketProtocols();
    }

    @Override
    @Cacheable(value = "NetworkProtocols", key = "'payload'")
    public List<NetworkProtocol> getAllPayloadProtocols() {
        log.debug("获取所有TCP/UDP负载类型协议");
        return NetworkProtocolRepository.findAllPayloadProtocols();
    }

    @Override
    @Cacheable(value = "networkProtocols", key = "'common'")
    public List<NetworkProtocol> getCommonProtocols() {
        log.debug("获取常用协议列表");
        // 获取常用的协议，可以根据使用频率或预定义列表
        List<String> commonProtocolNames = Arrays.asList(
                "HTTP", "HTTPS", "FTP", "SSH", "DNS", "SMTP", "POP3", "IMAP", "SNMP", "DHCP"
        );
        
        return commonProtocolNames.stream()
                .flatMap(name -> NetworkProtocolRepository.findByProNameContaining(name).stream())
                .distinct()
                .toList();
    }





    // ==================== 互联网协议实现 ====================

    @Override
    @Cacheable(value = "internetProtocols", key = "'all'")
    public List<InternetProtocol> getAllInternetProtocols() {
        log.debug("获取所有互联网协议");
        return internetProtocolRepository.selectAll();
    }

    @Override
    public InternetProtocol getInternetProtocolById(String proId) {
        log.debug("根据协议号获取互联网协议: {}", proId);
        try {
            Integer protocolNumber = Integer.parseInt(proId);
            return internetProtocolRepository.findByProtocolNumber(protocolNumber);
        } catch (NumberFormatException e) {
            log.warn("无效的协议号格式: {}", proId);
            return null;
        }
    }

    @Override
    public List<InternetProtocol> getInternetProtocolsByName(String protocolName) {
        log.debug("根据协议名称获取互联网协议: {}", protocolName);
        return internetProtocolRepository.findByKeyword(protocolName);
    }

    @Override
    public List<InternetProtocol> searchInternetProtocols(String keyword) {
        log.debug("搜索互联网协议: {}", keyword);
        List<InternetProtocol> results = new ArrayList<>();

        // 按关键字搜索
        results.addAll(internetProtocolRepository.findByKeywordContaining(keyword));

        // 按协议描述搜索
        results.addAll(internetProtocolRepository.findByProtocolDescriptionContaining(keyword));

        // 去重并返回
        return results.stream()
                .collect(Collectors.toMap(
                        InternetProtocol::getProtocolNumber,
                        p -> p,
                        (existing, replacement) -> existing))
                .values()
                .stream()
                .toList();
    }

    @Override
    public List<InternetProtocol> getInternetProtocolsByIds(List<String> proIds) {
        log.debug("根据协议号列表获取互联网协议: {}", proIds);
        List<Integer> protocolNumbers = proIds.stream()
                .map(id -> {
                    try {
                        return Integer.parseInt(id);
                    } catch (NumberFormatException e) {
                        log.warn("无效的协议号格式: {}", id);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .toList();

        return internetProtocolRepository.findByProtocolNumberIn(protocolNumbers);
    }

    // ==================== 通用协议接口实现 ====================

    @Override
    @Cacheable(value = "protocolTypes", key = "'all'")
    public List<String> getAllProtocolTypes() {
        log.debug("获取所有协议类型");
        Set<String> types = new HashSet<>();
        
        // 暂时返回空列表，需要根据实际需求实现
        // types.addAll(networkProtocolRepository.findAllCategories());
        // 互联网协议号没有协议类型概念，跳过
        
        return new ArrayList<>(types);
    }

    @Override
    @Cacheable(value = "protocolNames", key = "'all'")
    public List<String> getAllProtocolNames() {
        log.debug("获取所有协议名称");
        Set<String> names = new HashSet<>();
        
        names.addAll(networkProtocolRepository.findAllProtocolNames());
        
        return new ArrayList<>(names);
    }

    @Override
    public Map<String, Long> getProtocolStatistics() {
        log.debug("获取协议统计信息");
        
        Map<String, Long> statistics = new HashMap<>(8);
        statistics.put("NetworkProtocols", (long) getAllNetworkProtocols().size());
        statistics.put("InternetProtocols", (long) getAllInternetProtocols().size());
        statistics.put("totalProtocols", 
                statistics.get("NetworkProtocols") + 
                statistics.get("InternetProtocols"));
        
        return statistics;
    }

    @Override
    public Map<String, Object> searchAllProtocols(String keyword) {
        log.debug("根据关键字搜索所有类型的协议: {}", keyword);
        
        Map<String, Object> results = new HashMap<>(8);
        results.put("networkProtocols", searchNetworkProtocols(keyword));

        results.put("internetProtocols", searchInternetProtocols(keyword));
        
        return results;
    }

    @Override
    public void initProtocolCache() {
        log.info("初始化协议缓存");
        protocolCache.clear();
        
        // 预加载常用协议数据
        protocolCache.put("networkProtocols", Map.of("data", getAllNetworkProtocols()));

        protocolCache.put("internetProtocols", Map.of("data", getAllInternetProtocols()));
        
        log.info("协议缓存初始化完成");
    }

    @Override
    public void clearProtocolCache() {
        log.info("清除协议缓存");
        protocolCache.clear();
    }
}
