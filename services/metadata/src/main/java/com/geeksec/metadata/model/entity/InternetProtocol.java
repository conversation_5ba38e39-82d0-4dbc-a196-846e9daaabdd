package com.geeksec.metadata.model.entity;

import com.mybatisflex.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 互联网协议实体类
 * 对应 internet_protocols 表
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Table("internet_protocols")
public class InternetProtocol implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 协议号（0-255）
     */
    @Id(keyType = KeyType.None)
    @Column("protocol_number")
    private Integer protocolNumber;

    /**
     * 协议关键字
     */
    @Column("keyword")
    private String keyword;

    /**
     * 协议描述
     */
    @Column("protocol_description")
    private String protocolDescription;

    /**
     * 创建时间
     */
    @Column("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column("updated_at")
    private LocalDateTime updatedAt;
}
