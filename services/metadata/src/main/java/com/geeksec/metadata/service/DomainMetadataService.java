package com.geeksec.metadata.service;

import com.geeksec.metadata.model.enums.DomainType;

import java.util.List;

/**
 * 域名元数据服务接口
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface DomainMetadataService {
    
    /**
     * 获取所有域名类型
     * 
     * @return 域名类型列表
     */
    List<DomainType> getAllDomainTypes();
    
    /**
     * 根据代码获取域名类型
     * 
     * @param code 类型代码
     * @return 域名类型
     */
    DomainType getDomainTypeByCode(int code);
}
