package com.geeksec.metadata.model.enums;

import lombok.Getter;

/**
 * 标签目标类型枚举
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum LabelTargetType {
    
    /**
     * IP地址标签
     */
    IP("IP", "IP地址", "IP地址相关标签"),
    
    /**
     * 应用标签
     */
    APPLICATION("APPLICATION", "应用", "应用程序相关标签"),
    
    /**
     * 域名标签
     */
    DOMAIN("DOMAIN", "域名", "域名相关标签"),
    
    /**
     * 证书标签
     */
    CERTIFICATE("CERTIFICATE", "证书", "证书相关标签"),
    
    /**
     * 会话标签
     */
    SESSION("SESSION", "会话", "会话相关标签"),
    
    /**
     * 指纹标签
     */
    FINGERPRINT("FINGERPRINT", "指纹", "指纹相关标签"),
    
    /**
     * MAC地址标签
     */
    MAC("MAC", "MAC地址", "MAC地址相关标签"),
    
    /**
     * 端口标签
     */
    PORT("PORT", "端口", "端口相关标签");

    /**
     * 枚举值
     */
    private final String value;
    
    /**
     * 中文名称
     */
    private final String chineseName;
    
    /**
     * 描述
     */
    private final String description;

    LabelTargetType(String value, String chineseName, String description) {
        this.value = value;
        this.chineseName = chineseName;
        this.description = description;
    }

    /**
     * 根据值获取枚举
     */
    public static LabelTargetType fromValue(String value) {
        if (value == null) {
            return null;
        }
        for (LabelTargetType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据中文名称获取枚举
     */
    public static LabelTargetType fromChineseName(String chineseName) {
        if (chineseName == null) {
            return null;
        }
        for (LabelTargetType type : values()) {
            if (type.chineseName.equals(chineseName)) {
                return type;
            }
        }
        return null;
    }
}
