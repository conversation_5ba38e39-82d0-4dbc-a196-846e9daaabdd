package com.geeksec.metadata.service;

import java.util.List;

import com.geeksec.metadata.model.entity.InternetProtocol;
import com.geeksec.metadata.model.entity.NetworkProtocol;

/**
 * 协议服务接口
 * 提供各种协议的管理功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface ProtocolService {

    // ==================== 网络协议接口 ====================

    /**
     * 获取所有网络协议
     */
    List<NetworkProtocol> getAllNetworkProtocols();

    /**
     * 根据协议ID获取网络协议
     */
    NetworkProtocol getNetworkProtocolById(Integer id);

    /**
     * 根据协议名称获取网络协议
     */
    List<NetworkProtocol> getNetworkProtocolsByName(String protocolName);

    /**
     * 根据显示名称获取网络协议
     */
    List<NetworkProtocol> getNetworkProtocolsByDisplayName(String displayName);

    /**
     * 根据协议分类获取网络协议
     */
    List<NetworkProtocol> getNetworkProtocolsByCategory(String category);

    /**
     * 根据协议类型获取网络协议（1-连接, 2-单包, 3-tcp/udp负载）
     */
    List<NetworkProtocol> getNetworkProtocolsByProtocolType(Integer protocolType);

    /**
     * 搜索网络协议
     */
    List<NetworkProtocol> searchNetworkProtocols(String keyword);

    /**
     * 获取所有连接类型协议
     */
    List<NetworkProtocol> getAllConnectionProtocols();

    /**
     * 获取所有单包类型协议
     */
    List<NetworkProtocol> getAllSinglePacketProtocols();

    /**
     * 获取所有TCP/UDP负载类型协议
     */
    List<NetworkProtocol> getAllPayloadProtocols();

    /**
     * 获取常用协议
     */
    List<NetworkProtocol> getCommonProtocols();



    // ==================== 互联网协议接口 ====================

    /**
     * 获取所有互联网协议
     * @return 互联网协议列表
     */
    List<InternetProtocol> getAllInternetProtocols();

    /**
     * 根据协议号获取互联网协议
     * @param id 协议号
     * @return 互联网协议实体
     */
    InternetProtocol getInternetProtocolById(String id);

    /**
     * 根据协议名称获取互联网协议
     * @param protocolName 协议名称
     * @return 互联网协议列表
     */
    List<InternetProtocol> getInternetProtocolsByName(String protocolName);

    /**
     * 搜索互联网协议
     * @param keyword 搜索关键字
     * @return 互联网协议列表
     */
    List<InternetProtocol> searchInternetProtocols(String keyword);

    /**
     * 根据协议号列表获取互联网协议
     * @param ids 协议号列表
     * @return 互联网协议列表
     */
    List<InternetProtocol> getInternetProtocolsByIds(List<String> ids);

    // ==================== 通用协议接口 ====================
    
    /**
     * 获取所有协议类型
     */
    List<String> getAllProtocolTypes();
    
    /**
     * 获取所有协议名称
     */
    List<String> getAllProtocolNames();
    
    /**
     * 获取协议统计信息
     */
    java.util.Map<String, Long> getProtocolStatistics();
    
    /**
     * 根据关键字搜索所有类型的协议
     */
    java.util.Map<String, Object> searchAllProtocols(String keyword);

    /**
     * 初始化协议缓存
     */
    void initProtocolCache();

    /**
     * 清除协议缓存
     */
    void clearProtocolCache();


}
