package com.geeksec.metadata.model.enums;

import lombok.Getter;

/**
 * 标签来源枚举
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum LabelSource {
    
    /**
     * 系统内置标签
     */
    SYSTEM("SYSTEM", "系统内置", "系统内置的标签"),
    
    /**
     * 用户自定义标签
     */
    USER("USER", "用户自定义", "用户自定义的标签");

    /**
     * 枚举值
     */
    private final String value;
    
    /**
     * 中文名称
     */
    private final String chineseName;
    
    /**
     * 描述
     */
    private final String description;

    LabelSource(String value, String chineseName, String description) {
        this.value = value;
        this.chineseName = chineseName;
        this.description = description;
    }

    /**
     * 根据值获取枚举
     */
    public static LabelSource fromValue(String value) {
        if (value == null) {
            return null;
        }
        for (LabelSource source : values()) {
            if (source.value.equals(value)) {
                return source;
            }
        }
        return SYSTEM; // 默认为系统内置
    }

    /**
     * 根据中文名称获取枚举
     */
    public static LabelSource fromChineseName(String chineseName) {
        if (chineseName == null) {
            return null;
        }
        for (LabelSource source : values()) {
            if (source.chineseName.equals(chineseName)) {
                return source;
            }
        }
        return null;
    }
}
