package com.geeksec.metadata.service.impl;

import com.geeksec.metadata.model.enums.DomainType;
import com.geeksec.metadata.service.DomainMetadataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 域名元数据服务实现类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
public class DomainMetadataServiceImpl implements DomainMetadataService {

    @Override
    @Cacheable(value = "domainMetadata", key = "'types'")
    public List<DomainType> getAllDomainTypes() {
        log.debug("获取所有域名类型");
        return Arrays.asList(DomainType.values());
    }

    @Override
    public DomainType getDomainTypeByCode(int code) {
        log.debug("根据代码获取域名类型: {}", code);
        return DomainType.fromCode(code);
    }
}
