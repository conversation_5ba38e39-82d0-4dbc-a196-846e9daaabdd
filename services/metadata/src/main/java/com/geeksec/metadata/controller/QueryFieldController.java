package com.geeksec.metadata.controller;

import com.geeksec.metadata.service.QueryFieldMetadataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 查询字段控制器
 * 提供查询字段相关的元数据接口
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/metadata/query-fields")
@RequiredArgsConstructor
@Tag(name = "查询字段", description = "提供查询字段相关的元数据接口")
public class QueryFieldController {

    private final QueryFieldMetadataService queryFieldMetadataService;

    // ==================== ES查询字段接口 ====================

    @GetMapping("/es")
    @Operation(summary = "获取ES查询字段", description = "获取系统中所有ES查询字段")
    public ResponseEntity<Map<String, Object>> getEsQueryFields() {
        log.info("获取ES查询字段");
        Map<String, Object> result = queryFieldMetadataService.getAllEsQueryFields();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/es/{fieldName}")
    @Operation(summary = "获取ES查询字段配置", description = "根据字段名获取ES查询字段的配置信息")
    public ResponseEntity<Object> getEsQueryFieldConfig(
            @Parameter(description = "字段名", example = "ssl_subject")
            @PathVariable String fieldName) {
        log.info("获取ES查询字段配置: {}", fieldName);
        Object result = queryFieldMetadataService.getEsQueryFieldConfig(fieldName);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/es/{fieldName}/display-name")
    @Operation(summary = "获取ES查询字段显示名称", description = "根据字段名获取ES查询字段的显示名称")
    public ResponseEntity<String> getEsQueryFieldDisplayName(
            @Parameter(description = "字段名", example = "ssl_subject")
            @PathVariable String fieldName) {
        log.info("获取ES查询字段显示名称: {}", fieldName);
        String result = queryFieldMetadataService.getEsQueryFieldDisplayName(fieldName);
        return ResponseEntity.ok(result);
    }

    // ==================== 下载搜索字段接口 ====================

    @GetMapping("/download")
    @Operation(summary = "获取下载搜索字段", description = "获取系统中所有下载搜索字段")
    public ResponseEntity<Map<String, Object>> getDownloadSearchFields() {
        log.info("获取下载搜索字段");
        Map<String, Object> result = queryFieldMetadataService.getAllDownloadSearchFields();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/download/{fieldName}")
    @Operation(summary = "获取下载搜索字段配置", description = "根据字段名获取下载搜索字段的配置信息")
    public ResponseEntity<Object> getDownloadSearchFieldConfig(
            @Parameter(description = "字段名", example = "src_ip")
            @PathVariable String fieldName) {
        log.info("获取下载搜索字段配置: {}", fieldName);
        Object result = queryFieldMetadataService.getDownloadSearchFieldConfig(fieldName);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/download/{fieldName}/display-name")
    @Operation(summary = "获取下载搜索字段显示名称", description = "根据字段名获取下载搜索字段的显示名称")
    public ResponseEntity<String> getDownloadSearchFieldDisplayName(
            @Parameter(description = "字段名", example = "src_ip")
            @PathVariable String fieldName) {
        log.info("获取下载搜索字段显示名称: {}", fieldName);
        String result = queryFieldMetadataService.getDownloadSearchFieldDisplayName(fieldName);
        return ResponseEntity.ok(result);
    }

    // ==================== Nebula相关字段接口 ====================

    @GetMapping("/nebula/types")
    @Operation(summary = "获取Nebula类型", description = "获取系统中所有Nebula类型")
    public ResponseEntity<Map<String, Object>> getNebulaTypes() {
        log.info("获取Nebula类型");
        Map<String, Object> result = queryFieldMetadataService.getAllNebulaTypes();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/nebula/types/{type}")
    @Operation(summary = "获取Nebula类型名称", description = "根据类型获取Nebula类型的名称")
    public ResponseEntity<String> getNebulaTypeName(
            @Parameter(description = "类型", example = "ip")
            @PathVariable String type) {
        log.info("获取Nebula类型名称: {}", type);
        String result = queryFieldMetadataService.getNebulaTypeName(type);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/nebula/properties")
    @Operation(summary = "获取Nebula属性", description = "获取系统中所有Nebula属性")
    public ResponseEntity<Map<String, Object>> getNebulaProperties() {
        log.info("获取Nebula属性");
        Map<String, Object> result = queryFieldMetadataService.getAllNebulaProperties();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/nebula/properties/{propertyName}")
    @Operation(summary = "获取Nebula属性配置", description = "根据属性名获取Nebula属性的配置信息")
    public ResponseEntity<Object> getNebulaPropertyConfig(
            @Parameter(description = "属性名", example = "ip.addr")
            @PathVariable String propertyName) {
        log.info("获取Nebula属性配置: {}", propertyName);
        Object result = queryFieldMetadataService.getNebulaPropertyConfig(propertyName);
        return ResponseEntity.ok(result);
    }

    // ==================== 字段验证接口 ====================

    @GetMapping("/es/{fieldName}/validate")
    @Operation(summary = "验证ES查询字段", description = "验证ES查询字段是否有效")
    public ResponseEntity<Boolean> validateEsQueryField(
            @Parameter(description = "字段名", example = "ssl_subject")
            @PathVariable String fieldName) {
        log.info("验证ES查询字段: {}", fieldName);
        boolean result = queryFieldMetadataService.isValidEsQueryField(fieldName);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/download/{fieldName}/validate")
    @Operation(summary = "验证下载搜索字段", description = "验证下载搜索字段是否有效")
    public ResponseEntity<Boolean> validateDownloadSearchField(
            @Parameter(description = "字段名", example = "src_ip")
            @PathVariable String fieldName) {
        log.info("验证下载搜索字段: {}", fieldName);
        boolean result = queryFieldMetadataService.isValidDownloadSearchField(fieldName);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/nebula/types/{type}/validate")
    @Operation(summary = "验证Nebula类型", description = "验证Nebula类型是否有效")
    public ResponseEntity<Boolean> validateNebulaType(
            @Parameter(description = "类型", example = "ip")
            @PathVariable String type) {
        log.info("验证Nebula类型: {}", type);
        boolean result = queryFieldMetadataService.isValidNebulaType(type);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/nebula/properties/{propertyName}/validate")
    @Operation(summary = "验证Nebula属性", description = "验证Nebula属性是否有效")
    public ResponseEntity<Boolean> validateNebulaProperty(
            @Parameter(description = "属性名", example = "ip.addr")
            @PathVariable String propertyName) {
        log.info("验证Nebula属性: {}", propertyName);
        boolean result = queryFieldMetadataService.isValidNebulaProperty(propertyName);
        return ResponseEntity.ok(result);
    }
}
