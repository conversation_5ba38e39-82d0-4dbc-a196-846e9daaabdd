package com.geeksec.metadata.service;

import com.geeksec.metadata.model.entity.*;
import com.geeksec.metadata.model.enums.CyberKillChain;
import com.geeksec.metadata.model.enums.LabelCategory;
import com.geeksec.metadata.model.enums.LabelSource;

import java.util.List;

/**
 * 标签服务接口
 * 提供各种类型标签的管理功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface LabelService {

    // ==================== IP标签相关接口 ====================
    
    /**
     * 获取所有IP标签
     */
    List<IpLabel> getAllIpLabels();
    
    /**
     * 根据 Cyber Kill Chain 阶段获取IP标签
     *
     * @param cyberKillChain Cyber Kill Chain 阶段
     * @return IP标签列表
     */
    List<IpLabel> getIpLabelsByCyberKillChain(CyberKillChain cyberKillChain);
    
    /**
     * 根据类别获取IP标签
     */
    List<IpLabel> getIpLabelsByCategory(LabelCategory category);
    
    /**
     * 根据来源获取IP标签
     */
    List<IpLabel> getIpLabelsBySource(LabelSource source);
    
    /**
     * 根据名称搜索IP标签
     */
    List<IpLabel> searchIpLabelsByName(String name);
    
    /**
     * 根据威胁评分范围获取IP标签
     */
    List<IpLabel> getIpLabelsByThreatScoreRange(Integer minScore, Integer maxScore);



    // ==================== 应用标签相关接口 ====================
    
    /**
     * 获取所有应用标签
     */
    List<ApplicationLabel> getAllApplicationLabels();
    
    /**
     * 根据 Cyber Kill Chain 阶段获取应用标签
     *
     * @param cyberKillChain Cyber Kill Chain 阶段
     * @return 应用标签列表
     */
    List<ApplicationLabel> getApplicationLabelsByCyberKillChain(CyberKillChain cyberKillChain);
    
    /**
     * 根据类别获取应用标签
     */
    List<ApplicationLabel> getApplicationLabelsByCategory(LabelCategory category);

    // ==================== 域名标签相关接口 ====================
    
    /**
     * 获取所有域名标签
     */
    List<DomainLabel> getAllDomainLabels();
    
    /**
     * 根据 Cyber Kill Chain 阶段获取域名标签
     *
     * @param cyberKillChain Cyber Kill Chain 阶段
     * @return 域名标签列表
     */
    List<DomainLabel> getDomainLabelsByCyberKillChain(CyberKillChain cyberKillChain);
    
    /**
     * 根据类别获取域名标签
     */
    List<DomainLabel> getDomainLabelsByCategory(LabelCategory category);

    // ==================== 证书标签相关接口 ====================
    
    /**
     * 获取所有证书标签
     */
    List<CertificateLabel> getAllCertificateLabels();
    
    /**
     * 根据 Cyber Kill Chain 阶段获取证书标签
     *
     * @param cyberKillChain Cyber Kill Chain 阶段
     * @return 证书标签列表
     */
    List<CertificateLabel> getCertificateLabelsByCyberKillChain(CyberKillChain cyberKillChain);
    
    /**
     * 根据类别获取证书标签
     */
    List<CertificateLabel> getCertificateLabelsByCategory(LabelCategory category);



    // ==================== 会话标签相关接口 ====================
    
    /**
     * 获取所有会话标签
     */
    List<SessionLabel> getAllSessionLabels();
    
    /**
     * 根据 Cyber Kill Chain 阶段获取会话标签
     *
     * @param cyberKillChain Cyber Kill Chain 阶段
     * @return 会话标签列表
     */
    List<SessionLabel> getSessionLabelsByCyberKillChain(CyberKillChain cyberKillChain);

    // ==================== 指纹标签相关接口 ====================
    
    /**
     * 获取所有指纹标签
     */
    List<FingerprintLabel> getAllFingerprintLabels();
    
    /**
     * 根据 Cyber Kill Chain 阶段获取指纹标签
     *
     * @param cyberKillChain Cyber Kill Chain 阶段
     * @return 指纹标签列表
     */
    List<FingerprintLabel> getFingerprintLabelsByCyberKillChain(CyberKillChain cyberKillChain);

    // ==================== 通用标签接口 ====================
    
    /**
     * 根据标签ID和类型获取标签
     */
    BaseLabel getLabelByIdAndType(Integer labelId, String labelType);
    
    /**
     * 根据 Cyber Kill Chain 阶段获取所有类型的标签统计
     *
     * @param cyberKillChain Cyber Kill Chain 阶段
     * @return 标签统计信息
     */
    java.util.Map<String, Long> getLabelCountByCyberKillChain(CyberKillChain cyberKillChain);
    
    /**
     * 根据类别获取所有类型的标签统计
     */
    java.util.Map<String, Long> getLabelCountByCategory(LabelCategory category);
    
    /**
     * 初始化标签缓存
     */
    void initLabelCache();
    
    /**
     * 清除标签缓存
     */
    void clearLabelCache();
}
