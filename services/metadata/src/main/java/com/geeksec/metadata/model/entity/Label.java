package com.geeksec.metadata.model.entity;

import com.geeksec.metadata.model.enums.CyberKillChain;
import com.geeksec.metadata.model.enums.LabelCategory;
import com.geeksec.metadata.model.enums.LabelSource;
import com.geeksec.metadata.model.enums.LabelTargetType;
import com.mybatisflex.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 统一标签实体类
 * 对应 labels 表
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Accessors(chain = true)
@Table("labels")
public class Label implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 标签ID
     */
    @Id(keyType = KeyType.Auto)
    @Column("id")
    private Integer id;

    /**
     * 标签名称（英文标识）
     */
    @Column("name")
    private String name;

    /**
     * 显示名称（用户友好名称）
     */
    @Column("display_name")
    private String displayName;

    /**
     * 标签描述
     */
    @Column("description")
    private String description;

    /**
     * 备注信息
     */
    @Column("remark")
    private String remark;

    /**
     * 标签目标类型
     */
    @Column("target_type")
    private LabelTargetType targetType;

    /**
     * 标签类别
     */
    @Column("category")
    private LabelCategory category;

    /**
     * 标签来源
     */
    @Column("source")
    private LabelSource source;

    /**
     * 威胁等级(0-100)
     */
    @Column("threat_level")
    private Integer threatLevel;

    /**
     * 信任等级(0-100)
     */
    @Column("trust_level")
    private Integer trustLevel;

    /**
     * 默认威胁等级(0-100)
     */
    @Column("default_threat_level")
    private Integer defaultThreatLevel;

    /**
     * 默认信任等级(0-100)
     */
    @Column("default_trust_level")
    private Integer defaultTrustLevel;

    /**
     * Cyber Kill Chain 阶段
     */
    @Column("cyber_kill_chain")
    private CyberKillChain cyberKillChain;

    /**
     * 标签颜色（十六进制）
     */
    @Column("color")
    private String color;

    /**
     * 排序顺序
     */
    @Column("sort_order")
    private Integer sortOrder;

    /**
     * 是否激活
     */
    @Column("is_active")
    private Boolean isActive;

    /**
     * 版本号（乐观锁）
     */
    @Column("version")
    private Integer version;

    /**
     * 创建者用户ID
     */
    @Column("created_by")
    private Integer createdBy;

    /**
     * 创建时间
     */
    @Column("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column("updated_at")
    private LocalDateTime updatedAt;
}
