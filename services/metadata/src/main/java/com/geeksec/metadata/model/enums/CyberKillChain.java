package com.geeksec.metadata.model.enums;

import lombok.Getter;

/**
 * Cyber Kill Chain 枚举
 * 基于 Lockheed Martin Cyber Kill Chain 模型
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum CyberKillChain {
    
    /**
     * 侦察探测
     */
    RECONNAISSANCE("RECONNAISSANCE", "侦察探测", "Reconnaissance", 
            "攻击者收集目标组织的信息，包括网络架构、系统配置、人员信息等"),
    
    /**
     * 武器化
     */
    WEAPONIZATION("WEAPONIZATION", "武器化", "Weaponization", 
            "攻击者创建恶意载荷，将漏洞利用代码与恶意软件结合形成武器"),
    
    /**
     * 投递
     */
    DELIVERY("DELIVERY", "投递", "Delivery", 
            "攻击者通过各种方式将恶意载荷传输到目标环境"),
    
    /**
     * 漏洞利用
     */
    EXPLOITATION("EXPLOITATION", "漏洞利用", "Exploitation", 
            "攻击者利用系统、应用程序或人员的漏洞来执行恶意代码"),
    
    /**
     * 安装植入
     */
    INSTALLATION("INSTALLATION", "安装植入", "Installation", 
            "攻击者在受害者系统上安装恶意软件以维持持久性访问"),
    
    /**
     * 命令控制
     */
    COMMAND_AND_CONTROL("COMMAND_AND_CONTROL", "命令控制", "Command and Control", 
            "攻击者建立与受害者系统的通信渠道以进行远程控制"),
    
    /**
     * 目标行动
     */
    ACTIONS_ON_OBJECTIVES("ACTIONS_ON_OBJECTIVES", "目标行动", "Actions on Objectives", 
            "攻击者执行最终目标，如数据窃取、破坏或其他恶意活动"),
    
    /**
     * 其他
     */
    OTHER("OTHER", "其他", "Other", 
            "不属于标准杀伤链阶段的其他攻击活动"),
    
    /**
     * 未知
     */
    UNKNOWN("UNKNOWN", "未知", "Unknown", 
            "无法确定具体攻击阶段的活动");

    /**
     * 枚举值
     */
    private final String value;
    
    /**
     * 中文名称
     */
    private final String chineseName;
    
    /**
     * 英文名称
     */
    private final String englishName;
    
    /**
     * 描述
     */
    private final String description;

    CyberKillChain(String value, String chineseName, String englishName, String description) {
        this.value = value;
        this.chineseName = chineseName;
        this.englishName = englishName;
        this.description = description;
    }

    /**
     * 根据值获取枚举
     */
    public static CyberKillChain fromValue(String value) {
        if (value == null) {
            return null;
        }
        for (CyberKillChain stage : values()) {
            if (stage.value.equals(value)) {
                return stage;
            }
        }
        return UNKNOWN;
    }

    /**
     * 根据中文名称获取枚举
     */
    public static CyberKillChain fromChineseName(String chineseName) {
        if (chineseName == null) {
            return null;
        }
        for (CyberKillChain stage : values()) {
            if (stage.chineseName.equals(chineseName)) {
                return stage;
            }
        }
        return null;
    }


}
