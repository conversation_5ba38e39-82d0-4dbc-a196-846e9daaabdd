package com.geeksec.metadata.service;

/**
 * 缓存管理服务接口
 * 提供缓存的清理、刷新和管理功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface CacheManagementService {
    
    /**
     * 清理所有系统字典缓存
     */
    void clearSystemDictCache();
    
    /**
     * 清理指定类型的字典缓存
     * 
     * @param dictType 字典类型代码
     */
    void clearDictCache(String dictType);
    
    /**
     * 清理所有标签缓存
     */
    void clearAllLabelCache();
    
    /**
     * 清理指定类型的标签缓存
     * 
     * @param labelType 标签类型（ip, domain, cert, app, session, fingerprint）
     */
    void clearLabelCache(String labelType);
    
    /**
     * 清理所有协议缓存
     */
    void clearAllProtocolCache();
    
    /**
     * 清理应用协议信息缓存
     */
    void clearAppProtocolCache();
    
    /**
     * 清理标签分类缓存
     */
    void clearTagCategoryCache();
    
    /**
     * 清理攻击阶段缓存
     */
    void clearAttackStageCache();
    
    /**
     * 清理所有缓存
     */
    void clearAllCache();
    
    /**
     * 预热系统字典缓存
     */
    void warmupSystemDictCache();
    
    /**
     * 预热标签缓存
     */
    void warmupLabelCache();
    
    /**
     * 预热协议缓存
     */
    void warmupProtocolCache();
    
    /**
     * 预热所有缓存
     */
    void warmupAllCache();
    
    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息
     */
    String getCacheStatistics();
}
