package com.geeksec.metadata.controller;

import com.geeksec.common.metadata.dto.CountryInfo;
import com.geeksec.common.metadata.enums.CaptureMode;
import com.geeksec.metadata.model.enums.*;
import com.geeksec.metadata.service.DomainMetadataService;
import com.geeksec.metadata.service.SystemConfigMetadataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 系统配置控制器
 * 提供系统配置和通用元数据的查询功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/metadata")
@RequiredArgsConstructor
@Tag(name = "系统配置", description = "提供系统配置和通用元数据的查询功能")
public class SystemConfigController {

    private final DomainMetadataService domainMetadataService;
    private final SystemConfigMetadataService systemConfigMetadataService;



    // ==================== 域名相关配置 ====================

    @GetMapping("/domain/types")
    @Operation(summary = "获取域名类型", description = "获取系统中所有域名类型")
    public ResponseEntity<List<DomainType>> getDomainTypes() {
        log.info("获取域名类型");
        List<DomainType> result = domainMetadataService.getAllDomainTypes();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/domain/types/{code}")
    @Operation(summary = "根据代码获取域名类型", description = "根据代码获取特定的域名类型")
    public ResponseEntity<DomainType> getDomainTypeByCode(
            @Parameter(description = "域名类型代码", example = "1")
            @PathVariable int code) {
        log.info("根据代码获取域名类型: {}", code);
        DomainType result = domainMetadataService.getDomainTypeByCode(code);
        return ResponseEntity.ok(result);
    }



    // ==================== 系统通用配置 ====================

    @GetMapping("/system/capture-modes")
    @Operation(summary = "获取捕获模式", description = "获取系统中所有捕获模式")
    public ResponseEntity<List<CaptureMode>> getCaptureModes() {
        log.info("获取捕获模式");
        List<CaptureMode> result = systemConfigMetadataService.getAllCaptureModes();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/system/countries")
    @Operation(summary = "获取国家", description = "获取系统中所有国家信息")
    public ResponseEntity<List<CountryInfo>> getCountries() {
        log.info("获取国家信息");
        List<CountryInfo> result = systemConfigMetadataService.getAllCountries();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/system/countries/{code}")
    @Operation(summary = "根据代码获取国家", description = "根据代码获取特定的国家信息")
    public ResponseEntity<CountryInfo> getCountryByCode(
            @Parameter(description = "国家代码", example = "CN")
            @PathVariable String code) {
        log.info("根据代码获取国家信息: {}", code);
        CountryInfo result = systemConfigMetadataService.getCountryByCode(code);
        return ResponseEntity.ok(result);
    }

}
