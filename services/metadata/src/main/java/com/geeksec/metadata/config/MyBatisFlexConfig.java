package com.geeksec.metadata.config;

import com.geeksec.metadata.model.enums.CyberKillChain;
import com.geeksec.metadata.model.enums.LabelCategory;
import com.geeksec.metadata.model.enums.LabelSource;
import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.audit.AuditManager;
import com.mybatisflex.core.audit.ConsoleMessageCollector;
import com.mybatisflex.core.audit.MessageCollector;
import com.mybatisflex.core.handler.EnumTypeHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis-Flex配置类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Configuration
public class MyBatisFlexConfig {

    /**
     * 配置MyBatis-Flex全局配置
     */
    @Bean
    public FlexGlobalConfig flexGlobalConfig() {
        FlexGlobalConfig globalConfig = FlexGlobalConfig.getDefaultConfig();
        
        // 开启审计功能（开发环境）
        if (log.isDebugEnabled()) {
            AuditManager.setAuditEnable(true);
            MessageCollector collector = new ConsoleMessageCollector();
            AuditManager.setMessageCollector(collector);
        }
        
        // 配置枚举类型处理器
        configureEnumTypeHandlers(globalConfig);
        
        return globalConfig;
    }

    /**
     * 配置枚举类型处理器
     */
    private void configureEnumTypeHandlers(FlexGlobalConfig globalConfig) {
        TypeHandlerRegistry typeHandlerRegistry = globalConfig.getConfiguration().getTypeHandlerRegistry();

        // 注册 Cyber Kill Chain 枚举处理器
        typeHandlerRegistry.register(CyberKillChain.class, new EnumTypeHandler<>(CyberKillChain.class));

        // 注册标签类别枚举处理器
        typeHandlerRegistry.register(LabelCategory.class, new EnumTypeHandler<>(LabelCategory.class));

        // 注册标签来源枚举处理器
        typeHandlerRegistry.register(LabelSource.class, new EnumTypeHandler<>(LabelSource.class));

        log.info("MyBatis-Flex枚举类型处理器配置完成");
    }
}
