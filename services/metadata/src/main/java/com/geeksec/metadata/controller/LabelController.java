package com.geeksec.metadata.controller;

import com.geeksec.metadata.model.dto.Label;
import com.geeksec.metadata.model.enums.CyberKillChain;
import com.geeksec.metadata.model.enums.LabelCategory;
import com.geeksec.metadata.model.enums.LabelSource;
import com.geeksec.metadata.service.LabelMetadataService;
import com.geeksec.metadata.service.LabelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 标签管理控制器
 * 提供各种类型标签的查询接口
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/metadata/labels")
@RequiredArgsConstructor
@Tag(name = "标签管理", description = "提供各种类型标签的查询和管理功能")
public class LabelController {

    private final LabelService labelService;
    private final LabelMetadataService labelMetadataService;

    // ==================== IP标签接口 ====================

    @GetMapping("/ip")
    @Operation(summary = "获取所有IP标签", description = "获取系统中所有激活状态的IP标签")
    public ResponseEntity<List<IpLabel>> getAllIpLabels() {
        log.info("获取所有IP标签");
        List<IpLabel> labels = labelService.getAllIpLabels();
        return ResponseEntity.ok(labels);
    }

    @GetMapping("/ip/cyber-kill-chain/{cyberKillChain}")
    @Operation(summary = "根据Cyber Kill Chain阶段获取IP标签", description = "根据指定的Cyber Kill Chain阶段获取相关的IP标签")
    public ResponseEntity<List<IpLabel>> getIpLabelsByCyberKillChain(
            @Parameter(description = "Cyber Kill Chain阶段") @PathVariable CyberKillChain cyberKillChain) {
        log.info("根据Cyber Kill Chain阶段获取IP标签: {}", cyberKillChain);
        List<IpLabel> labels = labelService.getIpLabelsByCyberKillChain(cyberKillChain);
        return ResponseEntity.ok(labels);
    }

    @GetMapping("/ip/category/{category}")
    @Operation(summary = "根据类别获取IP标签", description = "根据指定的标签类别获取相关的IP标签")
    public ResponseEntity<List<IpLabel>> getIpLabelsByCategory(
            @Parameter(description = "标签类别") @PathVariable LabelCategory category) {
        log.info("根据类别获取IP标签: {}", category);
        List<IpLabel> labels = labelService.getIpLabelsByCategory(category);
        return ResponseEntity.ok(labels);
    }

    @GetMapping("/ip/source/{source}")
    @Operation(summary = "根据来源获取IP标签", description = "根据指定的标签来源获取相关的IP标签")
    public ResponseEntity<List<IpLabel>> getIpLabelsBySource(
            @Parameter(description = "标签来源") @PathVariable LabelSource source) {
        log.info("根据来源获取IP标签: {}", source);
        List<IpLabel> labels = labelService.getIpLabelsBySource(source);
        return ResponseEntity.ok(labels);
    }

    @GetMapping("/ip/search")
    @Operation(summary = "搜索IP标签", description = "根据名称关键字搜索IP标签")
    public ResponseEntity<List<IpLabel>> searchIpLabels(
            @Parameter(description = "搜索关键字") @RequestParam String name) {
        log.info("搜索IP标签: {}", name);
        List<IpLabel> labels = labelService.searchIpLabelsByName(name);
        return ResponseEntity.ok(labels);
    }

    @GetMapping("/ip/threat-score")
    @Operation(summary = "根据威胁评分范围获取IP标签", description = "根据威胁评分范围获取相关的IP标签")
    public ResponseEntity<List<IpLabel>> getIpLabelsByThreatScore(
            @Parameter(description = "最小威胁评分") @RequestParam Integer minScore,
            @Parameter(description = "最大威胁评分") @RequestParam Integer maxScore) {
        log.info("根据威胁评分范围获取IP标签: {} - {}", minScore, maxScore);
        List<IpLabel> labels = labelService.getIpLabelsByThreatScoreRange(minScore, maxScore);
        return ResponseEntity.ok(labels);
    }



    // ==================== 应用标签接口 ====================

    @GetMapping("/application")
    @Operation(summary = "获取所有应用标签", description = "获取系统中所有激活状态的应用标签")
    public ResponseEntity<List<ApplicationLabel>> getAllApplicationLabels() {
        log.info("获取所有应用标签");
        List<ApplicationLabel> labels = labelService.getAllApplicationLabels();
        return ResponseEntity.ok(labels);
    }

    @GetMapping("/application/cyber-kill-chain/{cyberKillChain}")
    @Operation(summary = "根据Cyber Kill Chain阶段获取应用标签", description = "根据指定的Cyber Kill Chain阶段获取相关的应用标签")
    public ResponseEntity<List<ApplicationLabel>> getApplicationLabelsByCyberKillChain(
            @Parameter(description = "Cyber Kill Chain阶段") @PathVariable CyberKillChain cyberKillChain) {
        log.info("根据Cyber Kill Chain阶段获取应用标签: {}", cyberKillChain);
        List<ApplicationLabel> labels = labelService.getApplicationLabelsByCyberKillChain(cyberKillChain);
        return ResponseEntity.ok(labels);
    }

    @GetMapping("/application/category/{category}")
    @Operation(summary = "根据类别获取应用标签", description = "根据指定的标签类别获取相关的应用标签")
    public ResponseEntity<List<ApplicationLabel>> getApplicationLabelsByCategory(
            @Parameter(description = "标签类别") @PathVariable LabelCategory category) {
        log.info("根据类别获取应用标签: {}", category);
        List<ApplicationLabel> labels = labelService.getApplicationLabelsByCategory(category);
        return ResponseEntity.ok(labels);
    }

    // ==================== 域名标签接口 ====================

    @GetMapping("/domain")
    @Operation(summary = "获取所有域名标签", description = "获取系统中所有激活状态的域名标签")
    public ResponseEntity<List<DomainLabel>> getAllDomainLabels() {
        log.info("获取所有域名标签");
        List<DomainLabel> labels = labelService.getAllDomainLabels();
        return ResponseEntity.ok(labels);
    }

    @GetMapping("/domain/cyber-kill-chain/{cyberKillChain}")
    @Operation(summary = "根据Cyber Kill Chain阶段获取域名标签", description = "根据指定的Cyber Kill Chain阶段获取相关的域名标签")
    public ResponseEntity<List<DomainLabel>> getDomainLabelsByCyberKillChain(
            @Parameter(description = "Cyber Kill Chain阶段") @PathVariable CyberKillChain cyberKillChain) {
        log.info("根据Cyber Kill Chain阶段获取域名标签: {}", cyberKillChain);
        List<DomainLabel> labels = labelService.getDomainLabelsByCyberKillChain(cyberKillChain);
        return ResponseEntity.ok(labels);
    }

    // ==================== 证书标签接口 ====================

    @GetMapping("/certificate")
    @Operation(summary = "获取所有证书标签", description = "获取系统中所有激活状态的证书标签")
    public ResponseEntity<List<CertificateLabel>> getAllCertificateLabels() {
        log.info("获取所有证书标签");
        List<CertificateLabel> labels = labelService.getAllCertificateLabels();
        return ResponseEntity.ok(labels);
    }



    // ==================== 会话标签接口 ====================

    @GetMapping("/session")
    @Operation(summary = "获取所有会话标签", description = "获取系统中所有激活状态的会话标签")
    public ResponseEntity<List<SessionLabel>> getAllSessionLabels() {
        log.info("获取所有会话标签");
        List<SessionLabel> labels = labelService.getAllSessionLabels();
        return ResponseEntity.ok(labels);
    }

    // ==================== 指纹标签接口 ====================

    @GetMapping("/fingerprint")
    @Operation(summary = "获取所有指纹标签", description = "获取系统中所有激活状态的指纹标签")
    public ResponseEntity<List<FingerprintLabel>> getAllFingerprintLabels() {
        log.info("获取所有指纹标签");
        List<FingerprintLabel> labels = labelService.getAllFingerprintLabels();
        return ResponseEntity.ok(labels);
    }

    // ==================== 通用标签接口 ====================

    @GetMapping("/{labelType}/{labelId}")
    @Operation(summary = "根据ID和类型获取标签", description = "根据标签ID和类型获取具体的标签信息")
    public ResponseEntity<BaseLabel> getLabelByIdAndType(
            @Parameter(description = "标签类型") @PathVariable String labelType,
            @Parameter(description = "标签ID") @PathVariable Integer labelId) {
        log.info("根据ID和类型获取标签: {} - {}", labelType, labelId);
        BaseLabel label = labelService.getLabelByIdAndType(labelId, labelType);
        if (label != null) {
            return ResponseEntity.ok(label);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/statistics/cyber-kill-chain/{cyberKillChain}")
    @Operation(summary = "根据Cyber Kill Chain阶段获取标签统计", description = "根据Cyber Kill Chain阶段获取各类型标签的数量统计")
    public ResponseEntity<Map<String, Long>> getLabelCountByCyberKillChain(
            @Parameter(description = "Cyber Kill Chain阶段") @PathVariable CyberKillChain cyberKillChain) {
        log.info("根据Cyber Kill Chain阶段获取标签统计: {}", cyberKillChain);
        Map<String, Long> statistics = labelService.getLabelCountByCyberKillChain(cyberKillChain);
        return ResponseEntity.ok(statistics);
    }

    @GetMapping("/statistics/category/{category}")
    @Operation(summary = "根据类别获取标签统计", description = "根据标签类别获取各类型标签的数量统计")
    public ResponseEntity<Map<String, Long>> getLabelCountByCategory(
            @Parameter(description = "标签类别") @PathVariable LabelCategory category) {
        log.info("根据类别获取标签统计: {}", category);
        Map<String, Long> statistics = labelService.getLabelCountByCategory(category);
        return ResponseEntity.ok(statistics);
    }

    // ==================== 缓存管理接口 ====================

    @PostMapping("/cache/init")
    @Operation(summary = "初始化标签缓存", description = "初始化所有类型标签的缓存")
    public ResponseEntity<String> initLabelCache() {
        log.info("初始化标签缓存");
        labelService.initLabelCache();
        return ResponseEntity.ok("标签缓存初始化成功");
    }

    @DeleteMapping("/cache")
    @Operation(summary = "清除标签缓存", description = "清除所有类型标签的缓存")
    public ResponseEntity<String> clearLabelCache() {
        log.info("清除标签缓存");
        labelService.clearLabelCache();
        return ResponseEntity.ok("标签缓存清除成功");
    }

    // ==================== 标签元数据接口 ====================

    @GetMapping("/metadata/categories")
    @Operation(summary = "获取标签分类", description = "获取系统中所有标签分类")
    public ResponseEntity<List<Label>> getLabelCategories() {
        log.info("获取标签分类");
        List<Label> result = labelMetadataService.getBaseLabelCategoryList();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/metadata/categories/all")
    @Operation(summary = "获取所有标签分类", description = "获取系统中所有标签分类（包括规则标签）")
    public ResponseEntity<List<Label>> getAllLabelCategories() {
        log.info("获取所有标签分类");
        List<Label> result = labelMetadataService.getAllLabelCategoryList();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/metadata/analysis-signs")
    @Operation(summary = "获取分析标签", description = "获取系统中所有分析标签")
    public ResponseEntity<Map<String, Object>> getAnalysisSigns() {
        log.info("获取分析标签");
        Map<String, Object> result = labelMetadataService.getAnalysisSignMapping();
        return ResponseEntity.ok(result);
    }
}
