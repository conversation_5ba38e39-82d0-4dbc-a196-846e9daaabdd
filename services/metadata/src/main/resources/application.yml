server:
  port: 8089
  servlet:
    context-path: /metadata

spring:
  application:
    name: metadata-service
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
    include: common
  
  # 数据源配置 - 使用环境变量
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: org.postgresql.Driver
      url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:nta_metadata}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
      username: ${DB_USERNAME:nta_user}
      password: ${DB_PASSWORD:nta_password}
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin

  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: 2
      timeout: 6000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 3600000 # 1小时
      cache-null-values: false

# MyBatis-Flex配置
mybatis-flex:
  type-aliases-package: com.geeksec.metadata.model.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# Knife4j配置
knife4j:
  enable: true
  production: false
  basic:
    enable: false
  setting:
    language: zh-CN

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    com.geeksec.metadata: DEBUG
    org.springframework.cache: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/metadata-service.log

# 服务配置
services:
  session-service:
    url: http://session-service:8088
  task-service:
    url: http://task-service:8087

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    druid:
      url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:nta_dev}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
  datasource:
    druid:
      url: jdbc:postgresql://${DB_HOST:test-db}:${DB_PORT:5432}/${DB_NAME:nta_test}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
  data:
    redis:
      host: ${REDIS_HOST:test-redis}
      port: ${REDIS_PORT:6379}

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    druid:
      url: jdbc:postgresql://${DB_HOST:prod-db}:${DB_PORT:5432}/${DB_NAME:nta_prod}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
  data:
    redis:
      host: ${REDIS_HOST:prod-redis}
      port: ${REDIS_PORT:6379}
logging:
  level:
    com.geeksec.metadata: INFO
    org.springframework.cache: WARN
