package com.geeksec.metadata.controller;

import com.geeksec.metadata.service.QueryFieldMetadataService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Map;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 查询字段控制器测试类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@WebMvcTest(QueryFieldController.class)
@DisplayName("查询字段控制器测试")
class QueryFieldControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private QueryFieldMetadataService queryFieldMetadataService;

    private Map<String, Object> mockEsQueryFields;
    private Map<String, Object> mockDownloadSearchFields;
    private Map<String, Object> mockNebulaTypes;
    private Map<String, Object> mockNebulaProperties;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockEsQueryFields = Map.of(
            "ssl_subject", "SSL证书主题",
            "http_host", "HTTP主机",
            "dns_query", "DNS查询"
        );

        mockDownloadSearchFields = Map.of(
            "timestamp", "时间戳",
            "src_ip", "源IP",
            "dst_ip", "目标IP"
        );

        mockNebulaTypes = Map.of(
            "ip", "IP地址",
            "domain", "域名",
            "cert", "证书"
        );

        mockNebulaProperties = Map.of(
            "ip.addr", "IP地址",
            "domain.name", "域名",
            "cert.subject", "证书主题"
        );
    }

    // ==================== ES查询字段接口测试 ====================

    @Test
    @DisplayName("获取ES查询字段")
    void testGetEsQueryFields() throws Exception {
        // Given
        when(queryFieldMetadataService.getAllEsQueryFields()).thenReturn(mockEsQueryFields);

        // When & Then
        mockMvc.perform(get("/api/v1/metadata/query-fields/es")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.ssl_subject").value("SSL证书主题"))
                .andExpect(jsonPath("$.http_host").value("HTTP主机"))
                .andExpect(jsonPath("$.dns_query").value("DNS查询"));
    }

    @Test
    @DisplayName("获取ES查询字段配置")
    void testGetEsQueryFieldConfig() throws Exception {
        // Given
        when(queryFieldMetadataService.getEsQueryFieldConfig("ssl_subject")).thenReturn("SSL证书主题");

        // When & Then
        mockMvc.perform(get("/api/v1/metadata/query-fields/es/ssl_subject")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().string("\"SSL证书主题\""));
    }

    @Test
    @DisplayName("获取ES查询字段显示名称")
    void testGetEsQueryFieldDisplayName() throws Exception {
        // Given
        when(queryFieldMetadataService.getEsQueryFieldDisplayName("ssl_subject")).thenReturn("SSL证书主题");

        // When & Then
        mockMvc.perform(get("/api/v1/metadata/query-fields/es/ssl_subject/display-name")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().string("\"SSL证书主题\""));
    }

    // ==================== 下载搜索字段接口测试 ====================

    @Test
    @DisplayName("获取下载搜索字段")
    void testGetDownloadSearchFields() throws Exception {
        // Given
        when(queryFieldMetadataService.getAllDownloadSearchFields()).thenReturn(mockDownloadSearchFields);

        // When & Then
        mockMvc.perform(get("/api/v1/metadata/query-fields/download")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.timestamp").value("时间戳"))
                .andExpect(jsonPath("$.src_ip").value("源IP"))
                .andExpect(jsonPath("$.dst_ip").value("目标IP"));
    }

    @Test
    @DisplayName("获取下载搜索字段配置")
    void testGetDownloadSearchFieldConfig() throws Exception {
        // Given
        when(queryFieldMetadataService.getDownloadSearchFieldConfig("src_ip")).thenReturn("源IP");

        // When & Then
        mockMvc.perform(get("/api/v1/metadata/query-fields/download/src_ip")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().string("\"源IP\""));
    }

    @Test
    @DisplayName("获取下载搜索字段显示名称")
    void testGetDownloadSearchFieldDisplayName() throws Exception {
        // Given
        when(queryFieldMetadataService.getDownloadSearchFieldDisplayName("src_ip")).thenReturn("源IP");

        // When & Then
        mockMvc.perform(get("/api/v1/metadata/query-fields/download/src_ip/display-name")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().string("\"源IP\""));
    }

    // ==================== Nebula相关字段接口测试 ====================

    @Test
    @DisplayName("获取Nebula类型")
    void testGetNebulaTypes() throws Exception {
        // Given
        when(queryFieldMetadataService.getAllNebulaTypes()).thenReturn(mockNebulaTypes);

        // When & Then
        mockMvc.perform(get("/api/v1/metadata/query-fields/nebula/types")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.ip").value("IP地址"))
                .andExpect(jsonPath("$.domain").value("域名"))
                .andExpect(jsonPath("$.cert").value("证书"));
    }

    @Test
    @DisplayName("获取Nebula类型名称")
    void testGetNebulaTypeName() throws Exception {
        // Given
        when(queryFieldMetadataService.getNebulaTypeName("ip")).thenReturn("IP地址");

        // When & Then
        mockMvc.perform(get("/api/v1/metadata/query-fields/nebula/types/ip")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().string("\"IP地址\""));
    }

    @Test
    @DisplayName("获取Nebula属性")
    void testGetNebulaProperties() throws Exception {
        // Given
        when(queryFieldMetadataService.getAllNebulaProperties()).thenReturn(mockNebulaProperties);

        // When & Then
        mockMvc.perform(get("/api/v1/metadata/query-fields/nebula/properties")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$['ip.addr']").value("IP地址"))
                .andExpect(jsonPath("$['domain.name']").value("域名"))
                .andExpect(jsonPath("$['cert.subject']").value("证书主题"));
    }

    @Test
    @DisplayName("获取Nebula属性配置")
    void testGetNebulaPropertyConfig() throws Exception {
        // Given
        when(queryFieldMetadataService.getNebulaPropertyConfig("ip.addr")).thenReturn("IP地址");

        // When & Then
        mockMvc.perform(get("/api/v1/metadata/query-fields/nebula/properties/ip.addr")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().string("\"IP地址\""));
    }

    // ==================== 字段验证接口测试 ====================

    @Test
    @DisplayName("验证ES查询字段")
    void testValidateEsQueryField() throws Exception {
        // Given
        when(queryFieldMetadataService.isValidEsQueryField("ssl_subject")).thenReturn(true);

        // When & Then
        mockMvc.perform(get("/api/v1/metadata/query-fields/es/ssl_subject/validate")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().string("true"));
    }

    @Test
    @DisplayName("验证下载搜索字段")
    void testValidateDownloadSearchField() throws Exception {
        // Given
        when(queryFieldMetadataService.isValidDownloadSearchField("src_ip")).thenReturn(true);

        // When & Then
        mockMvc.perform(get("/api/v1/metadata/query-fields/download/src_ip/validate")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().string("true"));
    }

    @Test
    @DisplayName("验证Nebula类型")
    void testValidateNebulaType() throws Exception {
        // Given
        when(queryFieldMetadataService.isValidNebulaType("ip")).thenReturn(true);

        // When & Then
        mockMvc.perform(get("/api/v1/metadata/query-fields/nebula/types/ip/validate")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().string("true"));
    }

    @Test
    @DisplayName("验证Nebula属性")
    void testValidateNebulaProperty() throws Exception {
        // Given
        when(queryFieldMetadataService.isValidNebulaProperty("ip.addr")).thenReturn(true);

        // When & Then
        mockMvc.perform(get("/api/v1/metadata/query-fields/nebula/properties/ip.addr/validate")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().string("true"));
    }
}
