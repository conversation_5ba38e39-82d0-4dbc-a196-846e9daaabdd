# NTA 3.0 Services Bootstrap 配置模板
# 各个服务可以复制此模板并根据需要进行定制

spring:
  application:
    name: ${SERVICE_NAME:nta-service}
  
  profiles:
    # 激活Kubernetes配置
    include: kubernetes
  
  cloud:
    # 启用Kubernetes服务发现
    kubernetes:
      discovery:
        enabled: true
        # 服务名称
        service-name: ${spring.application.name}
        # 命名空间
        namespace: ${KUBERNETES_NAMESPACE:nta}
      config:
        enabled: true
        # 从ConfigMap加载配置
        sources:
          - name: ${spring.application.name}-config
            namespace: ${KUBERNETES_NAMESPACE:nta}
          - name: common-config
            namespace: ${KUBERNETES_NAMESPACE:nta}
    
    # 配置导入
    config:
      import:
        - "kubernetes:"
        - "classpath:application-kubernetes.yml"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    root: INFO
    "[com.geeksec]": DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
