# NTA 3.0 Services 生产环境配置
# 继承通用配置，并针对生产环境进行优化

spring:
  profiles:
    active: prod
  
  # 生产环境数据源配置
  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:prod-db.nta.local}:${DB_PORT:5432}/${DB_NAME:nta_prod}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai&useSSL=true&sslmode=require
    username: ${DB_USERNAME:nta_prod}
    password: ${DB_PASSWORD}
    
    # 生产环境 Druid 连接池配置（大规模连接池）
    druid:
      initial-size: 10
      min-idle: 10
      max-active: 50
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      
      # 生产环境监控配置（受限访问）
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: false  # 生产环境关闭Web监控界面
        url-pattern: /druid/*
        reset-enable: false

  # 生产环境 Redis 配置
  data:
    redis:
      host: ${REDIS_HOST:prod-redis.nta.local}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD}
      database: ${REDIS_DATABASE:2}
      timeout: 5000ms
      ssl: ${REDIS_SSL:true}
      lettuce:
        pool:
          max-active: 20
          max-idle: 20
          min-idle: 5
          max-wait: -1ms

  # 生产环境 Kafka 配置
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:prod-kafka-1.nta.local:9092,prod-kafka-2.nta.local:9092,prod-kafka-3.nta.local:9092}
    producer:
      retries: 5
      batch-size: 32768
      buffer-memory: 67108864
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: all
      compression-type: lz4
    consumer:
      bootstrap-servers: ${KAFKA_SERVERS:prod-kafka-1.nta.local:9092,prod-kafka-2.nta.local:9092,prod-kafka-3.nta.local:9092}
      group-id: ${spring.application.name}-prod
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      enable-auto-commit: true
      auto-commit-interval: 1000
      max-poll-records: 500

# MyBatis-Flex 生产环境配置
mybatis-flex:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
  global-config:
    logic-delete-column: deleted
    version-column: version
    key-generator-type: snowflake
    # 生产环境关闭 SQL 打印
    print-sql: false

# Knife4j 生产环境配置
knife4j:
  # 生产环境关闭API文档
  enable: false
  openapi:
    title: ${spring.application.name} API 文档 (生产环境)
    description: NTA 3.0 网络流量分析平台 - ${spring.application.name} 服务接口文档 (生产环境)
    version: 3.0.0
    concat: NTA 开发团队
    email: <EMAIL>
    url: https://www.geeksec.com
    license: Apache 2.0
    license-url: https://www.apache.org/licenses/LICENSE-2.0.html
  setting:
    language: zh_cn
    enable-swagger-models: false
    enable-document-manage: false

# Sa-Token 生产环境配置
sa-token:
  token-name: Authorization
  # 生产环境较长的token有效期
  timeout: 28800  # 8小时
  active-timeout: -1
  is-concurrent: true
  is-share: true
  token-style: uuid
  # 生产环境关闭日志
  is-log: false

# 服务器生产环境配置
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: ${CONTEXT_PATH:}
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024
  # 生产环境启用HTTPS
  ssl:
    enabled: ${SSL_ENABLED:false}
    key-store: ${SSL_KEY_STORE:}
    key-store-password: ${SSL_KEY_STORE_PASSWORD:}
    key-store-type: ${SSL_KEY_STORE_TYPE:PKCS12}

# 生产环境日志配置
logging:
  level:
    com.geeksec: INFO
    com.mybatisflex: WARN
    cn.dev33.satoken: WARN
    org.springframework.web: WARN
    org.springframework.security: WARN
    org.apache.kafka: WARN
    org.springframework.jdbc: WARN
    # 生产环境只记录错误日志
    root: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: ${LOG_FILE_PATH:/var/log/nta}/${spring.application.name}-prod.log
    max-size: 500MB
    max-history: 90
  # 生产环境日志轮转配置
  logback:
    rollingpolicy:
      max-file-size: 500MB
      max-history: 90
      total-size-cap: 10GB

# 生产环境监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: never
  metrics:
    export:
      prometheus:
        enabled: true
  security:
    enabled: true

# 生产环境特定配置
prod:
  # 生产环境数据路径
  data:
    base-path: ${PROD_DATA_PATH:/data/nta-prod}
    temp-path: ${PROD_TEMP_PATH:/tmp/nta-prod}
    backup-path: ${PROD_BACKUP_PATH:/backup/nta-prod}
  # 生产环境外部服务地址
  external:
    services:
      import-service: ${IMPORT_SERVICE_URL:http://prod-import.nta.local:5000}
      analysis-service: ${ANALYSIS_SERVICE_URL:http://prod-analysis.nta.local:6000}
  # 生产环境安全配置
  security:
    # 启用安全模式
    secure-mode: true
    # 启用访问控制
    access-control: true
    # 启用审计日志
    audit-log: true
  # 生产环境性能配置
  performance:
    # 启用缓存
    cache-enabled: true
    # 启用连接池监控
    connection-pool-monitor: true
    # 启用慢查询监控
    slow-query-monitor: true
    slow-query-threshold: 5000  # 5秒
