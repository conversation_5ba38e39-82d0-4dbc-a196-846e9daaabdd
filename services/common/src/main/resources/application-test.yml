# NTA 3.0 Services 测试环境配置
# 继承通用配置，并针对测试环境进行优化

spring:
  profiles:
    active: test
  
  # 测试环境数据源配置
  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:test-db.nta.local}:${DB_PORT:5432}/${DB_NAME:nta_test}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:nta_test}
    password: ${DB_PASSWORD:nta_test123}
    
    # 测试环境 Druid 连接池配置（中等规模连接池）
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 15
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=3000
      
      # 测试环境启用监控
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: ${DRUID_USERNAME:admin}
        login-password: ${DRUID_PASSWORD:test123}

  # 测试环境 Redis 配置
  data:
    redis:
      host: ${REDIS_HOST:test-redis.nta.local}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:test123}
      database: ${REDIS_DATABASE:1}
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 10
          max-idle: 10
          min-idle: 2
          max-wait: -1ms

  # 测试环境 Kafka 配置
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:test-kafka.nta.local:9092}
    producer:
      retries: 3
      batch-size: 16384
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: all
    consumer:
      bootstrap-servers: ${KAFKA_SERVERS:test-kafka.nta.local:9092}
      group-id: ${spring.application.name}-test
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      enable-auto-commit: true
      auto-commit-interval: 1000

# MyBatis-Flex 测试环境配置
mybatis-flex:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
  global-config:
    logic-delete-column: deleted
    version-column: version
    key-generator-type: snowflake
    # 测试环境适度打印 SQL
    print-sql: ${DEBUG_SQL:false}

# Knife4j 测试环境配置
knife4j:
  enable: true
  openapi:
    title: ${spring.application.name} API 文档 (测试环境)
    description: NTA 3.0 网络流量分析平台 - ${spring.application.name} 服务接口文档 (测试环境)
    version: 3.0.0-TEST
    concat: NTA 开发团队
    email: <EMAIL>
    url: https://test.geeksec.com
    license: Apache 2.0
    license-url: https://www.apache.org/licenses/LICENSE-2.0.html
  setting:
    language: zh_cn
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 实体类列表
    enable-version: true

# Sa-Token 测试环境配置
sa-token:
  token-name: Authorization
  # 测试环境中等token有效期
  timeout: 7200
  active-timeout: -1
  is-concurrent: true
  is-share: true
  token-style: uuid
  # 测试环境启用日志
  is-log: true

# 服务器测试环境配置
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: ${CONTEXT_PATH:}
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

# 测试环境日志配置
logging:
  level:
    com.geeksec: INFO
    com.mybatisflex: INFO
    cn.dev33.satoken: INFO
    org.springframework.web: INFO
    org.springframework.security: INFO
    org.apache.kafka: WARN
    # 测试环境关闭SQL详细日志
    org.springframework.jdbc: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: ${LOG_FILE_PATH:/var/log/nta}/${spring.application.name}-test.log
    max-size: 200MB
    max-history: 60

# 测试环境监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when_authorized
  metrics:
    export:
      prometheus:
        enabled: true

# 测试环境特定配置
test:
  # 测试环境数据路径
  data:
    base-path: ${TEST_DATA_PATH:/data/nta-test}
    temp-path: ${TEST_TEMP_PATH:/tmp/nta-test}
  # 测试环境外部服务地址
  external:
    services:
      import-service: ${IMPORT_SERVICE_URL:http://test-import.nta.local:5000}
      analysis-service: ${ANALYSIS_SERVICE_URL:http://test-analysis.nta.local:6000}
  # 测试环境调试选项
  debug:
    enable-sql-log: false
    enable-request-log: true
    enable-performance-monitor: true
  # 测试环境特定配置
  testing:
    # 启用测试数据自动清理
    auto-cleanup: true
    # 测试数据保留时间（小时）
    data-retention-hours: 24
    # 启用测试模式
    test-mode: true
