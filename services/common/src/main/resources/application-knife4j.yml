# Knife4j API 文档框架统一配置模板
# 各服务可以引入此配置文件，并根据需要进行覆盖

# Knife4j 配置
knife4j:
  # 启用 Knife4j
  enable: true
  # 生产环境设置
  production: false
  # 基础认证配置（可选）
  basic:
    enable: false
    username: admin
    password: admin123
  # 增强配置
  setting:
    # 界面语言
    language: zh_cn
    # 启用版本控制
    enable-version: true
    # 启用 Swagger Models
    enable-swagger-models: true
    # 启用文档管理
    enable-document-manage: true
    # Swagger Model 名称
    swagger-model-name: 实体类列表
    # 启用主机显示
    enable-host: false
    # 主机文本
    enable-host-text: ${KNIFE4J_HOST:localhost:8080}
    # 启用请求缓存
    enable-request-cache: true
    # 启用过滤 multipart 接口
    enable-filter-multipart-apis: false
    # 过滤 multipart 接口的方法类型
    enable-filter-multipart-api-method-type: POST
    # 启用后置脚本
    enable-after-script: true
    # 启用搜索
    enable-search: true
    # 启用 Footer
    enable-footer: true
    # 启用 Footer 自定义
    enable-footer-custom: true
    # Footer 自定义内容
    footer-custom-content: "Copyright © 2024 GeekSec. All rights reserved."
    # 启用动态参数
    enable-dynamic-parameter: true
    # 启用调试
    enable-debug: true
    # 启用 OpenAPI 规范
    enable-openapi: true
    # 启用重载缓存参数
    enable-reload-cache-parameter: false

# SpringDoc OpenAPI 配置（Knife4j 基于此配置）
springdoc:
  # API 文档路径
  api-docs:
    path: /v3/api-docs
    enabled: true
  # Swagger UI 配置
  swagger-ui:
    # 访问路径（Knife4j 会重定向到 /doc.html）
    path: /swagger-ui.html
    # 标签排序
    tags-sorter: alpha
    # 操作排序
    operations-sorter: alpha
    # 显示扩展
    display-extensions: true
    # 显示操作 ID
    display-operation-id: false
    # 默认模型渲染
    default-models-expand-depth: 1
    # 默认模型展开深度
    default-model-expand-depth: 1
    # 显示请求持续时间
    display-request-duration: true
    # 文档展开设置
    doc-expansion: none
    # 过滤器
    filter: false
    # 最大显示标签数
    max-displayed-tags: 50
    # 显示通用扩展
    show-extensions: false
    # 显示通用扩展
    show-common-extensions: false
  # 路径匹配
  paths-to-match: /**
  # 包扫描
  packages-to-scan: com.geeksec
  # 缓存禁用
  cache:
    disabled: true
