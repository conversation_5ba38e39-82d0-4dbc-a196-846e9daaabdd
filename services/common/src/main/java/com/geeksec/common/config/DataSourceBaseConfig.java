package com.geeksec.common.config;

import com.alibaba.druid.pool.DruidDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * 数据源基础配置类
 * 
 * 提供通用的数据源配置，各服务模块可以继承此类来创建自己的数据源配置
 * 统一使用Druid连接池，提供标准的连接池配置
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public abstract class DataSourceBaseConfig {

    /**
     * 创建标准的Druid数据源
     * 
     * @param prefix 配置前缀
     * @param dataSourceName 数据源名称（用于日志）
     * @return 配置好的数据源
     */
    protected DataSource createDruidDataSource(String prefix, String dataSourceName) {
        log.info("初始化{}数据源，配置前缀: {}", dataSourceName, prefix);
        
        DruidDataSource dataSource = new DruidDataSource();
        
        // 基础连接池配置
        dataSource.setInitialSize(5);
        dataSource.setMinIdle(5);
        dataSource.setMaxActive(20);
        dataSource.setMaxWait(60000);
        
        // 连接检测配置
        dataSource.setTimeBetweenEvictionRunsMillis(60000);
        dataSource.setMinEvictableIdleTimeMillis(300000);
        dataSource.setValidationQuery("SELECT 1");
        dataSource.setTestWhileIdle(true);
        dataSource.setTestOnBorrow(false);
        dataSource.setTestOnReturn(false);
        
        // 预编译语句配置
        dataSource.setPoolPreparedStatements(true);
        dataSource.setMaxPoolPreparedStatementPerConnectionSize(20);
        
        // 监控和过滤器配置
        try {
            dataSource.setFilters("stat,wall,slf4j");
        } catch (Exception e) {
            log.warn("设置Druid过滤器失败", e);
        }
        
        // 连接属性配置
        dataSource.setConnectionProperties("druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000");
        
        return dataSource;
    }

    /**
     * 创建PostgreSQL数据源的标准配置
     * 
     * @param url 数据库URL
     * @param username 用户名
     * @param password 密码
     * @param dataSourceName 数据源名称
     * @return 配置好的PostgreSQL数据源
     */
    protected DataSource createPostgreSQLDataSource(String url, String username, String password, String dataSourceName) {
        log.info("初始化PostgreSQL数据源: {}", dataSourceName);
        
        DruidDataSource dataSource = (DruidDataSource) createDruidDataSource("", dataSourceName);
        dataSource.setDriverClassName("org.postgresql.Driver");
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        
        // PostgreSQL特定配置
        dataSource.setValidationQuery("SELECT 1");
        
        return dataSource;
    }

    /**
     * 创建MySQL数据源的标准配置
     * 
     * @param url 数据库URL
     * @param username 用户名
     * @param password 密码
     * @param dataSourceName 数据源名称
     * @return 配置好的MySQL数据源
     */
    protected DataSource createMySQLDataSource(String url, String username, String password, String dataSourceName) {
        log.info("初始化MySQL数据源: {}", dataSourceName);
        
        DruidDataSource dataSource = (DruidDataSource) createDruidDataSource("", dataSourceName);
        dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        
        // MySQL特定配置
        dataSource.setValidationQuery("SELECT 1");
        
        return dataSource;
    }

    /**
     * 创建Doris数据源的标准配置
     * 
     * @param url 数据库URL
     * @param username 用户名
     * @param password 密码
     * @param dataSourceName 数据源名称
     * @return 配置好的Doris数据源
     */
    protected DataSource createDorisDataSource(String url, String username, String password, String dataSourceName) {
        log.info("初始化Doris数据源: {}", dataSourceName);
        
        DruidDataSource dataSource = (DruidDataSource) createDruidDataSource("", dataSourceName);
        dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        
        // Doris特定配置
        dataSource.setValidationQuery("SELECT 1");
        // Doris连接池优化配置
        dataSource.setInitialSize(2);
        dataSource.setMinIdle(2);
        dataSource.setMaxActive(10);
        
        return dataSource;
    }

    /**
     * 获取数据源健康检查信息
     * 
     * @param dataSource 数据源
     * @return 健康检查信息
     */
    protected String getDataSourceHealthInfo(DataSource dataSource) {
        if (dataSource instanceof DruidDataSource) {
            DruidDataSource druidDataSource = (DruidDataSource) dataSource;
            return String.format("活跃连接: %d, 空闲连接: %d, 最大连接: %d", 
                druidDataSource.getActiveCount(),
                druidDataSource.getPoolingCount(),
                druidDataSource.getMaxActive());
        }
        return "数据源健康状态: 正常";
    }

    /**
     * 验证数据源配置
     * 
     * @param dataSource 数据源
     * @param dataSourceName 数据源名称
     */
    protected void validateDataSource(DataSource dataSource, String dataSourceName) {
        try {
            dataSource.getConnection().close();
            log.info("数据源 {} 连接测试成功", dataSourceName);
        } catch (Exception e) {
            log.error("数据源 {} 连接测试失败", dataSourceName, e);
            throw new RuntimeException("数据源配置错误: " + dataSourceName, e);
        }
    }
}
