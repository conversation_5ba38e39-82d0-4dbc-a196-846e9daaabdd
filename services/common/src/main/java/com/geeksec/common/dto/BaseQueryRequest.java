package com.geeksec.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 查询请求基类
 * 
 * 各服务的查询请求可以继承此类，包含常用的查询条件
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "查询请求基类")
public class BaseQueryRequest extends BasePageRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 关键词搜索
     */
    @Schema(description = "关键词搜索", example = "搜索关键词")
    private String keyword;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "1")
    private Integer status;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始", example = "2023-01-01T00:00:00")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束", example = "2023-12-31T23:59:59")
    private LocalDateTime createTimeEnd;

    /**
     * 更新时间开始
     */
    @Schema(description = "更新时间开始", example = "2023-01-01T00:00:00")
    private LocalDateTime updateTimeStart;

    /**
     * 更新时间结束
     */
    @Schema(description = "更新时间结束", example = "2023-12-31T23:59:59")
    private LocalDateTime updateTimeEnd;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createBy;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID", example = "1")
    private Long updateBy;

    /**
     * 是否有关键词搜索
     * 
     * @return 是否有关键词
     */
    public boolean hasKeyword() {
        return keyword != null && !keyword.trim().isEmpty();
    }

    /**
     * 是否有状态过滤
     * 
     * @return 是否有状态
     */
    public boolean hasStatus() {
        return status != null;
    }

    /**
     * 是否有创建时间范围
     * 
     * @return 是否有创建时间范围
     */
    public boolean hasCreateTimeRange() {
        return createTimeStart != null || createTimeEnd != null;
    }

    /**
     * 是否有更新时间范围
     * 
     * @return 是否有更新时间范围
     */
    public boolean hasUpdateTimeRange() {
        return updateTimeStart != null || updateTimeEnd != null;
    }

    /**
     * 获取处理后的关键词（去除首尾空格，转小写）
     * 
     * @return 处理后的关键词
     */
    public String getProcessedKeyword() {
        return hasKeyword() ? keyword.trim().toLowerCase() : null;
    }

    /**
     * 获取模糊搜索的关键词（添加%通配符）
     * 
     * @return 模糊搜索关键词
     */
    public String getLikeKeyword() {
        return hasKeyword() ? "%" + keyword.trim() + "%" : null;
    }
}
