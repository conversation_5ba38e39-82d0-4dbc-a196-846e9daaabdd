package com.geeksec.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务类型枚举
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Getter
@AllArgsConstructor
public enum TaskTypeEnum {

    /**
     * 在线任务
     */
    ONLINE(1, "在线任务"),

    /**
     * 离线任务
     */
    OFFLINE(2, "离线任务"),

    /**
     * 数据准备任务
     */
    DATA_PREPARE(3, "数据准备任务"),

    /**
     * 下载任务
     */
    DOWNLOAD(4, "下载任务"),

    /**
     * 分析任务
     */
    ANALYSIS(5, "分析任务");

    /**
     * 类型码
     */
    private final Integer code;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据类型码获取枚举
     * 
     * @param code 类型码
     * @return 任务类型枚举
     */
    public static TaskTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (TaskTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为在线任务
     * 
     * @param code 类型码
     * @return 是否为在线任务
     */
    public static boolean isOnline(Integer code) {
        return ONLINE.getCode().equals(code);
    }

    /**
     * 判断是否为离线任务
     * 
     * @param code 类型码
     * @return 是否为离线任务
     */
    public static boolean isOffline(Integer code) {
        return OFFLINE.getCode().equals(code);
    }
}
