package com.geeksec.common.config;

import com.geeksec.common.interceptor.RequestLogInterceptor;
import com.geeksec.common.interceptor.PerformanceInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC 基础配置类
 *
 * 提供统一的Web配置，各服务可以继承此类并添加特定配置
 * 替代各服务中重复的WebMvcConfig类
 *
 * <AUTHOR>
 */
@Configuration
public class WebMvcBaseConfig implements WebMvcConfigurer {

    protected final RequestLogInterceptor requestLogInterceptor;
    protected final PerformanceInterceptor performanceInterceptor;

    public WebMvcBaseConfig(RequestLogInterceptor requestLogInterceptor,
                           PerformanceInterceptor performanceInterceptor) {
        this.requestLogInterceptor = requestLogInterceptor;
        this.performanceInterceptor = performanceInterceptor;
    }

    /**
     * 默认构造函数，用于不需要拦截器的场景
     */
    public WebMvcBaseConfig() {
        this.requestLogInterceptor = null;
        this.performanceInterceptor = null;
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/**")
            .allowedOrigins("*")
            .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH")
            .allowedHeaders("*")
            .exposedHeaders("Authorization", "Link", "X-Total-Count")
            .allowCredentials(false)
            .maxAge(3600);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // Swagger UI 资源
        registry.addResourceHandler("swagger-ui.html")
            .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**")
            .addResourceLocations("classpath:/META-INF/resources/webjars/");

        // Knife4j 资源
        registry.addResourceHandler("doc.html")
            .addResourceLocations("classpath:/META-INF/resources/");

        // 静态资源
        registry.addResourceHandler("/static/**")
            .addResourceLocations("classpath:/static/");
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 性能监控拦截器
        if (performanceInterceptor != null) {
            registry.addInterceptor(performanceInterceptor)
                .addPathPatterns("/api/**")
                .excludePathPatterns("/api/*/health", "/api/*/actuator/**");
        }

        // 请求日志拦截器
        if (requestLogInterceptor != null) {
            registry.addInterceptor(requestLogInterceptor)
                .addPathPatterns("/api/**")
                .excludePathPatterns("/api/*/health", "/api/*/actuator/**");
        }
    }
}
