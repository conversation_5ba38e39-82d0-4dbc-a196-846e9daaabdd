package com.geeksec.common.service.impl;

import com.geeksec.common.entity.UserDisplayPreference;
import com.geeksec.common.repository.UserDisplayPreferenceRepository;
import com.geeksec.common.service.UserDisplayPreferenceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户显示偏好配置服务实现类
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDisplayPreferenceServiceImpl implements UserDisplayPreferenceService {

    private final UserDisplayPreferenceRepository preferenceRepository;

    @Override
    public Map<String, Object> getUserDisplayConfig(Integer userId, String module, String configType) {
        log.info("获取用户显示配置, userId: {}, module: {}, configType: {}", userId, module, configType);
        
        Map<String, Object> result = new HashMap<>(8);
        
        try {
            UserDisplayPreference preference = preferenceRepository.selectByUserIdAndModuleAndType(userId, module, configType);
            
            if (preference != null) {
                result.put("success", true);
                result.put("data", preference);
            } else {
                // 返回默认配置
                String defaultConfig = getDefaultConfig(module, configType);
                UserDisplayPreference defaultPreference = createDefaultPreference(userId, module, configType, defaultConfig);
                result.put("success", true);
                result.put("data", defaultPreference);
            }
            
            return result;
        } catch (Exception e) {
            log.error("获取用户显示配置失败, userId: {}, module: {}, configType: {}, error: ", userId, module, configType, e);
            result.put("success", false);
            result.put("message", "获取配置失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    @Transactional
    public boolean saveUserDisplayConfig(Integer userId, String module, String configType, String configJson, Integer operatorId) {
        log.info("保存用户显示配置, userId: {}, module: {}, configType: {}", userId, module, configType);
        
        try {
            // 检查是否已存在配置
            if (preferenceRepository.existsByUserIdAndModuleAndType(userId, module, configType)) {
                // 如果存在，则更新
                return updateUserDisplayConfig(userId, module, configType, configJson, operatorId);
            }
            
            UserDisplayPreference preference = new UserDisplayPreference();
            preference.setUserId(userId);
            preference.setModule(module);
            preference.setConfigType(configType);
            preference.setConfigJson(configJson);
            preference.setCreatedAt(LocalDateTime.now());
            preference.setUpdatedAt(LocalDateTime.now());
            preference.setCreatedBy(operatorId);
            preference.setUpdatedBy(operatorId);
            
            int inserted = preferenceRepository.insert(preference);
            return inserted > 0;
        } catch (Exception e) {
            log.error("保存用户显示配置失败, userId: {}, module: {}, configType: {}, error: ", userId, module, configType, e);
            throw new RuntimeException("保存用户显示配置失败", e);
        }
    }

    @Override
    @Transactional
    public boolean updateUserDisplayConfig(Integer userId, String module, String configType, String configJson, Integer operatorId) {
        log.info("更新用户显示配置, userId: {}, module: {}, configType: {}", userId, module, configType);
        
        try {
            UserDisplayPreference existing = preferenceRepository.selectByUserIdAndModuleAndType(userId, module, configType);
            if (existing == null) {
                // 如果不存在，则创建
                return saveUserDisplayConfig(userId, module, configType, configJson, operatorId);
            }
            
            int updated = preferenceRepository.updateConfigJson(existing.getId(), configJson, operatorId);
            return updated > 0;
        } catch (Exception e) {
            log.error("更新用户显示配置失败, userId: {}, module: {}, configType: {}, error: ", userId, module, configType, e);
            throw new RuntimeException("更新用户显示配置失败", e);
        }
    }

    @Override
    @Transactional
    public boolean deleteUserDisplayConfig(Integer userId, String module, String configType) {
        log.info("删除用户显示配置, userId: {}, module: {}, configType: {}", userId, module, configType);
        
        try {
            int deleted = preferenceRepository.deleteByUserIdAndModuleAndType(userId, module, configType);
            return deleted > 0;
        } catch (Exception e) {
            log.error("删除用户显示配置失败, userId: {}, module: {}, configType: {}, error: ", userId, module, configType, e);
            throw new RuntimeException("删除用户显示配置失败", e);
        }
    }

    @Override
    public List<UserDisplayPreference> getUserAllDisplayConfigs(Integer userId) {
        log.info("获取用户所有显示配置, userId: {}", userId);
        
        try {
            return preferenceRepository.selectByUserId(userId);
        } catch (Exception e) {
            log.error("获取用户所有显示配置失败, userId: {}, error: ", userId, e);
            throw new RuntimeException("获取用户所有显示配置失败", e);
        }
    }

    @Override
    public List<UserDisplayPreference> getUserModuleDisplayConfigs(Integer userId, String module) {
        log.info("获取用户模块显示配置, userId: {}, module: {}", userId, module);
        
        try {
            return preferenceRepository.selectByUserIdAndModule(userId, module);
        } catch (Exception e) {
            log.error("获取用户模块显示配置失败, userId: {}, module: {}, error: ", userId, module, e);
            throw new RuntimeException("获取用户模块显示配置失败", e);
        }
    }

    @Override
    public boolean hasUserDisplayConfig(Integer userId, String module, String configType) {
        log.info("检查用户显示配置, userId: {}, module: {}, configType: {}", userId, module, configType);

        try {
            return preferenceRepository.existsByUserIdAndModuleAndType(userId, module, configType);
        } catch (Exception e) {
            log.error("检查用户显示配置失败, userId: {}, module: {}, configType: {}, error: ", userId, module, configType, e);
            return false;
        }
    }

    @Override
    public String getDefaultConfig(String module, String configType) {
        log.info("获取默认配置, module: {}, configType: {}", module, configType);

        // 根据模块和配置类型返回默认配置
        if (UserDisplayPreference.Module.CERTIFICATE.equals(module) &&
            UserDisplayPreference.ConfigType.TABLE_VIEW.equals(configType)) {
            return getCertificateTableDefaultConfig();
        }

        // 其他模块的默认配置可以在这里扩展
        return getGenericDefaultConfig();
    }

    @Override
    @Transactional
    public boolean copyDisplayConfig(Integer sourceUserId, Integer targetUserId, String module, String configType, Integer operatorId) {
        log.info("复制显示配置, sourceUserId: {}, targetUserId: {}, module: {}, configType: {}",
                sourceUserId, targetUserId, module, configType);

        try {
            UserDisplayPreference sourceConfig = preferenceRepository.selectByUserIdAndModuleAndType(sourceUserId, module, configType);
            if (sourceConfig == null) {
                log.warn("源配置不存在, sourceUserId: {}, module: {}, configType: {}", sourceUserId, module, configType);
                return false;
            }

            return saveUserDisplayConfig(targetUserId, module, configType, sourceConfig.getConfigJson(), operatorId);
        } catch (Exception e) {
            log.error("复制显示配置失败, sourceUserId: {}, targetUserId: {}, module: {}, configType: {}, error: ",
                    sourceUserId, targetUserId, module, configType, e);
            throw new RuntimeException("复制显示配置失败", e);
        }
    }

    @Override
    @Transactional
    public boolean batchDeleteUserDisplayConfigs(Integer userId, String module) {
        log.info("批量删除用户显示配置, userId: {}, module: {}", userId, module);

        try {
            int deleted;
            if (module != null) {
                deleted = preferenceRepository.deleteByUserIdAndModule(userId, module);
            } else {
                deleted = preferenceRepository.deleteByQuery(
                    com.mybatisflex.core.query.QueryWrapper.create()
                        .where(com.geeksec.common.entity.table.UserDisplayPreferenceTableDef.USER_DISPLAY_PREFERENCE.USER_ID.eq(userId))
                );
            }
            return deleted > 0;
        } catch (Exception e) {
            log.error("批量删除用户显示配置失败, userId: {}, module: {}, error: ", userId, module, e);
            throw new RuntimeException("批量删除用户显示配置失败", e);
        }
    }

    @Override
    public Map<String, Object> getModuleConfigStatistics(String module) {
        log.info("获取模块配置统计, module: {}", module);

        Map<String, Object> statistics = new HashMap<>(8);

        try {
            Long totalConfigs = preferenceRepository.countByModule(module);
            List<UserDisplayPreference> configs = preferenceRepository.selectByModule(module);

            statistics.put("totalConfigs", totalConfigs);
            statistics.put("totalUsers", configs.stream().map(UserDisplayPreference::getUserId).distinct().count());
            statistics.put("configTypes", configs.stream().map(UserDisplayPreference::getConfigType).distinct().count());
            statistics.put("module", module);

            return statistics;
        } catch (Exception e) {
            log.error("获取模块配置统计失败, module: {}, error: ", module, e);
            throw new RuntimeException("获取模块配置统计失败", e);
        }
    }

    /**
     * 创建默认偏好配置对象
     */
    private UserDisplayPreference createDefaultPreference(Integer userId, String module, String configType, String configJson) {
        UserDisplayPreference preference = new UserDisplayPreference();
        preference.setUserId(userId);
        preference.setModule(module);
        preference.setConfigType(configType);
        preference.setConfigJson(configJson);
        preference.setCreatedAt(LocalDateTime.now());
        preference.setUpdatedAt(LocalDateTime.now());
        return preference;
    }

    /**
     * 获取证书表格默认配置
     */
    private String getCertificateTableDefaultConfig() {
        return """
            {
                "columns": [
                    {"field": "cert_id", "title": "证书ID", "width": 200, "visible": true},
                    {"field": "first_seen", "title": "首次出现", "width": 150, "visible": true},
                    {"field": "last_seen", "title": "末次出现", "width": 150, "visible": true},
                    {"field": "threat_level", "title": "威胁等级", "width": 100, "visible": true},
                    {"field": "trust_level", "title": "信任等级", "width": 100, "visible": true},
                    {"field": "issuer_o", "title": "签发机构", "width": 200, "visible": true},
                    {"field": "subject_o", "title": "所有者", "width": 200, "visible": true},
                    {"field": "labels", "title": "标签", "width": 150, "visible": true},
                    {"field": "remark", "title": "备注", "width": 200, "visible": true}
                ],
                "pageSize": 20,
                "sortField": "last_seen",
                "sortOrder": "desc"
            }
            """;
    }

    /**
     * 获取通用默认配置
     */
    private String getGenericDefaultConfig() {
        return """
            {
                "columns": [],
                "pageSize": 20,
                "sortField": "created_at",
                "sortOrder": "desc"
            }
            """;
    }
}
