package com.geeksec.common.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 基础业务实体类
 * 
 * 提取所有业务实体的共同特性：
 * - 备注信息
 * - 标签管理
 * - 时间戳
 * - 黑白名单标识
 * 
 * 这个类包含纯业务逻辑，不包含持久化相关注解
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@SuperBuilder
@EqualsAndHashCode
@Schema(description = "基础业务实体类")
public abstract class BaseBusinessEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    protected String remark;

    /**
     * 标签ID集合
     */
    @lombok.Builder.Default
    @Schema(description = "标签ID集合")
    protected Set<Long> labels = new HashSet<>(16);

    /**
     * 首次发现时间
     */
    @Schema(description = "首次发现时间")
    protected LocalDateTime firstSeen;

    /**
     * 最后发现时间
     */
    @Schema(description = "最后发现时间")
    protected LocalDateTime lastSeen;

    /**
     * 黑名单标识
     */
    @Schema(description = "黑名单标识", example = "false")
    protected Boolean blackList = false;

    /**
     * 白名单标识
     */
    @Schema(description = "白名单标识", example = "false")
    protected Boolean whiteList = false;

    /**
     * 默认构造函数
     */
    protected BaseBusinessEntity() {
        // 默认构造函数
    }

    /**
     * 添加标签
     *
     * @param labelId 标签ID
     */
    public void addLabel(Long labelId) {
        if (labelId == null) {
            return;
        }
        if (labels == null) {
            labels = new HashSet<>();
        }
        labels.add(labelId);
    }

    /**
     * 移除标签
     *
     * @param labelId 标签ID
     */
    public void removeLabel(Long labelId) {
        if (labelId == null || labels == null) {
            return;
        }
        labels.remove(labelId);
    }

    /**
     * 设置标签集合
     *
     * @param labelIds 标签ID集合
     */
    public void setLabels(Set<Long> labelIds) {
        if (labelIds == null || labelIds.isEmpty()) {
            this.labels = new HashSet<>();
            return;
        }
        this.labels = new HashSet<>(labelIds);
    }

    /**
     * 检查是否包含指定标签
     *
     * @param labelId 标签ID
     * @return 是否包含
     */
    public boolean hasLabel(Long labelId) {
        return labelId != null && labels != null && labels.contains(labelId);
    }

    /**
     * 更新最后发现时间为当前时间
     */
    public void updateLastSeen() {
        this.lastSeen = LocalDateTime.now();
    }

    /**
     * 设置首次发现时间（如果尚未设置）
     */
    public void setFirstSeenIfNull() {
        if (this.firstSeen == null) {
            this.firstSeen = LocalDateTime.now();
        }
    }

    /**
     * 标记为黑名单
     */
    public void markAsBlacklist() {
        this.blackList = true;
        this.whiteList = false;
    }

    /**
     * 标记为白名单
     */
    public void markAsWhitelist() {
        this.whiteList = true;
        this.blackList = false;
    }

    /**
     * 清除黑白名单标识
     */
    public void clearListStatus() {
        this.blackList = false;
        this.whiteList = false;
    }

    /**
     * 检查是否在黑名单中
     *
     * @return 是否在黑名单中
     */
    public boolean isBlacklisted() {
        return Boolean.TRUE.equals(blackList);
    }

    /**
     * 检查是否在白名单中
     *
     * @return 是否在白名单中
     */
    public boolean isWhitelisted() {
        return Boolean.TRUE.equals(whiteList);
    }
}
