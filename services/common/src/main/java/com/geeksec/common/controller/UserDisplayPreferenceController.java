package com.geeksec.common.controller;

import com.geeksec.common.entity.UserDisplayPreference;
import com.geeksec.common.service.UserDisplayPreferenceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户显示偏好配置控制器
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/user/display/preferences")
@RequiredArgsConstructor
@Validated
@Tag(name = "用户显示偏好", description = "用户显示偏好配置管理接口")
public class UserDisplayPreferenceController {

    private final UserDisplayPreferenceService displayPreferenceService;

    /**
     * 获取用户显示配置
     */
    @GetMapping
    @Operation(summary = "获取用户显示配置", description = "获取用户在指定模块的显示配置")
    public ResponseEntity<Map<String, Object>> getUserDisplayConfig(
            @Parameter(description = "用户ID", required = true)
            @RequestParam("user_id") @NotNull Integer userId,
            @Parameter(description = "模块名称", required = true)
            @RequestParam("module") @NotBlank String module,
            @Parameter(description = "配置类型", required = true)
            @RequestParam("config_type") @NotBlank String configType) {
        log.info("获取用户显示配置, userId: {}, module: {}, configType: {}", userId, module, configType);
        
        try {
            Map<String, Object> result = displayPreferenceService.getUserDisplayConfig(userId, module, configType);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取用户显示配置失败, userId: {}, module: {}, configType: {}, error: ", userId, module, configType, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 保存用户显示配置
     */
    @PostMapping
    @Operation(summary = "保存用户显示配置", description = "保存用户在指定模块的显示配置")
    public ResponseEntity<Map<String, Object>> saveUserDisplayConfig(
            @Parameter(description = "用户ID", required = true)
            @RequestParam("user_id") @NotNull Integer userId,
            @Parameter(description = "模块名称", required = true)
            @RequestParam("module") @NotBlank String module,
            @Parameter(description = "配置类型", required = true)
            @RequestParam("config_type") @NotBlank String configType,
            @Parameter(description = "操作者用户ID", required = false)
            @RequestParam(value = "operator_id", required = false) Integer operatorId,
            @Parameter(description = "配置JSON", required = true)
            @RequestBody @NotBlank String configJson) {
        log.info("保存用户显示配置, userId: {}, module: {}, configType: {}", userId, module, configType);
        
        Map<String, Object> result = new HashMap<>(4);
        
        try {
            boolean success = displayPreferenceService.saveUserDisplayConfig(
                userId, module, configType, configJson, operatorId != null ? operatorId : userId);
            
            result.put("success", success);
            result.put("message", success ? "配置保存成功" : "配置保存失败");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("保存用户显示配置失败, userId: {}, module: {}, configType: {}, error: ", userId, module, configType, e);
            result.put("success", false);
            result.put("message", "配置保存失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 更新用户显示配置
     */
    @PutMapping
    @Operation(summary = "更新用户显示配置", description = "更新用户在指定模块的显示配置")
    public ResponseEntity<Map<String, Object>> updateUserDisplayConfig(
            @Parameter(description = "用户ID", required = true)
            @RequestParam("user_id") @NotNull Integer userId,
            @Parameter(description = "模块名称", required = true)
            @RequestParam("module") @NotBlank String module,
            @Parameter(description = "配置类型", required = true)
            @RequestParam("config_type") @NotBlank String configType,
            @Parameter(description = "操作者用户ID", required = false)
            @RequestParam(value = "operator_id", required = false) Integer operatorId,
            @Parameter(description = "配置JSON", required = true)
            @RequestBody @NotBlank String configJson) {
        log.info("更新用户显示配置, userId: {}, module: {}, configType: {}", userId, module, configType);
        
        Map<String, Object> result = new HashMap<>(4);
        
        try {
            boolean success = displayPreferenceService.updateUserDisplayConfig(
                userId, module, configType, configJson, operatorId != null ? operatorId : userId);
            
            result.put("success", success);
            result.put("message", success ? "配置更新成功" : "配置更新失败");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新用户显示配置失败, userId: {}, module: {}, configType: {}, error: ", userId, module, configType, e);
            result.put("success", false);
            result.put("message", "配置更新失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 删除用户显示配置
     */
    @DeleteMapping
    @Operation(summary = "删除用户显示配置", description = "删除用户在指定模块的显示配置")
    public ResponseEntity<Map<String, Object>> deleteUserDisplayConfig(
            @Parameter(description = "用户ID", required = true)
            @RequestParam("user_id") @NotNull Integer userId,
            @Parameter(description = "模块名称", required = true)
            @RequestParam("module") @NotBlank String module,
            @Parameter(description = "配置类型", required = true)
            @RequestParam("config_type") @NotBlank String configType) {
        log.info("删除用户显示配置, userId: {}, module: {}, configType: {}", userId, module, configType);
        
        Map<String, Object> result = new HashMap<>(4);
        
        try {
            boolean success = displayPreferenceService.deleteUserDisplayConfig(userId, module, configType);
            
            result.put("success", success);
            result.put("message", success ? "配置删除成功" : "配置删除失败");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("删除用户显示配置失败, userId: {}, module: {}, configType: {}, error: ", userId, module, configType, e);
            result.put("success", false);
            result.put("message", "配置删除失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取用户所有显示配置
     */
    @GetMapping("/all")
    @Operation(summary = "获取用户所有显示配置", description = "获取用户的所有显示配置")
    public ResponseEntity<List<UserDisplayPreference>> getUserAllDisplayConfigs(
            @Parameter(description = "用户ID", required = true)
            @RequestParam("user_id") @NotNull Integer userId) {
        log.info("获取用户所有显示配置, userId: {}", userId);
        
        try {
            List<UserDisplayPreference> configs = displayPreferenceService.getUserAllDisplayConfigs(userId);
            return ResponseEntity.ok(configs);
        } catch (Exception e) {
            log.error("获取用户所有显示配置失败, userId: {}, error: ", userId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取用户模块显示配置
     */
    @GetMapping("/module")
    @Operation(summary = "获取用户模块显示配置", description = "获取用户在指定模块的所有显示配置")
    public ResponseEntity<List<UserDisplayPreference>> getUserModuleDisplayConfigs(
            @Parameter(description = "用户ID", required = true)
            @RequestParam("user_id") @NotNull Integer userId,
            @Parameter(description = "模块名称", required = true)
            @RequestParam("module") @NotBlank String module) {
        log.info("获取用户模块显示配置, userId: {}, module: {}", userId, module);
        
        try {
            List<UserDisplayPreference> configs = displayPreferenceService.getUserModuleDisplayConfigs(userId, module);
            return ResponseEntity.ok(configs);
        } catch (Exception e) {
            log.error("获取用户模块显示配置失败, userId: {}, module: {}, error: ", userId, module, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 复制显示配置
     */
    @PostMapping("/copy")
    @Operation(summary = "复制显示配置", description = "从源用户复制显示配置到目标用户")
    public ResponseEntity<Map<String, Object>> copyDisplayConfig(
            @Parameter(description = "源用户ID", required = true)
            @RequestParam("source_user_id") @NotNull Integer sourceUserId,
            @Parameter(description = "目标用户ID", required = true)
            @RequestParam("target_user_id") @NotNull Integer targetUserId,
            @Parameter(description = "模块名称", required = true)
            @RequestParam("module") @NotBlank String module,
            @Parameter(description = "配置类型", required = true)
            @RequestParam("config_type") @NotBlank String configType,
            @Parameter(description = "操作者用户ID", required = false)
            @RequestParam(value = "operator_id", required = false) Integer operatorId) {
        log.info("复制显示配置, sourceUserId: {}, targetUserId: {}, module: {}, configType: {}", 
                sourceUserId, targetUserId, module, configType);
        
        Map<String, Object> result = new HashMap<>(4);
        
        try {
            boolean success = displayPreferenceService.copyDisplayConfig(
                sourceUserId, targetUserId, module, configType, operatorId != null ? operatorId : targetUserId);
            
            result.put("success", success);
            result.put("message", success ? "配置复制成功" : "配置复制失败");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("复制显示配置失败, sourceUserId: {}, targetUserId: {}, module: {}, configType: {}, error: ", 
                    sourceUserId, targetUserId, module, configType, e);
            result.put("success", false);
            result.put("message", "配置复制失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取模块配置统计
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取模块配置统计", description = "获取指定模块的配置统计信息")
    public ResponseEntity<Map<String, Object>> getModuleConfigStatistics(
            @Parameter(description = "模块名称", required = true)
            @RequestParam("module") @NotBlank String module) {
        log.info("获取模块配置统计, module: {}", module);
        
        try {
            Map<String, Object> statistics = displayPreferenceService.getModuleConfigStatistics(module);
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("获取模块配置统计失败, module: {}, error: ", module, e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
