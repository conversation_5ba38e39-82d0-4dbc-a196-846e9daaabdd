package com.geeksec.common.mapper;

import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;

/**
 * 通用 Mapper 基类
 * 
 * 各服务的 Mapper 接口可以继承此类，获得基础的 CRUD 操作
 * 基于 MyBatis-Flex 的 BaseMapper
 * 
 * @param <T> 实体类型
 * <AUTHOR>
 * @since 3.0.0
 */
public interface CommonBaseMapper<T> extends BaseMapper<T> {
    
    // 这里可以添加通用的自定义方法
    // 目前直接继承 MyBatis-Flex 的 BaseMapper，已经提供了丰富的 CRUD 操作
    
    /**
     * 根据实体中的非空字段查询
     *
     * @param entity 实体对象
     * @return 查询结果数量
     */
    default long selectCountByEntity(T entity) {
        // 这个方法需要根据具体的实体类型来实现
        // 暂时返回总数量，子类可以重写此方法
        return selectCountByQuery(QueryWrapper.create());
    }
    
    /**
     * 根据实体中的非空字段查询是否存在
     * 
     * @param entity 实体对象
     * @return 是否存在
     */
    default boolean existsByEntity(T entity) {
        return selectCountByEntity(entity) > 0;
    }
}
