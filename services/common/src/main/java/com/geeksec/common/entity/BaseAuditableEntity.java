package com.geeksec.common.entity;

import com.mybatisflex.annotation.Column;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 可审计实体基类
 *
 * 提供标准的审计字段：
 * - 创建者和创建时间
 * - 修改者和修改时间
 * - 逻辑删除标识
 * - 版本号（乐观锁）
 *
 * <AUTHOR>
 */
@Data
@SuperBuilder
@EqualsAndHashCode
@Schema(description = "可审计实体基类")
public abstract class BaseAuditableEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 创建者
     */
    @Column(onInsertValue = "'system'")
    @Schema(description = "创建者", example = "system")
    private String createdBy;

    /**
     * 创建时间
     */
    @Column(value = "created_at", onInsertValue = "now()")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    /**
     * 最后修改者
     */
    @Column(value = "updated_by", onUpdateValue = "'system'")
    @Schema(description = "最后修改者", example = "system")
    private String lastModifiedBy;

    /**
     * 最后修改时间
     */
    @Column(value = "updated_at", onUpdateValue = "now()")
    @Schema(description = "最后修改时间")
    private LocalDateTime updatedAt;

    /**
     * 逻辑删除标识（0-未删除，1-已删除）
     */
    @Column("deleted")
    @Schema(description = "逻辑删除标识", example = "0")
    private Integer deleted = 0;

    /**
     * 版本号（乐观锁）
     */
    @Column("version")
    @Schema(description = "版本号", example = "1")
    private Long version = 1L;

    /**
     * 默认构造函数
     */
    protected BaseAuditableEntity() {
        // 默认构造函数
    }
}
