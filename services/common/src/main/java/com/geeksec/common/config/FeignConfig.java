package com.geeksec.common.config;

import java.util.concurrent.TimeUnit;

import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import feign.Logger;
import feign.Request;
import feign.Retryer;

/**
 * OpenFeign 配置类
 * 
 * 配置OpenFeign客户端的超时、重试、熔断器等功能
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Configuration
@EnableFeignClients(basePackages = "com.geeksec")
public class FeignConfig {

    /**
     * 配置Feign日志级别
     * 
     * @return Logger.Level
     */
    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.BASIC;
    }

    /**
     * 配置Feign请求选项
     * 
     * @return Request.Options
     */
    @Bean
    public Request.Options requestOptions() {
        return new Request.Options(
            5000,  // 连接超时时间（毫秒）
            TimeUnit.MILLISECONDS,
            10000, // 读取超时时间（毫秒）
            TimeUnit.MILLISECONDS,
            true   // 跟随重定向
        );
    }

    /**
     * 配置Feign重试器
     * 
     * @return Retryer
     */
    @Bean
    public Retryer feignRetryer() {
        // 重试间隔100ms，最大间隔1000ms，最大重试次数3次
        return new Retryer.Default(100, 1000, 3);
    }

    /**
     * 配置熔断器
     * 
     * @return Customizer<Resilience4JCircuitBreakerFactory>
     */
    /*
    @Bean
    public Customizer<Resilience4JCircuitBreakerFactory> defaultCustomizer() {
        return factory -> factory.configureDefault(id -> new Resilience4JConfigBuilder(id)
            .timeLimiterConfig(TimeLimiterConfig.custom()
                .timeoutDuration(Duration.ofSeconds(10))
                .build())
            .circuitBreakerConfig(CircuitBreakerConfig.custom()
                // 失败率阈值50%
                .failureRateThreshold(50)
                // 慢调用率阈值60%
                .slowCallRateThreshold(60)
                // 慢调用持续时间阈值5秒
                .slowCallDurationThreshold(Duration.ofSeconds(5))
                // 滑动窗口大小10
                .slidingWindowSize(10)
                // 最小调用次数5
                .minimumNumberOfCalls(5)
                // 半开状态允许的调用次数3
                .permittedNumberOfCallsInHalfOpenState(3)
                // 等待时间30秒
                .waitDurationInOpenState(Duration.ofSeconds(30))
                .build())
            .build());
    }
    */
}
