package com.geeksec.common.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.time.Duration;
import java.time.Instant;

/**
 * 性能监控拦截器
 *
 * 监控API接口的性能指标，包括：
 * - 请求响应时间
 * - 慢查询检测
 * - 性能告警
 *
 * 替代各服务中重复的性能监控拦截器
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Component
public class PerformanceInterceptor implements HandlerInterceptor {

    /** 慢请求阈值（毫秒） */
    private static final long SLOW_REQUEST_THRESHOLD = 3000L;

    /** 超慢请求阈值（毫秒） */
    private static final long VERY_SLOW_REQUEST_THRESHOLD = 10000L;

    private static final String START_TIME_ATTRIBUTE = "performanceStartTime";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 记录请求开始时间
        request.setAttribute(START_TIME_ATTRIBUTE, Instant.now());
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
        // 不做处理
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response,
                               Object handler, Exception ex) {
        // 计算请求耗时
        Instant startTime = (Instant) request.getAttribute(START_TIME_ATTRIBUTE);
        if (startTime != null) {
            Duration duration = Duration.between(startTime, Instant.now());
            long durationMs = duration.toMillis();

            String method = request.getMethod();
            String uri = request.getRequestURI();
            int status = response.getStatus();

            // 性能监控和告警
            if (durationMs >= VERY_SLOW_REQUEST_THRESHOLD) {
                // 超慢请求告警
                log.warn("⚠️ 超慢请求告警 - {} {} | 状态: {} | 耗时: {}ms | 阈值: {}ms",
                        method, uri, status, durationMs, VERY_SLOW_REQUEST_THRESHOLD);
            } else if (durationMs >= SLOW_REQUEST_THRESHOLD) {
                // 慢请求告警
                log.warn("🐌 慢请求告警 - {} {} | 状态: {} | 耗时: {}ms | 阈值: {}ms",
                        method, uri, status, durationMs, SLOW_REQUEST_THRESHOLD);
            } else {
                // 正常请求
                log.debug("✅ 性能正常 - {} {} | 状态: {} | 耗时: {}ms",
                         method, uri, status, durationMs);
            }

            // 记录性能指标（可以后续集成到监控系统）
            recordPerformanceMetrics(method, uri, status, durationMs, ex != null);
        }
    }

    /**
     * 记录性能指标
     *
     * @param method HTTP方法
     * @param uri 请求URI
     * @param status 响应状态码
     * @param duration 请求耗时
     * @param hasException 是否有异常
     */
    private void recordPerformanceMetrics(String method, String uri, int status,
                                        long duration, boolean hasException) {
        // TODO: 集成到监控系统（如Prometheus、Micrometer等）
        // 这里可以记录到时序数据库或监控系统

        // 示例：记录关键性能指标
        if (log.isDebugEnabled()) {
            log.debug("性能指标 - 方法: {}, URI: {}, 状态: {}, 耗时: {}ms, 异常: {}",
                     method, uri, status, duration, hasException);
        }

        // 可以在这里添加：
        // 1. 发送到Prometheus
        // 2. 记录到InfluxDB
        // 3. 发送到APM系统
        // 4. 触发告警通知
    }

    /**
     * 判断是否为慢请求
     */
    public static boolean isSlowRequest(long duration) {
        return duration >= SLOW_REQUEST_THRESHOLD;
    }

    /**
     * 判断是否为超慢请求
     */
    public static boolean isVerySlowRequest(long duration) {
        return duration >= VERY_SLOW_REQUEST_THRESHOLD;
    }

    /**
     * 获取慢请求阈值
     */
    public static long getSlowRequestThreshold() {
        return SLOW_REQUEST_THRESHOLD;
    }

    /**
     * 获取超慢请求阈值
     */
    public static long getVerySlowRequestThreshold() {
        return VERY_SLOW_REQUEST_THRESHOLD;
    }
}
