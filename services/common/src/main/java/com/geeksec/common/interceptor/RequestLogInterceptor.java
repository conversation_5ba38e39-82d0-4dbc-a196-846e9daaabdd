package com.geeksec.common.interceptor;

import com.geeksec.common.util.NtaUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.time.Duration;
import java.time.Instant;

/**
 * 请求日志拦截器
 *
 * 记录所有API请求的基本信息，包括：
 * - 请求URL和方法
 * - 请求参数
 * - 客户端IP
 * - 用户代理
 * - 请求时间
 *
 * 替代各服务中重复的请求日志拦截器
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Component
public class RequestLogInterceptor implements HandlerInterceptor {

    private static final String UNKNOWN_IP = "unknown";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 记录请求开始时间
        request.setAttribute("startTime", Instant.now());

        // 获取请求信息
        String method = request.getMethod();
        String uri = request.getRequestURI();
        String queryString = request.getQueryString();
        String clientIp = getClientIp(request);
        String userAgent = request.getHeader("User-Agent");

        // 构建完整URL
        String fullUrl = uri;
        if (NtaUtils.isNotEmpty(queryString)) {
            fullUrl += "?" + queryString;
        }

        // 记录请求日志
        log.info("请求开始 - {} {} | IP: {} | UA: {}",
                method, fullUrl, clientIp, userAgent);

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
        // 不做处理
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        Instant startTime = (Instant) request.getAttribute("startTime");
        if (startTime != null) {
            Duration duration = Duration.between(startTime, Instant.now());

            String method = request.getMethod();
            String uri = request.getRequestURI();
            int status = response.getStatus();

            // 记录请求完成日志
            if (ex != null) {
                log.error("请求异常 - {} {} | 状态: {} | 耗时: {}ms | 异常: {}",
                         method, uri, status, duration.toMillis(), ex.getMessage());
            } else {
                log.info("请求完成 - {} {} | 状态: {} | 耗时: {}ms",
                        method, uri, status, duration.toMillis());
            }
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (NtaUtils.isEmpty(ip) || UNKNOWN_IP.equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (NtaUtils.isEmpty(ip) || UNKNOWN_IP.equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (NtaUtils.isEmpty(ip) || UNKNOWN_IP.equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (NtaUtils.isEmpty(ip) || UNKNOWN_IP.equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (NtaUtils.isEmpty(ip) || UNKNOWN_IP.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 处理多个IP的情况，取第一个
        if (NtaUtils.isNotEmpty(ip) && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        return ip;
    }
}
