package com.geeksec.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.io.Serializable;

/**
 * 分页请求基类
 * 
 * 各服务的分页查询请求可以继承此类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Schema(description = "分页请求基类")
public class BasePageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码（从1开始）
     */
    @Schema(description = "页码", example = "1", minimum = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10", minimum = "1", maximum = "100")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "createTime")
    private String sortField;

    /**
     * 排序方向
     */
    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    private String sortOrder = "desc";

    /**
     * 获取偏移量
     * 
     * @return 偏移量
     */
    public Integer getOffset() {
        return (pageNum - 1) * pageSize;
    }

    /**
     * 设置默认分页参数
     */
    public void setDefaultPageParams() {
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        if (pageSize > 100) {
            pageSize = 100;
        }
        if (sortOrder == null || (!sortOrder.equalsIgnoreCase("asc") && !sortOrder.equalsIgnoreCase("desc"))) {
            sortOrder = "desc";
        }
    }

    /**
     * 是否为升序排序
     * 
     * @return 是否升序
     */
    public boolean isAscending() {
        return "asc".equalsIgnoreCase(sortOrder);
    }

    /**
     * 是否为降序排序
     * 
     * @return 是否降序
     */
    public boolean isDescending() {
        return "desc".equalsIgnoreCase(sortOrder);
    }
}
