package com.geeksec.auth.application.service;

import cn.dev33.satoken.stp.StpUtil;
import com.geeksec.auth.application.dto.command.LoginCommand;
import com.geeksec.auth.application.dto.response.LoginResponseDto;
import com.geeksec.auth.domain.model.User;
import com.geeksec.auth.domain.model.Username;
import com.geeksec.auth.domain.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Sa-Token 认证服务测试
 * 
 * 测试 Sa-Token 1.44.0 集成的认证功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@ExtendWith(MockitoExtension.class)
class SaTokenAuthServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @InjectMocks
    private SaTokenAuthService saTokenAuthService;

    private User testUser;
    private LoginCommand loginCommand;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = User.builder()
                .id(1L)
                .username(new Username("testuser"))
                .password("$2a$10$encoded.password.hash")
                .displayName("测试用户")
                .status(1)
                .build();

        // 创建登录命令
        loginCommand = new LoginCommand("testuser", "password123");
    }

    @Test
    void login_成功登录_应该返回登录响应() {
        // Given
        when(userRepository.findByUsername("testuser"))
                .thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches("password123", "$2a$10$encoded.password.hash"))
                .thenReturn(true);

        // When
        LoginResponseDto response = saTokenAuthService.login(loginCommand);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.userId()).isEqualTo(1L);
        assertThat(response.username()).isEqualTo("testuser");
        assertThat(response.displayName()).isEqualTo("测试用户");
        assertThat(response.token()).isNotNull();
        assertThat(response.expiresIn()).isGreaterThan(0);
    }

    @Test
    void login_用户不存在_应该抛出异常() {
        // Given
        when(userRepository.findByUsername("nonexistent"))
                .thenReturn(Optional.empty());

        LoginCommand invalidCommand = new LoginCommand("nonexistent", "password");

        // When & Then
        assertThatThrownBy(() -> saTokenAuthService.login(invalidCommand))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("用户名或密码错误");
    }

    @Test
    void login_密码错误_应该抛出异常() {
        // Given
        when(userRepository.findByUsername("testuser"))
                .thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches("wrongpassword", "$2a$10$encoded.password.hash"))
                .thenReturn(false);

        LoginCommand invalidCommand = new LoginCommand("testuser", "wrongpassword");

        // When & Then
        assertThatThrownBy(() -> saTokenAuthService.login(invalidCommand))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("用户名或密码错误");
    }

    @Test
    void login_用户被禁用_应该抛出异常() {
        // Given
        User disabledUser = User.builder()
                .id(1L)
                .username(new Username("testuser"))
                .password("$2a$10$encoded.password.hash")
                .displayName("测试用户")
                .status(0) // 禁用状态
                .build();

        when(userRepository.findByUsername("testuser"))
                .thenReturn(Optional.of(disabledUser));
        when(passwordEncoder.matches("password123", "$2a$10$encoded.password.hash"))
                .thenReturn(true);

        // When & Then
        assertThatThrownBy(() -> saTokenAuthService.login(loginCommand))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("用户已被禁用");
    }

    @Test
    void validateToken_有效Token_应该返回true() {
        // Given
        String validToken = "valid-token";
        
        // 模拟 Sa-Token 行为需要实际的集成测试
        // 这里仅作为示例，实际测试需要 Spring Boot Test 环境
        
        // When
        boolean isValid = saTokenAuthService.validateToken(validToken);

        // Then
        // 在单元测试中，由于没有 Sa-Token 上下文，这里会返回 false
        assertThat(isValid).isFalse();
    }

    @Test
    void logout_有效Token_应该返回true() {
        // Given
        String validToken = "valid-token";

        // When
        boolean result = saTokenAuthService.logout(validToken);

        // Then
        // 在单元测试中，由于没有 Sa-Token 上下文，这里会返回 false
        assertThat(result).isFalse();
    }

    @Test
    void hasPermission_检查权限_应该返回false() {
        // Given
        String permission = "user:read";

        // When
        boolean hasPermission = saTokenAuthService.hasPermission(permission);

        // Then
        // 在单元测试中，由于没有 Sa-Token 上下文，这里会返回 false
        assertThat(hasPermission).isFalse();
    }

    @Test
    void hasRole_检查角色_应该返回false() {
        // Given
        String role = "admin";

        // When
        boolean hasRole = saTokenAuthService.hasRole(role);

        // Then
        // 在单元测试中，由于没有 Sa-Token 上下文，这里会返回 false
        assertThat(hasRole).isFalse();
    }

    @Test
    void getUserPermissions_获取用户权限_应该返回空列表() {
        // Given
        Long userId = 1L;

        // When
        var permissions = saTokenAuthService.getUserPermissions(userId);

        // Then
        assertThat(permissions).isEmpty();
    }

    @Test
    void getUserRoles_获取用户角色_应该返回空列表() {
        // Given
        Long userId = 1L;

        // When
        var roles = saTokenAuthService.getUserRoles(userId);

        // Then
        assertThat(roles).isEmpty();
    }
}
