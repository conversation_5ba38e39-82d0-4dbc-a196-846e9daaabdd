# Spring Datasource
spring.datasource.url=jdbc:h2:mem:authdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# Flyway
spring.flyway.enabled=false
spring.flyway.locations=classpath:db/migration,classpath:db/test_migration
# 对于H2内存数据库，通常希望每次都从头开始迁移，或者至少允许基于基线的迁移
spring.flyway.baseline-on-migrate=true
spring.flyway.clean-on-validation-error=true
# 如果有必要，可以清理 schema 再迁移，但 baseline-on-migrate 通常更安全
# spring.flyway.clean-on-validation-error=true # 谨慎使用

spring.jpa.hibernate.ddl-auto=create-drop
logging.level.org.springframework=DEBUG

# H2 Console (可选, 便于调试)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console
spring.h2.console.settings.web-allow-others=false
