# Auth Service 测试环境配置
# 继承通用测试环境配置

spring:
  profiles:
    active: test
    include: common

  # Auth服务测试环境数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:test-db.nta.local}:${DB_PORT:5432}/${DB_NAME:nta_auth_test}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:nta_auth_test}
    password: ${DB_PASSWORD:nta_auth_test123}

  # Auth服务测试环境Redis配置
  data:
    redis:
      host: ${REDIS_HOST:test-redis.nta.local}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:test123}
      database: ${REDIS_DATABASE:1}  # Auth服务使用数据库1
      timeout: 5000ms

# Auth服务特定配置
auth:
  # Sa-Token 测试环境配置
  token:
    # 测试环境token配置
    timeout: 7200  # 测试环境2小时过期
    active-timeout: -1  # 永不冻结
    is-concurrent: true  # 允许并发登录
    is-share: true  # 共享token
    token-style: uuid  # token风格
  
  # 密码策略配置
  password:
    min-length: 8  # 测试环境密码最小长度
    require-uppercase: true
    require-lowercase: true
    require-numbers: true
    require-special-chars: false
    max-attempts: 5  # 测试环境限制尝试次数
    lock-duration: 600  # 锁定10分钟
  
  # 测试环境用户配置
  test-users:
    - username: test_admin
      password: TestAdmin123
      roles: [ADMIN, USER]
    - username: test_user
      password: TestUser123
      roles: [USER]

# Sa-Token 测试环境配置
sa-token:
  token-name: Authorization
  timeout: 7200  # 测试环境2小时
  active-timeout: -1
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-log: true  # 测试环境启用日志

# 服务器配置
server:
  port: ${SERVER_PORT:8081}
  servlet:
    context-path: ${CONTEXT_PATH:/auth}

# 日志配置
logging:
  level:
    '[com.geeksec.auth]': INFO
    '[com.mybatisflex]': INFO
    '[cn.dev33.satoken]': INFO
