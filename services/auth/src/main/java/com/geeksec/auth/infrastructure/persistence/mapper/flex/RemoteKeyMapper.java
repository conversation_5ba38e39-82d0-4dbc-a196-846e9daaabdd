package com.geeksec.auth.infrastructure.persistence.mapper.flex;

import com.geeksec.auth.infrastructure.persistence.entity.RemoteKeyEntity;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

import static com.geeksec.auth.infrastructure.persistence.entity.table.RemoteKeyEntityTableDef.REMOTE_KEY_ENTITY;

/**
 * 远程密钥Mapper接口
 *
 * 使用 MyBatis-Flex 1.10.9 BaseMapper + 表定义类
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Mapper
public interface RemoteKeyMapper extends BaseMapper<RemoteKeyEntity> {

    /**
     * 获取所有远程密钥
     *
     * @return 远程密钥列表
     */
    default List<String> getAllApiKeys() {
        return selectListByQuery(QueryWrapper.create()
                .where(REMOTE_KEY_ENTITY.DELETED.eq(0)))
                .stream()
                .map(RemoteKeyEntity::getApiKey)
                .toList();
    }
}
