package com.geeksec.auth.infrastructure.config;

import com.geeksec.auth.interfaces.rest.interceptor.TokenExtractorInterceptor;
import com.geeksec.common.config.WebMvcBaseConfig;
import com.geeksec.common.interceptor.PermissionInterceptor;
import com.geeksec.common.interceptor.PerformanceInterceptor;
import com.geeksec.common.interceptor.RequestLogInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;

/**
 * Web MVC 配置类
 *
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfig extends WebMvcBaseConfig {

    private final PermissionInterceptor permissionInterceptor;
    private final TokenExtractorInterceptor tokenExtractorInterceptor;

    public WebMvcConfig(
        PerformanceInterceptor performanceInterceptor,
        PermissionInterceptor permissionInterceptor,
        TokenExtractorInterceptor tokenExtractorInterceptor,
        RequestLogInterceptor requestLogInterceptor
    ) {
        super(requestLogInterceptor, performanceInterceptor);
        this.permissionInterceptor = permissionInterceptor;
        this.tokenExtractorInterceptor = tokenExtractorInterceptor;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 继承父类的通用拦截器配置
        super.addInterceptors(registry);

        // 令牌提取拦截器，必须在权限拦截器之前
        registry.addInterceptor(tokenExtractorInterceptor)
            .addPathPatterns("/api/**")
            .excludePathPatterns("/api/v1/auth/login", "/api/v1/auth/validate");

        // 权限拦截器
        registry.addInterceptor(permissionInterceptor)
            .addPathPatterns("/api/**")
            .excludePathPatterns("/api/v1/auth/login", "/api/v1/auth/validate");
    }
}