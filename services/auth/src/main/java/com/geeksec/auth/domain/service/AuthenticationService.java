package com.geeksec.auth.domain.service;

import com.geeksec.auth.domain.model.User;
import com.geeksec.auth.domain.repository.RemoteKeyRepository;
import com.geeksec.auth.domain.repository.UserRepository;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 认证领域服务
 *
 * <AUTHOR>
 */
@Service
public class AuthenticationService {
    private final UserRepository userRepository;
    private final RemoteKeyRepository remoteKeyRepository;

    public AuthenticationService(UserRepository userRepository, RemoteKeyRepository remoteKeyRepository) {
        this.userRepository = userRepository;
        this.remoteKeyRepository = remoteKeyRepository;
    }

    /**
     * 用户认证
     *
     * @param username 用户名
     * @param password 密码
     * @return 认证结果
     */
    public AuthenticationResult authenticate(String username, String password) {
        Optional<User> userOptional = userRepository.findByUsername(new User.Username(username));

        if (userOptional.isEmpty()) {
            return AuthenticationResult.failure("用户不存在");
        }

        User user = userOptional.get();

        if (user.getStatus() != User.UserStatus.ACTIVE) {
            return AuthenticationResult.failure("用户已禁用");
        }

        if (!user.authenticate(password)) {
            return AuthenticationResult.failure("密码错误");
        }

        // 使用 Sa-Token 进行登录并生成令牌
        cn.dev33.satoken.stp.StpUtil.login(user.getId());
        String token = cn.dev33.satoken.stp.StpUtil.getTokenValue();

        return AuthenticationResult.success(token, user);
    }

    /**
     * 远程登录
     *
     * @param username 用户名
     * @param identification 身份标识
     * @return 认证结果
     */
    public AuthenticationResult remoteAuthenticate(String username, String identification) {
        // 验证身份标识
        if (!validateRemoteIdentification(username, identification)) {
            return AuthenticationResult.failure("身份标识无效");
        }

        Optional<User> userOptional = userRepository.findByUsername(new User.Username(username));

        if (userOptional.isEmpty()) {
            return AuthenticationResult.failure("用户不存在");
        }

        User user = userOptional.get();

        if (user.getStatus() != User.UserStatus.ACTIVE) {
            return AuthenticationResult.failure("用户已禁用");
        }

        // 使用 Sa-Token 进行登录并生成令牌
        cn.dev33.satoken.stp.StpUtil.login(user.getId());
        String token = cn.dev33.satoken.stp.StpUtil.getTokenValue();

        return AuthenticationResult.success(token, user);
    }

    /**
     * 验证远程登录身份标识
     *
     * @param username 用户名
     * @param identification 身份标识
     * @return 是否有效
     */
    private boolean validateRemoteIdentification(String username, String identification) {
        // 获取远程密钥
        List<String> remoteKeys = getRemoteKeys();
        if (remoteKeys.isEmpty()) {
            return false;
        }

        // 生成当前日期字符串
        String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());

        // 验证身份标识
        return remoteKeys.stream()
            .map(remoteKey -> {
                // 生成验证字符串：username_date_remoteKey
                String currentIdentification = "%s_%s_%s".formatted(username, dateStr, remoteKey);
                // 计算MD5
                return DigestUtils.md5DigestAsHex(currentIdentification.getBytes());
            })
            .anyMatch(identification::equals);
    }

    /**
     * 获取远程密钥
     *
     * @return 远程密钥列表
     */
    private List<String> getRemoteKeys() {
        // 从数据库中获取远程密钥
        return remoteKeyRepository.getAllRemoteKeys();
    }

    /**
     * 验证令牌
     *
     * @param token 令牌
     * @return 验证结果
     */
    public TokenValidationResult validateToken(String token) {
        try {
            Object loginId = cn.dev33.satoken.stp.StpUtil.getLoginIdByToken(token);
            if (loginId != null) {
                User user = userRepository.findById(new User.UserId(Long.valueOf(loginId.toString())))
                        .orElse(null);
                if (user != null) {
                    return TokenValidationResult.valid(user);
                }
            }
            return TokenValidationResult.invalid("令牌无效");
        } catch (Exception e) {
            return TokenValidationResult.invalid("令牌验证失败: " + e.getMessage());
        }
    }

    /**
     * 刷新令牌
     *
     * @param token 原令牌
     * @return 新令牌
     */
    public TokenRefreshResult refreshToken(String token) {
        try {
            Object loginId = cn.dev33.satoken.stp.StpUtil.getLoginIdByToken(token);
            if (loginId != null) {
                // 重新登录生成新 Token
                cn.dev33.satoken.stp.StpUtil.login(loginId);
                String newToken = cn.dev33.satoken.stp.StpUtil.getTokenValue();

                // 注销旧 Token
                cn.dev33.satoken.stp.StpUtil.logoutByTokenValue(token);

                return TokenRefreshResult.success(newToken);
            }
            return TokenRefreshResult.failure("令牌无效");
        } catch (Exception e) {
            return TokenRefreshResult.failure("刷新令牌失败: " + e.getMessage());
        }
    }

    /**
     * 登出
     *
     * @param token 令牌
     */
    public void logout(String token) {
        try {
            cn.dev33.satoken.stp.StpUtil.logoutByTokenValue(token);
        } catch (Exception e) {
            // 忽略登出异常
        }
    }

    /**
     * 认证结果
     */
    public static class AuthenticationResult {
        private final boolean success;
        private final String token;
        private final User user;
        private final String errorMessage;

        private AuthenticationResult(boolean success, String token, User user, String errorMessage) {
            this.success = success;
            this.token = token;
            this.user = user;
            this.errorMessage = errorMessage;
        }

        public static AuthenticationResult success(String token, User user) {
            return new AuthenticationResult(true, token, user, null);
        }

        public static AuthenticationResult failure(String errorMessage) {
            return new AuthenticationResult(false, null, null, errorMessage);
        }

        public boolean isSuccess() {
            return success;
        }

        public String getToken() {
            return token;
        }

        public User getUser() {
            return user;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }

    /**
     * 令牌验证结果
     */
    public static class TokenValidationResult {
        private final boolean valid;
        private final User user;
        private final String errorMessage;

        private TokenValidationResult(boolean valid, User user, String errorMessage) {
            this.valid = valid;
            this.user = user;
            this.errorMessage = errorMessage;
        }

        public static TokenValidationResult valid(User user) {
            return new TokenValidationResult(true, user, null);
        }

        public static TokenValidationResult invalid(String errorMessage) {
            return new TokenValidationResult(false, null, errorMessage);
        }

        public boolean isValid() {
            return valid;
        }

        public User getUser() {
            return user;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }

    /**
     * 令牌刷新结果
     */
    public static class TokenRefreshResult {
        private final boolean success;
        private final String newToken;
        private final String errorMessage;

        private TokenRefreshResult(boolean success, String newToken, String errorMessage) {
            this.success = success;
            this.newToken = newToken;
            this.errorMessage = errorMessage;
        }

        public static TokenRefreshResult success(String newToken) {
            return new TokenRefreshResult(true, newToken, null);
        }

        public static TokenRefreshResult failure(String errorMessage) {
            return new TokenRefreshResult(false, null, errorMessage);
        }

        public boolean isSuccess() {
            return success;
        }

        public String getNewToken() {
            return newToken;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }
}
