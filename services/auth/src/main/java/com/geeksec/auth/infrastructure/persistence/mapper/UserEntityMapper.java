package com.geeksec.auth.infrastructure.persistence.mapper;

import java.time.LocalDateTime;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.geeksec.auth.domain.model.User;
import com.geeksec.auth.infrastructure.persistence.entity.UserEntity;

/**
 * 用户映射器
 *
 * <AUTHOR>
 */
@Component
public class UserEntityMapper {

    private final RoleEntityMapper roleEntityMapper;

    public UserEntityMapper(RoleEntityMapper roleEntityMapper) {
        this.roleEntityMapper = roleEntityMapper;
    }

    /**
     * 将领域模型转换为实体
     */
    public UserEntity toEntity(User user) {
        UserEntity entity = new UserEntity();
        entity.setUserId(user.getId().getValue());
        entity.setUsername(user.getUsername().getValue());
        entity.setPassword(user.getPassword().getHashedValue());
        entity.setDisplayName(user.getDisplayName());
        entity.setStatus(user.getStatus() == User.UserStatus.ACTIVE ? 1 : 0);
        entity.setCreatedAt(LocalDateTime.ofInstant(user.getCreatedAt(), java.time.ZoneId.systemDefault()));
        entity.setUpdatedAt(LocalDateTime.ofInstant(user.getUpdatedAt(), java.time.ZoneId.systemDefault()));

        // 转换角色
        entity.setRoles(user.getRoles().stream()
                .map(roleEntityMapper::toEntity)
                .collect(Collectors.toSet()));

        return entity;
    }

    /**
     * 将实体转换为领域模型
     */
    public User toDomain(UserEntity entity) {
        return new User.Builder()
                .withId(new User.UserId(entity.getUserId()))
                .withUsername(new User.Username(entity.getUsername()))
                .withPassword(User.Password.fromHashed(entity.getPassword()))
                .withDisplayName(entity.getDisplayName())
                .withStatus(entity.getStatus() == 1 ? User.UserStatus.ACTIVE : User.UserStatus.INACTIVE)
                .withRoles(entity.getRoles().stream()
                        .map(roleEntityMapper::toDomain)
                        .collect(Collectors.toSet()))
                .withCreatedAt(entity.getCreatedAt().atZone(java.time.ZoneId.systemDefault()).toInstant())
                .withUpdatedAt(entity.getUpdatedAt().atZone(java.time.ZoneId.systemDefault()).toInstant())
                .build();
    }
}
