package com.geeksec.auth.application.service;

import org.springframework.stereotype.Service;

import com.geeksec.auth.application.dto.command.LoginCommand;
import com.geeksec.auth.application.dto.command.RemoteLoginCommand;
import com.geeksec.auth.application.dto.response.LoginResponseDto;
import com.geeksec.auth.domain.service.AuthenticationService;
import com.geeksec.auth.domain.service.AuthenticationService.AuthenticationResult;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 认证应用服务
 *
 * 整合原有认证逻辑和 Sa-Token 1.44.0 权限认证
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthApplicationService {

    private final AuthenticationService authenticationService;
    private final SaTokenAuthService saTokenAuthService;

    /**
     * 用户登录
     *
     * 使用 Sa-Token 进行认证和令牌管理
     *
     * @param command 登录命令
     * @return 登录响应
     */
    public LoginResponseDto login(LoginCommand command) {
        log.info("用户登录: {}", command.username());

        // 使用 Sa-Token 认证服务进行登录
        return saTokenAuthService.login(command);
    }

    /**
     * 远程登录
     *
     * @param request 远程登录请求
     * @return 登录结果，包含令牌和成功标志
     */
    public AuthenticationResult remoteLogin(RemoteLoginCommand command) {
        return authenticationService.remoteAuthenticate(
            command.username(),
            command.identification()
        );
    }

    /**
     * 用户登出
     *
     * 使用 Sa-Token 进行登出
     *
     * @param token 令牌
     * @return 是否成功
     */
    public boolean logout(String token) {
        log.info("用户登出");
        return saTokenAuthService.logout(token);
    }

    /**
     * 验证令牌
     *
     * 使用 Sa-Token 验证令牌有效性
     *
     * @param token 令牌
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        return saTokenAuthService.validateToken(token);
    }

    /**
     * 刷新令牌
     *
     * 使用 Sa-Token 刷新令牌
     *
     * @param token 原令牌
     * @return 新令牌
     */
    public String refreshToken(String token) {
        log.info("刷新令牌");
        return saTokenAuthService.refreshToken(token);
    }
}
