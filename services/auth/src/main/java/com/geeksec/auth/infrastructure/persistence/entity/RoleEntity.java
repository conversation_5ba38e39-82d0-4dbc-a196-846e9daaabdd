package com.geeksec.auth.infrastructure.persistence.entity;

import com.geeksec.auth.domain.model.Role;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 角色实体
 *
 * 使用 MyBatis-Flex 1.10.9 注解进行映射
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Table("sys_role")
public class RoleEntity {

    /**
     * 角色ID - 主键，自增
     */
    @Id(keyType = KeyType.Auto)
    @Column("role_id")
    private Long id;

    /**
     * 角色名称
     */
    @Column("role_name")
    private String name;

    /**
     * 角色描述
     */
    @Column("description")
    private String description;

    /**
     * 创建时间
     */
    @Column(value = "created_at", onInsertValue = "now()")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(value = "updated_at", onInsertValue = "now()", onUpdateValue = "now()")
    private LocalDateTime updatedAt;

    /**
     * 逻辑删除标记：0-未删除，1-已删除
     */
    @Column("deleted")
    private Integer deleted;

    /**
     * 权限关联 - 这个字段不会映射到数据库，需要手动处理
     */
    private transient Set<PermissionEntity> permissions = new HashSet<>();

    public Role toDomain() {
        return new Role.Builder()
            .withId(new Role.RoleId(id))
            .withName(name)
            .withDescription(description)
            .withPermissions(permissions.stream()
                .map(PermissionEntity::toDomain)
                .collect(java.util.stream.Collectors.toSet()))
            .build();
    }

    public static RoleEntity fromDomain(Role role) {
        RoleEntity entity = new RoleEntity();
        entity.setId(role.getId().getValue());
        entity.setName(role.getName());
        entity.setDescription(role.getDescription());
        entity.setPermissions(role.getPermissions().stream()
            .map(PermissionEntity::fromDomain)
            .collect(java.util.stream.Collectors.toSet()));
        return entity;
    }
}
