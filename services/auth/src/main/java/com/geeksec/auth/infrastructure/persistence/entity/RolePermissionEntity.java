package com.geeksec.auth.infrastructure.persistence.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

/**
 * 角色权限关联实体
 *
 * 使用 MyBatis-Flex 1.10.9 注解进行映射
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Table("role_permission")
public class RolePermissionEntity {

    /**
     * 主键ID - 自增
     */
    @Id(keyType = KeyType.Auto)
    private Long id;

    /**
     * 角色ID
     */
    @Column("role_id")
    private Long roleId;

    /**
     * 权限ID
     */
    @Column("permission_id")
    private Long permissionId;
}
