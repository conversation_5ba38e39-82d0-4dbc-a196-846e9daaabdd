package com.geeksec.auth.infrastructure.persistence.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 用户角色关联实体表定义类
 *
 * 由 MyBatis-Flex 注解处理器生成，用于类型安全的查询构建
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public class UserRoleEntityTableDef extends TableDef {

    /**
     * 用户角色关联实体表定义实例
     */
    public static final UserRoleEntityTableDef USER_ROLE_ENTITY = new UserRoleEntityTableDef();

    /**
     * 主键ID - 自增
     */
    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * 用户ID
     */
    public final QueryColumn USER_ID = new QueryColumn(this, "user_id");

    /**
     * 角色ID
     */
    public final QueryColumn ROLE_ID = new QueryColumn(this, "role_id");

    /**
     * 构造函数
     */
    public UserRoleEntityTableDef() {
        super("", "user_role");
    }

    /**
     * 带别名的构造函数
     */
    public UserRoleEntityTableDef(String alias) {
        super("", "user_role", alias);
    }
}
