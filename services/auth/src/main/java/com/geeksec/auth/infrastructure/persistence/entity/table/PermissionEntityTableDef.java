package com.geeksec.auth.infrastructure.persistence.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 权限实体表定义类
 *
 * 由 MyBatis-Flex 注解处理器生成，用于类型安全的查询构建
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public class PermissionEntityTableDef extends TableDef {

    /**
     * 权限实体表定义实例
     */
    public static final PermissionEntityTableDef PERMISSION_ENTITY = new PermissionEntityTableDef();

    /**
     * 权限ID - 主键，自增
     */
    public final QueryColumn ID = new QueryColumn(this, "permission_id");

    /**
     * 权限编码
     */
    public final QueryColumn CODE = new QueryColumn(this, "permission_code");

    /**
     * 权限名称
     */
    public final QueryColumn NAME = new QueryColumn(this, "permission_name");

    /**
     * 权限描述
     */
    public final QueryColumn DESCRIPTION = new QueryColumn(this, "description");

    /**
     * 创建时间
     */
    public final QueryColumn CREATED_AT = new QueryColumn(this, "created_at");

    /**
     * 更新时间
     */
    public final QueryColumn UPDATED_AT = new QueryColumn(this, "updated_at");

    /**
     * 逻辑删除标记：0-未删除，1-已删除
     */
    public final QueryColumn DELETED = new QueryColumn(this, "deleted");

    /**
     * 构造函数
     */
    public PermissionEntityTableDef() {
        super("", "sys_permission");
    }

    /**
     * 带别名的构造函数
     */
    public PermissionEntityTableDef(String alias) {
        super("", "sys_permission", alias);
    }
}
