package com.geeksec.auth.infrastructure.persistence.mapper.flex;

import com.geeksec.auth.infrastructure.persistence.entity.PermissionEntity;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import static com.geeksec.auth.infrastructure.persistence.entity.table.PermissionEntityTableDef.PERMISSION_ENTITY;

/**
 * 权限Mapper接口
 *
 * 使用 MyBatis-Flex 1.10.9 BaseMapper + 表定义类
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Mapper
public interface PermissionMapper extends BaseMapper<PermissionEntity> {

    /**
     * 根据权限编码查询权限
     *
     * @param code 权限编码
     * @return 权限实体
     */
    default PermissionEntity findByCode(String code) {
        return selectOneByQuery(QueryWrapper.create()
                .where(PERMISSION_ENTITY.CODE.eq(code))
                .and(PERMISSION_ENTITY.DELETED.eq(0)));
    }

    /**
     * 检查权限编码是否存在
     *
     * @param code 权限编码
     * @return 是否存在
     */
    default boolean existsByCode(String code) {
        return selectCountByQuery(QueryWrapper.create()
                .where(PERMISSION_ENTITY.CODE.eq(code))
                .and(PERMISSION_ENTITY.DELETED.eq(0))) > 0;
    }
}
