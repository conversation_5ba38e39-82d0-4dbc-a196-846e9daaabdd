package com.geeksec.auth.interfaces.rest;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.geeksec.auth.application.dto.command.ChangePasswordCommand;
import com.geeksec.auth.application.dto.command.CreateUserCommand;
import com.geeksec.auth.application.dto.command.UpdateUserCommand;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.auth.application.dto.response.UserResponseDto;
import com.geeksec.auth.application.service.UserApplicationService;
import com.geeksec.common.annotation.OperationLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.hateoas.Link;
import org.springframework.http.CacheControl;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.linkTo;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

/**
 * 用户控制器
 *
 * 使用 Sa-Token 1.44.0 权限认证替代 Spring Security
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/users")
@RequiredArgsConstructor
@Tag(name = "用户管理", description = "用户信息管理相关接口")
@SecurityRequirement(name = "saToken")
public class UserController {

    private final UserApplicationService userApplicationService;

    /**
     * 创建用户
     */
    @PostMapping
    @SaCheckPermission("user:create")
    @Operation(summary = "创建用户", description = "创建新用户，需要 user:create 权限")
    @OperationLog(module = "用户", operation = "创建", description = "创建新用户")
    public ResponseEntity<ApiResponse<UserResponseDto>> createUser(@Valid @RequestBody CreateUserCommand command) {
        UserResponseDto createdUser = userApplicationService.createUser(command);
        List<Link> links = List.of(
            linkTo(methodOn(UserController.class).createUser(command)).withSelfRel(),
            linkTo(methodOn(UserController.class).getUser(createdUser.id())).withRel("self")
        );
        return ResponseEntity.created(linkTo(methodOn(UserController.class).getUser(createdUser.id())).toUri())
            .body(ApiResponse.success(createdUser, links));
    }

    /**
     * 获取用户
     */
    @GetMapping("/{userId}")
    @SaCheckPermission("user:read")
    @Operation(summary = "获取用户", description = "根据用户ID获取用户信息，需要 user:read 权限")
    public ResponseEntity<ApiResponse<UserResponseDto>> getUser(@PathVariable Long userId) {
        return userApplicationService.getUser(userId)
            .map(user -> {
                List<Link> links = List.of(
                    linkTo(methodOn(UserController.class).getUser(userId)).withSelfRel(),
                    linkTo(methodOn(UserController.class).updateUser(userId, null)).withRel("update"),
                    linkTo(methodOn(UserController.class).deleteUser(userId)).withRel("delete")
                );
                return ResponseEntity.ok()
                    .cacheControl(CacheControl.maxAge(1, TimeUnit.HOURS))
                    .body(ApiResponse.success(user, links));
            })
            .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 获取所有用户
     */
    @GetMapping
    @SaCheckPermission("user:read")
    @Operation(summary = "获取所有用户", description = "分页获取用户列表，需要 user:read 权限")
    public ResponseEntity<ApiResponse<List<UserResponseDto>>> getAllUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<UserResponseDto> users = userApplicationService.getAllUsers(pageable);

        ApiResponse.PageInfo pageInfo = new ApiResponse.PageInfo();
        pageInfo.setPage(page);
        pageInfo.setSize(size);
        pageInfo.setTotalElements(users.getTotalElements());
        pageInfo.setTotalPages(users.getTotalPages());

        List<Link> links = List.of(
            linkTo(methodOn(UserController.class).getAllUsers(page, size)).withSelfRel(),
            linkTo(methodOn(UserController.class).createUser(null)).withRel("create")
        );

        return ResponseEntity.ok()
            .cacheControl(CacheControl.maxAge(5, TimeUnit.MINUTES))
            .body(ApiResponse.success(users.getContent(), pageInfo, links));
    }

    /**
     * 更新用户
     */
    @PutMapping("/{userId}")
    @SaCheckPermission("user:update")
    @Operation(summary = "更新用户", description = "更新用户信息，需要 user:update 权限")
    public ResponseEntity<ApiResponse<UserResponseDto>> updateUser(
            @PathVariable Long userId,
            @Valid @RequestBody UpdateUserCommand command) {
        return userApplicationService.updateUser(userId, command)
                .map(updatedUser -> {
                    List<Link> links = List.of(
                        linkTo(methodOn(UserController.class).updateUser(userId, command)).withSelfRel(),
                        linkTo(methodOn(UserController.class).getUser(userId)).withRel("self")
                    );
                    return ResponseEntity.ok(ApiResponse.success(updatedUser, links));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{userId}")
    @SaCheckPermission("user:delete")
    @Operation(summary = "删除用户", description = "删除指定用户，需要 user:delete 权限")
    public ResponseEntity<ApiResponse<Void>> deleteUser(@PathVariable Long userId) {
        userApplicationService.deleteUser(userId);
        return ResponseEntity.noContent().build();
    }

    /**
     * 修改密码
     */
    @PostMapping("/{userId}/password")
    @SaCheckLogin
    @Operation(summary = "修改密码", description = "修改用户密码，需要登录")
    @OperationLog(module = "用户", operation = "修改密码", description = "修改用户密码")
    public ResponseEntity<ApiResponse<Void>> changePassword(
            @PathVariable Long userId,
            @Valid @RequestBody ChangePasswordCommand command) {
        userApplicationService.changePassword(userId, command.oldPassword(), command.newPassword());

        List<Link> links = List.of(
            linkTo(methodOn(UserController.class).changePassword(userId, command)).withSelfRel(),
            linkTo(methodOn(UserController.class).getUser(userId)).withRel("self")
        );

        return ResponseEntity.ok(ApiResponse.success((Void) null, links));
    }
}
