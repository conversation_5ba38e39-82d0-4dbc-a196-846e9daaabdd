package com.geeksec.auth.infrastructure.persistence.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 用户实体表定义类
 *
 * 由 MyBatis-Flex 注解处理器生成，用于类型安全的查询构建
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public class UserEntityTableDef extends TableDef {

    /**
     * 用户实体表定义实例
     */
    public static final UserEntityTableDef USER_ENTITY = new UserEntityTableDef();

    /**
     * 用户ID - 主键，自增
     */
    public final QueryColumn USER_ID = new QueryColumn(this, "user_id");

    /**
     * 用户名 - 唯一
     */
    public final QueryColumn USERNAME = new QueryColumn(this, "username");

    /**
     * 密码 - 加密存储
     */
    public final QueryColumn PASSWORD = new QueryColumn(this, "password");

    /**
     * 显示名称
     */
    public final QueryColumn DISPLAY_NAME = new QueryColumn(this, "display_name");

    /**
     * 用户组ID
     */
    public final QueryColumn GROUP_ID = new QueryColumn(this, "group_id");

    /**
     * 用户状态：0-禁用，1-启用
     */
    public final QueryColumn STATUS = new QueryColumn(this, "status");

    /**
     * 创建时间
     */
    public final QueryColumn CREATED_AT = new QueryColumn(this, "created_at");

    /**
     * 更新时间
     */
    public final QueryColumn UPDATED_AT = new QueryColumn(this, "updated_at");

    /**
     * 逻辑删除标记：0-未删除，1-已删除
     */
    public final QueryColumn DELETED = new QueryColumn(this, "deleted");

    /**
     * 版本号 - 乐观锁
     */
    public final QueryColumn VERSION = new QueryColumn(this, "version");

    /**
     * 构造函数
     */
    public UserEntityTableDef() {
        super("", "sys_user");
    }

    /**
     * 带别名的构造函数
     */
    public UserEntityTableDef(String alias) {
        super("", "sys_user", alias);
    }
}
