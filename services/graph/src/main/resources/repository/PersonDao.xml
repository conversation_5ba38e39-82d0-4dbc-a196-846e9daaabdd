<!--
    Copyright (c) 2022- All project authors. All rights reserved.
    
    This source code is licensed under Apache 2.0 License.
-->
<mapper namespace="com.geeksec.graph.repository.PersonDao">

    <!-- 如果 space 与 yml 中声明的或 mapper 的 space 一致，可不写 -->
    <select id="select1">
        RETURN 1
    </select>

    <select id="selectPersonMap">
        match (v:person) return v.person.name as name, v.person.age as age limit 1
    </select>

    <select id="selectPerson" resultType="com.geeksec.graph.pojo.vertex.Person">
        match (v:person) return v.person.name as name, v.person.age as age limit 1
    </select>

    <select id="selectListString" resultType="java.lang.String">
        match (v:person) return v.person.name as name limit 100
    </select>

    <select id="selectPersonsMap" resultType="java.util.Map">
        match (v:person) return v limit 100
    </select>

    <select id="selectPersons" resultType="com.geeksec.graph.pojo.vertex.Person">
        match (v:person) return v.person.name as name, v.person.age  as age limit 100
    </select>

    <select id="selectPersonsSet" resultType="java.util.Map">
        match (v:person) return v.person.name as name, v.person.age  as age limit 100
    </select>

    <select id="selectInt" >
        return 1
    </select>

    <select id="selectV">
        match (v:person) return v limit 1
    </select>

    <select id="selectListV" resultType="com.geeksec.graph.pojo.vertex.Person">
        match (v:person) return v limit 3
    </select>

    <select id="selectString">
        match (v:person) return v.person.name as name limit 1
    </select>

    <select id="selectStringParam">
        return $p0
    </select>

    <select id="selectIntParam">
        return $p0
    </select>

    <select id="selectBoolParam">
        return $p0
    </select>

    <select id="selectCustomPage" resultType="com.geeksec.graph.pojo.vertex.Person">
        MATCH (n: person)
        RETURN n
    </select>

    <select id="selectCustomPageAndName" resultType="com.geeksec.graph.pojo.vertex.Person">
        MATCH (n: person{ name: $p1 })
        RETURN n
    </select>

    <select id="testMulti">
        return "1111";
        return "222";
    </select>

    <select id="selectMapWhenNull">
        MATCH (n: person)
        WHERE 1 == 2
        RETURN n
    </select>

    <select id="testSpaceSwitchStep1">
        use test;
        INSERT VERTEX paragraph( name ) VALUES -31415926:('spaceSwitch');
    </select>

    <select id="testSpaceSwitchStep2">
        MATCH (n: paragraph)
        WHERE id(n) == -31415926
        RETURN count(n)
    </select>

    <insert id="insertWithTimestamp">
        INSERT VERTEX IF NOT EXISTS `person` (
        `birthday`
        )
        VALUES ${ ng.valueFmt(person.name) } : (
        ${ ng.valueFmt(person.birthday) }
        );
    </insert>

    <select id="selectVertexes" resultType="org.nebula.contrib.ngbatis.models.data.NgVertex">
        MATCH (n)
        RETURN n
        LIMIT 10
    </select>

    <select id="selectEdges" resultType="org.nebula.contrib.ngbatis.models.data.NgEdge">
        MATCH (n: person)-[r]->(n2: person)
        RETURN r
        LIMIT 10
    </select>

    <select id="selectSubgraph" resultType="org.nebula.contrib.ngbatis.models.data.NgSubgraph">
        GET SUBGRAPH WITH PROP 3 STEPS FROM "叶小南"
        BOTH like
        YIELD VERTICES AS nodes, EDGES AS relationships
    </select>

    <select id="selectByPerson" resultType="com.geeksec.graph.pojo.vertex.Person">
        MATCH (n: person)
        WHERE 1 == 1
        @for ( entry in p ) {
        @if ( isNotEmpty( entry.value ) ) {
        AND n.person.${ entry.key } == $p.${ entry.key }
        @}
        @}
        RETURN n
    </select>

    <insert id="insertDynamic">
        @for( v in p0 ) {
        @var javaV = @ng_args[0].get(vLP.dataIndex );
        @var vid = ng.id( javaV );
        @var kv = ng.kv( javaV.propertyList );
        @var cols = ng.join( @kv.columns, ",", "ng.schemaFmt" );
        @var vals = ng.join( @kv.values );
        INSERT VERTEX IF NOT EXISTS `${ v.tagName }` ( ${ cols } ) VALUES ${ vid } : ( ${ vals } );
        @}
    </insert>
    
</mapper>