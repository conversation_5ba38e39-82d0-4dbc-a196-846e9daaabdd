<mapper namespace="com.geeksec.graph.repository.AppDao">

    <select id="listAppAllEdgeTypeAssociationNext" resultType="com.geeksec.graph.vo.VertexEdgeNextVo">
        MATCH (APP:APP)
        WHERE id(APP) == ${ng.valueFmt(condition.str)}
        RETURN DISTINCT
        "APP" AS fromType,
        0 AS fromBlackList,
        id(APP) AS fromAddr,
        id(APP) AS fromLabel,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        "" AS toLabel,
        true as sourceStatus,
        false as directionStatus,
        null AS vInfo,
        "" AS teage
        LIMIT 1

        @for( edgeItem in condition.edgeInfo ) {
            @if (edgeItem.edge == 'ua_belong_app'){
                UNION ALL
                MATCH
                (APP:APP)-[ua_belong_app:ua_belong_app]-(UA:UA)
                WHERE
                id(APP) == ${ng.valueFmt(condition.str)}
                RETURN
                "UA" AS fromType,
                0 AS fromBlackList,
                UA.UA.ua_id AS fromAddr,
                UA.UA.ua_id AS fromLabel,
                "APP" AS toType,
                0 AS toBlackList,
                id(APP) AS toAddr,
                id(APP) AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(APP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'cert_belong_app'){
                UNION ALL
                MATCH
                (APP:APP)-[cert_belong_app:cert_belong_app]-(CERT:CERT)
                WHERE
                id(APP) == ${ng.valueFmt(condition.str)}
                RETURN
                "CERT" AS fromType,
                CERT.CERT.black_list AS fromBlackList,
                CERT.CERT.cert_id AS fromAddr,
                CERT.CERT.cert_id AS fromLabel,
                "APP" AS toType,
                0 AS toBlackList,
                id(APP) AS toAddr,
                id(APP) AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(APP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'domain_belong_app'){
                UNION ALL
                MATCH
                (APP:APP)-[domain_belong_app:domain_belong_app]-(DOMAIN:DOMAIN)
                WHERE
                id(APP) == ${ng.valueFmt(condition.str)}
                RETURN
                "DOMAIN" AS fromType,
                DOMAIN.DOMAIN.black_list AS fromBlackList,
                DOMAIN.DOMAIN.domain_addr AS fromAddr,
                DOMAIN.DOMAIN.domain_addr AS fromLabel,
                "APP" AS toType,
                0 AS toBlackList,
                id(APP) AS toAddr,
                id(APP) AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(APP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'ip_belong_app'){
                UNION ALL
                MATCH
                (APP:APP)-[ip_belong_app:ip_belong_app]-(IP:IP)
                WHERE
                id(APP) == ${ng.valueFmt(condition.str)}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "APP" AS toType,
                0 AS toBlackList,
                id(APP) AS toAddr,
                id(APP) AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(APP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'sslfinger_belong_app'){
                UNION ALL
                MATCH
                (APP:APP)-[sslfinger_belong_app:sslfinger_belong_app]-(SSLFINGER:SSLFINGER)
                WHERE
                id(APP) == ${ng.valueFmt(condition.str)}
                RETURN
                "SSLFINGER" AS fromType,
                0 AS fromBlackList,
                SSLFINGER.SSLFINGER.finger_id AS fromAddr,
                SSLFINGER.SSLFINGER.finger_id AS fromLabel,
                "APP" AS toType,
                0 AS toBlackList,
                id(APP) AS toAddr,
                id(APP) AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(APP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
        @}
    </select>

    <select id="getApp" resultType="com.geeksec.graph.vo.AppVo">
        MATCH (APP:APP)
        WHERE id(APP) == ${ng.valueFmt(appId)}
        RETURN
        APP.APP.app_name AS appName,
        APP.APP.app_version AS appVersion
    </select>

</mapper>