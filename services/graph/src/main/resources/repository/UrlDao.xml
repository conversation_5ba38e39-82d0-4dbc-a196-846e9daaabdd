<mapper namespace="com.geeksec.graph.repository.UrlDao">

    <select id="listUrlAllEdgeTypeAssociationNext" resultType="com.geeksec.graph.vo.VertexEdgeNextVo">
        MATCH (URL:URL)
        WHERE id(URL) == ${ng.valueFmt(condition.str)}
        RETURN DISTINCT
        "URL" AS fromType,
        URL.URL.black_list AS fromBlackList,
        URL.URL.url_key AS fromAddr,
        URL.URL.url_key AS fromLabel,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        "" AS toLabel,
        true as sourceStatus,
        false as directionStatus,
        null AS vInfo,
        "" AS teage
        LIMIT 1

        @for( edgeItem in condition.edgeInfo ) {
            @if (edgeItem.edge == 'cert_url_related'){
                UNION ALL
                MATCH
                (URL:URL)-[cert_url_related:cert_url_related]-(CERT:CERT)
                WHERE
                id(URL) == ${ng.valueFmt(condition.str)}
                RETURN
                "CERT" AS fromType,
                CERT.CERT.black_list AS fromBlackList,
                CERT.CERT.cert_id AS fromAddr,
                CERT.CERT.cert_id AS fromLabel,
                "URL" AS toType,
                URL.URL.black_list AS toBlackList,
                URL.URL.url_key AS toAddr,
                URL.URL.url_key AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(URL) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'domain_url_related'){
                UNION ALL
                MATCH
                (URL:URL)-[domain_url_related:domain_url_related]-(DOMAIN:DOMAIN)
                WHERE
                id(URL) == ${ng.valueFmt(condition.str)}
                RETURN
                "DOMAIN" AS fromType,
                DOMAIN.DOMAIN.black_list AS fromBlackList,
                DOMAIN.DOMAIN.domain_addr AS fromAddr,
                DOMAIN.DOMAIN.domain_addr AS fromLabel,
                "URL" AS toType,
                URL.URL.black_list AS toBlackList,
                URL.URL.url_key AS toAddr,
                URL.URL.url_key AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(URL) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'ip_url_related'){
                UNION ALL
                MATCH
                (URL:URL)-[ip_url_related:ip_url_related]-(IP:IP)
                WHERE
                id(URL) == ${ng.valueFmt(condition.str)}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "URL" AS toType,
                URL.URL.black_list AS toBlackList,
                URL.URL.url_key AS toAddr,
                URL.URL.url_key AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(URL) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
        @}
    </select>

</mapper>