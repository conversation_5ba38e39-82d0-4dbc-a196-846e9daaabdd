<mapper namespace="com.geeksec.graph.repository.SslFingerDao">

    <select id="listFingerDescribeByFingers" resultType="com.geeksec.graph.vo.SslFingerDescVo">
        MATCH (S:SSLFINGER)
        WHERE id(S) IN ${fingers}
        RETURN S.SSLFINGER.finger_id AS fingerId,
        S.SSLFINGER.type AS fingerType,
        S.SSLFINGER.finger_desc AS fingerDesc
    </select>

    <select id="listFingerLabelByFingers" resultType="com.geeksec.graph.vo.SslFingerLabelVo">
        MATCH (F:SSLFINGER)-[h:has_label]->(L:LABEL)
        WHERE id(F) IN ${fingers}
        RETURN F.SSLFINGER.finger_id AS fingerId,
        F.SSLFINGER.type AS fingerType,
        L.LABEL.label_id AS labelId,
        L.LABEL.label_name AS labelName
    </select>

    <select id="listServerRelatedIpsByFingers" resultType="com.geeksec.graph.vo.SslFingerRelatedIpsVo">
        GO FROM ${ ng.join(fingers,",","ng.valueFmt") }
        OVER server_use_sslfinger REVERSELY
        YIELD properties($^).finger_id AS finger, properties($$).ip_addr AS ip
        | GROUP BY $-.finger
        YIELD $-.finger AS fingerId, collect($-.ip) AS ipList
    </select>

    <select id="listClientRelatedIpsByFingers" resultType="com.geeksec.graph.vo.SslFingerRelatedIpsVo">
        GO FROM ${ ng.join(fingers,",","ng.valueFmt") }
        OVER client_use_sslfinger REVERSELY
        YIELD properties($^).finger_id AS finger, properties($$).ip_addr AS ip
        | GROUP BY $-.finger
        YIELD $-.finger AS fingerId, collect($-.ip) AS ipList
    </select>

    <select id="countCertByFingers" resultType="com.geeksec.graph.vo.SslFingerCountVo">
        MATCH (L:SSLFINGER)-[E:sslfinger_connect_cert]->(C:CERT)
        WHERE id(L) IN ${fingers}
        RETURN L.SSLFINGER.finger_id AS fingerId, count(E) AS count
    </select>

    <select id="listSslFingerAllEdgeTypeAssociationNext" resultType="com.geeksec.graph.vo.VertexEdgeNextVo">
        MATCH (SSLFINGER:SSLFINGER)
        WHERE id(SSLFINGER) == ${ng.valueFmt(condition.str)}
        RETURN DISTINCT
        "SSLFINGER" AS fromType,
        0 AS fromBlackList,
        SSLFINGER.SSLFINGER.finger_id AS fromAddr,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        true as sourceStatus,
        false as directionStatus,
        null AS vInfo,
        "" AS teage
        LIMIT 1

        @for( edgeItem in condition.edgeInfo ) {
            @if (edgeItem.edge == 'sslfinger_connect_domain'){
                UNION ALL
                MATCH
                (SSLFINGER:SSLFINGER)-[sslfinger_connect_domain:sslfinger_connect_domain]->(DOMAIN:DOMAIN)
                WHERE
                id(SSLFINGER) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND DOMAIN.DOMAIN.black_list >= ${edgeItem.weightLimit[0]} AND DOMAIN.DOMAIN.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "SSLFINGER" AS fromType,
                0 AS fromBlackList,
                SSLFINGER.SSLFINGER.finger_id AS fromAddr,
                "DOMAIN" AS toType,
                DOMAIN.DOMAIN.black_list AS toBlackList,
                DOMAIN.DOMAIN.domain_addr AS toAddr,
                false as sourceStatus,
                false as directionStatus,
                properties(DOMAIN) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'sslfinger_connect_cert'){
                UNION ALL
                MATCH
                (SSLFINGER:SSLFINGER)-[sslfinger_connect_cert:sslfinger_connect_cert]->(CERT:CERT)
                WHERE
                id(SSLFINGER) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND CERT.CERT.black_list >= ${edgeItem.weightLimit[0]} AND CERT.CERT.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "SSLFINGER" AS fromType,
                0 AS fromBlackList,
                SSLFINGER.SSLFINGER.finger_id AS fromAddr,
                "CERT" AS toType,
                CERT.CERT.black_list AS toBlackList,
                CERT.CERT.cert_id AS toAddr,
                false as sourceStatus,
                false as directionStatus,
                properties(CERT) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'client_use_sslfinger'){
                UNION ALL
                MATCH
                (SSLFINGER:SSLFINGER)-[client_use_sslfinger:client_use_sslfinger]-(IP:IP)
                WHERE
                id(SSLFINGER) == ${ng.valueFmt(condition.str)}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                "SSLFINGER" AS toType,
                0 AS toBlackList,
                SSLFINGER.SSLFINGER.finger_id AS toAddr,
                false as sourceStatus,
                true as directionStatus,
                properties(IP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'server_use_sslfinger'){
                UNION ALL
                MATCH
                (SSLFINGER:SSLFINGER)-[server_use_sslfinger:server_use_sslfinger]-(IP:IP)
                WHERE
                id(SSLFINGER) == ${ng.valueFmt(condition.str)}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                "SSLFINGER" AS toType,
                0 AS toBlackList,
                SSLFINGER.SSLFINGER.finger_id AS toAddr,
                false as sourceStatus,
                true as directionStatus,
                properties(IP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'sslfinger_belong_app'){
                UNION ALL
                MATCH
                (SSLFINGER:SSLFINGER)-[sslfinger_belong_app:sslfinger_belong_app]->(APP:APP)
                WHERE
                id(SSLFINGER) == ${ng.valueFmt(condition.str)}
                RETURN
                "SSLFINGER" AS fromType,
                0 AS fromBlackList,
                SSLFINGER.SSLFINGER.finger_id AS fromAddr,
                "APP" AS toType,
                0 AS toBlackList,
                id(APP) AS toAddr,
                false as sourceStatus,
                false as directionStatus,
                properties(APP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
        @}
    </select>

</mapper>