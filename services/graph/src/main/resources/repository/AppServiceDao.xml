<mapper namespace="com.geeksec.graph.repository.AppServiceDao">

    <select id="listAppServiceAllEdgeTypeAssociationNext" resultType="com.geeksec.graph.vo.VertexEdgeNextVo">
        MATCH (APPSERVICE:APPSERVICE)
        WHERE id(APPSERVICE) == ${ng.valueFmt(condition.str)}
        RETURN DISTINCT
        "APPSERVICE" AS fromType,
        0 AS fromBlackList,
        APPSERVICE.APPSERVICE.service_key AS fromAddr,
        APPSERVICE.APPSERVICE.AppName AS fromLabel,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        "" AS toLabel,
        true as sourceStatus,
        false as directionStatus,
        null AS vInfo,
        "" AS teage
        LIMIT 1

        @for( edgeItem in condition.edgeInfo ) {
            @if (edgeItem.edge == 'app_server'){
                UNION ALL
                MATCH
                (APPSERVICE:APPSERVICE)-[app_server:app_server]->(IP:IP)
                WHERE
                id(APPSERVICE) == ${ng.valueFmt(condition.str)}
                RETURN
                "APPSERVICE" AS fromType,
                0 AS fromBlackList,
                APPSERVICE.APPSERVICE.service_key AS fromAddr,
                APPSERVICE.APPSERVICE.AppName AS fromLabel,
                "IP" AS toType,
                IP.IP.black_list AS toBlackList,
                IP.IP.ip_addr AS toAddr,
                IP.IP.ip_addr AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(IP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'client_app'){
                UNION ALL
                MATCH
                (APPSERVICE:APPSERVICE)-[client_app:client_app]-(IP:IP)
                WHERE
                id(APPSERVICE) == ${ng.valueFmt(condition.str)}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "APPSERVICE" AS toType,
                0 AS toBlackList,
                APPSERVICE.APPSERVICE.service_key AS toAddr,
                APPSERVICE.APPSERVICE.AppName AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(IP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'special_business_port_service'){
                UNION ALL
                MATCH
                (APPSERVICE:APPSERVICE)-[special_business_port_service:special_business_port_service]-(CERT:CERT)
                WHERE
                id(APPSERVICE) == ${ng.valueFmt(condition.str)}
                RETURN
                "CERT" AS fromType,
                CERT.CERT.black_list AS fromBlackList,
                CERT.CERT.cert_id AS fromAddr,
                CERT.CERT.cert_id AS fromLabel,
                "APPSERVICE" AS toType,
                0 AS toBlackList,
                APPSERVICE.APPSERVICE.service_key AS toAddr,
                APPSERVICE.APPSERVICE.AppName AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(CERT) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
        @}
    </select>

    <select id="getAppService" resultType="com.geeksec.graph.vo.AppServiceVo">
        MATCH (APPSERVICE:APPSERVICE)
        WHERE id(APPSERVICE) == ${ng.valueFmt(serviceKey)}
        RETURN
        APPSERVICE.APPSERVICE.service_key AS serviceKey,
        APPSERVICE.APPSERVICE.ip_addr AS ipAddr,
        APPSERVICE.APPSERVICE.AppName AS AppName,
        APPSERVICE.APPSERVICE.dPort AS dPort,
        APPSERVICE.APPSERVICE.IPPro AS IPPro
    </select>

</mapper>