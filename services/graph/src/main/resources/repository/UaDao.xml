<mapper namespace="com.geeksec.graph.repository.UaDao">

    <select id="listUaAllEdgeTypeAssociationNext" resultType="com.geeksec.graph.vo.VertexEdgeNextVo">
        MATCH (UA:UA)
        WHERE id(UA) == ${ng.valueFmt(condition.str)}
        RETURN DISTINCT
        "UA" AS fromType,
        0 AS fromBlackList,
        UA.UA.ua_id AS fromAddr,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        true as sourceStatus,
        false as directionStatus,
        null AS vInfo,
        "" AS teage
        LIMIT 1

        @for( edgeItem in condition.edgeInfo ) {
            @if (edgeItem.edge == 'ua_connect_domain'){
                UNION ALL
                MATCH
                (UA:UA)-[ua_connect_domain:ua_connect_domain]->(DOMAIN:DOMAIN)
                WHERE
                id(UA) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND DOMAIN.DOMAIN.black_list >= ${edgeItem.weightLimit[0]} AND DOMAIN.DOMAIN.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "UA" AS fromType,
                0 AS fromBlackList,
                UA.UA.ua_id AS fromAddr,
                "DOMAIN" AS toType,
                DOMAIN.DOMAIN.black_list AS toBlackList,
                DOMAIN.DOMAIN.domain_addr AS toAddr,
                false as sourceStatus,
                false as directionStatus,
                properties(DOMAIN) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'ua_belong_app'){
                UNION ALL
                MATCH
                (UA:UA)-[ua_belong_app:ua_belong_app]->(APP:APP)
                WHERE
                id(UA) == ${ng.valueFmt(condition.str)}
                RETURN
                "UA" AS fromType,
                0 AS fromBlackList,
                UA.UA.ua_id AS fromAddr,
                "APP" AS toType,
                0 AS toBlackList,
                id(APP) AS toAddr,
                false as sourceStatus,
                false as directionStatus,
                properties(APP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'ua_belong_os'){
                UNION ALL
                MATCH
                (UA:UA)-[ua_belong_os:ua_belong_os]->(OS:OS)
                WHERE
                id(UA) == ${ng.valueFmt(condition.str)}
                RETURN
                "UA" AS fromType,
                0 AS fromBlackList,
                UA.UA.ua_id AS fromAddr,
                "OS" AS toType,
                0 AS toBlackList,
                OS.OS.os_name AS toAddr,
                false as sourceStatus,
                false as directionStatus,
                properties(OS) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'ua_belong_device'){
                UNION ALL
                MATCH
                (UA:UA)-[ua_belong_device:ua_belong_device]->(DEVICE:DEVICE)
                WHERE
                id(UA) == ${ng.valueFmt(condition.str)}
                RETURN
                "UA" AS fromType,
                0 AS fromBlackList,
                UA.UA.ua_id AS fromAddr,
                "DEVICE" AS toType,
                0 AS toBlackList,
                DEVICE.DEVICE.device_name AS toAddr,
                false as sourceStatus,
                false as directionStatus,
                properties(DEVICE) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'client_use_ua'){
                UNION ALL
                MATCH
                (UA:UA)-[client_use_ua:client_use_ua]-(IP:IP)
                WHERE
                id(UA) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND IP.IP.black_list >= ${edgeItem.weightLimit[0]} AND IP.IP.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                "UA" AS toType,
                0 AS toBlackList,
                UA.UA.ua_id AS toAddr,
                false as sourceStatus,
                true as directionStatus,
                properties(IP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
        @}
    </select>

</mapper>