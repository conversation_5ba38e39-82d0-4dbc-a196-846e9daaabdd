# Graph Service 测试环境配置
# 继承通用测试环境配置

spring:
  profiles:
    active: test
    include: common

  # Graph服务测试环境数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:test-db.nta.local}:${DB_PORT:5432}/${DB_NAME:nta_graph_test}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:nta_graph_test}
    password: ${DB_PASSWORD:nta_graph_test123}

# 服务器配置
server:
  port: ${SERVER_PORT:8087}
  servlet:
    context-path: /graph

# Nebula Graph 测试环境配置
nebula:
  ngbatis:
    use-session-pool: true
  # 测试环境 Nebula Graph 连接配置
  hosts: ${NEBULA_HOSTS:test-nebula.nta.local:9669}
  username: ${NEBULA_USERNAME:nta_test}
  password: ${NEBULA_PASSWORD:nta_test123}
  space: ${NEBULA_SPACE:gs_analysis_graph_test}
  
  # 测试环境连接池配置（中等规模）
  pool-config:
    min-conns-size: 2
    max-conns-size: 10
    timeout: 3000
    idle-time: 0
    interval-idle: -1
    wait-time: 120
    min-cluster-health-rate: 1.0
    enable-ssl: false
    max_sessions_per_ip_per_user: 500

# Graph服务特定配置
graph:
  # 图数据处理配置
  processing:
    batch-size: 500  # 测试环境中等批次
    max-depth: 6  # 图遍历最大深度
    timeout: 60000  # 60秒超时
  
  # 缓存配置
  cache:
    enable: true
    ttl: 600  # 10分钟缓存
    max-size: 5000
  
  # 测试环境数据路径
  data:
    import-path: ${GRAPH_IMPORT_PATH:/data/nta-test/graph}
    export-path: ${GRAPH_EXPORT_PATH:/data/nta-test/export}

# CQL配置
cql:
  parser:
    mapperLocations: classpath*:repository/*.xml

# 日志配置
logging:
  level:
    com.geeksec.graph: INFO
    org.springframework.web: INFO
    com.vesoft.nebula: WARN
