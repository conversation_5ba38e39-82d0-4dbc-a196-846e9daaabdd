# Graph Service 生产环境配置
# 继承通用生产环境配置

spring:
  profiles:
    active: prod
    include: common

  # Graph服务生产环境数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:prod-db.nta.local}:${DB_PORT:5432}/${DB_NAME:nta_graph_prod}?useUnicode=true&characterEncoding=utf8&useSSL=true&sslmode=require&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:nta_graph_prod}
    password: ${DB_PASSWORD}

# 服务器配置
server:
  port: ${SERVER_PORT:8087}
  servlet:
    context-path: /graph

# Nebula Graph 生产环境配置
nebula:
  ngbatis:
    use-session-pool: true
  # 生产环境 Nebula Graph 连接配置（集群模式）
  hosts: ${NEBULA_HOSTS:prod-nebula-1.nta.local:9669,prod-nebula-2.nta.local:9669,prod-nebula-3.nta.local:9669}
  username: ${NEBULA_USERNAME:nta_prod}
  password: ${NEBULA_PASSWORD}
  space: ${NEBULA_SPACE:gs_analysis_graph_prod}
  
  # 生产环境连接池配置（大规模）
  pool-config:
    min-conns-size: 10
    max-conns-size: 50
    timeout: 5000
    idle-time: 0
    interval-idle: -1
    wait-time: 300
    min-cluster-health-rate: 0.8  # 允许20%节点不可用
    enable-ssl: ${NEBULA_SSL:true}
    max_sessions_per_ip_per_user: 2000

# Graph服务特定配置
graph:
  # 图数据处理配置
  processing:
    batch-size: 2000  # 生产环境大批次
    max-depth: 8  # 图遍历最大深度
    timeout: 300000  # 5分钟超时
  
  # 缓存配置
  cache:
    enable: true
    ttl: 1800  # 30分钟缓存
    max-size: 50000
  
  # 生产环境数据路径
  data:
    import-path: ${GRAPH_IMPORT_PATH:/data/nta-prod/graph}
    export-path: ${GRAPH_EXPORT_PATH:/data/nta-prod/export}
    backup-path: ${GRAPH_BACKUP_PATH:/backup/nta-prod/graph}
  
  # 生产环境性能配置
  performance:
    enable-query-cache: true
    enable-result-cache: true
    max-query-time: 300000  # 最大查询时间5分钟
    enable-slow-query-log: true
    slow-query-threshold: 10000  # 慢查询阈值10秒

# CQL配置
cql:
  parser:
    mapperLocations: classpath*:repository/*.xml

# 日志配置
logging:
  level:
    com.geeksec.graph: INFO
    org.springframework.web: WARN
    com.vesoft.nebula: WARN
