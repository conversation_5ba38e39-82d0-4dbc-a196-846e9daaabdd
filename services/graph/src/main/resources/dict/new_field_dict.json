[{"label": "会话", "value": "connectinfo", "children": [{"value": "sIp", "label": "源IP", "type": "IP"}, {"value": "sPort", "label": "源端口", "type": "int"}, {"value": "dIp", "label": "目的IP", "type": "IP"}, {"value": "dPort", "label": "目的端口", "type": "int"}, {"value": "IPPro", "label": "IP协议号", "type": "int"}, {"value": "SessionId", "label": "会话ID", "type": "string"}, {"value": "<PERSON><PERSON>", "label": "存储标记", "type": "string"}, {"value": "AppName", "label": "应用", "type": "string"}, {"value": "ProName", "label": "网络协议名", "type": "string"}, {"value": "ThreadId", "label": "处理线程号", "type": "int"}, {"value": "Duration", "label": "持续秒数", "type": "int"}, {"value": "StartTime", "label": "起始时间", "type": "int"}, {"value": "EndTime", "label": "结束时间", "type": "int"}, {"value": "CreateTime", "label": "入库时间", "type": "int"}, {"value": "FirstProto", "label": "首层协议ID", "type": "int"}, {"value": "ProListNum", "label": "协议层数", "type": "int"}, {"value": "SbytesDiviDbytes", "label": "源目的包数差异", "type": "float"}, {"value": "TotalBytes", "label": "总字节数", "type": "int"}, {"value": "TotalPacketNum", "label": "总包数", "type": "int"}, {"value": "sMac", "label": "源MAC", "type": "string"}, {"value": "s<PERSON><PERSON><PERSON>", "label": "源SSL指纹", "type": "string"}, {"value": "sIpCity", "label": "源IP所在城市", "type": "string"}, {"value": "sIpCountry", "label": "源IP所在国家", "type": "string"}, {"value": "sIpSubdivisions", "label": "源IP所在省份", "type": "string"}, {"value": "sMinHopCount", "label": "源端发送TTL最小距离", "type": "int"}, {"value": "sMaxHopCount", "label": "源端发送TTL最大距离", "type": "int"}, {"value": "sInitialTTL", "label": "源端发送原始TTL", "type": "int"}, {"value": "dMac", "label": "目的MAC", "type": "string"}, {"value": "d<PERSON><PERSON><PERSON>", "label": "目的SSL指纹", "type": "string"}, {"value": "dIpCity", "label": "目的IP所在城市", "type": "string"}, {"value": "dIpCountry", "label": "目的IP所在国家", "type": "string"}, {"value": "dIpSubdivisions", "label": "目的IP所在省份", "type": "int"}, {"value": "dTTLMax", "label": "目的端发送最大TTL", "type": "int"}, {"value": "dTTLMin", "label": "目的端发送最小TTL", "type": "int"}, {"value": "dInitialTTL", "label": "目的端发送原始TTL", "type": "int"}, {"value": "ip_port_ipport_appid", "label": "目的IP-端口-IP协议号-应用ID标识", "type": "string"}, {"value": "mac2mac", "label": "MAC对", "type": "string"}, {"value": "ip2ip", "label": "IP对", "type": "string"}, {"value": "sip_appid_dport_dip", "label": "源IP-应用ID-目的端口-目的IP标识", "type": "string"}, {"value": "PktNumApp", "label": "识别应用包数", "type": "int"}, {"value": "pkt", "label": "包信息", "children": [{"value": "pkt.sPayloadNum", "label": "源端发送负载包数", "type": "int"}, {"value": "pkt.sPayloadBytes", "label": "源端发送负载字节数", "type": "int"}, {"value": "pkt.dPayloadNum", "label": "目的端发送负载包数", "type": "int"}, {"value": "pkt.dPayloadBytes", "label": "目的端发送负载字节数", "type": "int"}]}, {"value": "HTTP", "label": "HTTP信息", "children": [{"value": "HTTP.Url", "label": "请求URL", "type": "string"}, {"value": "HTTP.Act", "label": "请求方式", "type": "int"}, {"value": "HTTP.Host", "label": "目的端发送负载字节数", "type": "int"}, {"value": "HTTP.User-Agent", "label": "客户端信息", "type": "int"}, {"value": "HTTP.Response", "label": "回应类型", "type": "string"}]}, {"value": "DNS", "label": "DNS信息", "children": [{"value": "DNS.Domain", "label": "本次询问域名", "type": "string"}, {"value": "DNS.DomainIp", "label": "本次答复的IP地址", "type": "string"}]}, {"value": "SSL", "label": "SSL信息", "children": [{"value": "SSL.CH_Ciphersuit", "label": "ClientHello密码套件", "type": "string"}, {"value": "SSL.CH_CiphersuitNum", "label": "ClientHello密码套件数量", "type": "int"}, {"value": "SSL.CH_ServerName", "label": "服务器名", "type": "string"}, {"value": "SSL.CH_ALPN", "label": "应用协议类型", "type": "string"}, {"value": "SSL.sCertHash", "label": "客户端证书Hash列表", "type": "string"}, {"value": "SSL.dCertHash", "label": "服务端证书Hash", "type": "string"}]}]}, {"label": "DNS元数据", "value": "DNS", "children": [{"value": "sIp", "label": "源IP", "type": "IP"}, {"value": "sPort", "label": "源端口", "type": "int"}, {"value": "dIp", "label": "目的IP", "type": "IP"}, {"value": "dPort", "label": "目的端口", "type": "int"}, {"value": "SessionId", "label": "会话ID", "type": "string"}, {"value": "AppName", "label": "应用", "type": "string"}, {"value": "TaskId", "label": "任务ID", "type": "int"}, {"value": "StartTime", "label": "起始时间(秒)", "type": "int"}, {"value": "StartNSec", "label": "起始时间(纳秒)", "type": "int"}, {"value": "CreateTime", "label": "入库时间", "type": "int"}, {"value": "Flags", "label": "DNS标志位", "type": "int"}, {"value": "Que", "label": "询问信息数量", "type": "int"}, {"value": "<PERSON>s", "label": "回答信息数量", "type": "int"}, {"value": "<PERSON><PERSON>", "label": "认证信息数量", "type": "int"}, {"value": "Add", "label": "附加信息数量", "type": "int"}, {"value": "Domain", "label": "域名", "type": "String"}, {"value": "DomainIp", "label": "答复地址", "type": "数组"}, {"value": "<PERSON><PERSON>", "label": "存储标记", "type": "string"}, {"value": "Query", "label": "询问信息", "children": [{"value": "Query.name", "label": "名称", "type": "string"}, {"value": "Query.type", "label": "类型", "type": "int"}, {"value": "Query.class", "label": "登记", "type": "int"}]}, {"value": "Answer", "label": "回答信息", "children": [{"value": "Answer.name", "label": "名称", "type": "string"}, {"value": "Answer.type", "label": "类型", "type": "int"}, {"value": "Answer.class", "label": "登记", "type": "int"}, {"value": "Answer.ttl", "label": "有效期", "type": "int"}, {"value": "Answer.type", "label": "类型", "type": "int"}, {"value": "Answer.data_len", "label": "字节数", "type": "int"}, {"value": "Answer.value", "label": "回应值", "type": "string"}]}]}, {"label": "HTTP元数据", "value": "HTTP", "children": [{"value": "sIp", "label": "源IP", "type": "IP"}, {"value": "sPort", "label": "源端口", "type": "int"}, {"value": "dIp", "label": "目的IP", "type": "IP"}, {"value": "dPort", "label": "目的端口", "type": "int"}, {"value": "ServerIP", "label": "服务端IP", "type": "string"}, {"value": "SessionId", "label": "会话ID", "type": "string"}, {"value": "TaskId", "label": "任务ID", "type": "int"}, {"value": "BatchNum", "label": "批次ID", "type": "int"}, {"value": "StartTime", "label": "起始时间", "type": "int"}, {"value": "StartNSec", "label": "起始时间_纳秒", "type": "int"}, {"value": "CreateTime", "label": "入库时间", "type": "string"}, {"value": "Url", "label": "网址", "type": "string"}, {"value": "Act", "label": "请求类型", "type": "string"}, {"value": "Host", "label": "服务端信息", "type": "string"}, {"value": "Response", "label": "回应类型", "type": "string"}, {"value": "sHTTPFinger", "label": "源端HTTP指纹", "type": "string"}, {"value": "dHTTPFinger", "label": "目的HTTP指纹", "type": "string"}, {"value": "<PERSON><PERSON>", "label": "存储标记", "type": "string"}, {"value": "Client", "label": "客户端信息", "children": [{"value": "Client.Accept", "label": "支持页面类型", "type": "string"}, {"value": "Client.Host", "label": "站点信息", "type": "string"}, {"value": "Client.User-Agent", "label": "UA信息", "type": "string"}, {"value": "Client.Title", "label": "标头列表", "type": "string"}, {"value": "Client.Payload", "label": "内容", "type": "string"}, {"value": "Client.<PERSON><PERSON>", "label": "cookie内容", "type": "string"}, {"value": "Client.Content-Type", "label": "内容类型", "type": "string"}]}, {"label": "服务端信息", "value": "Server", "children": [{"value": "Server.Accept-Ranges", "label": "允许断点续传", "type": "string"}, {"value": "Server.Content-Encoding", "label": "cookie内容", "type": "string"}, {"value": "Server.Content-Length", "label": "字节数", "type": "string"}, {"value": "Server.Content-Type", "label": "内容类型", "type": "string"}, {"value": "Server.Title", "label": "标头列表", "type": "string"}]}]}, {"label": "SSL元数据", "value": "SSL", "children": [{"value": "sIp", "label": "源IP", "type": "IP"}, {"value": "sPort", "label": "源端口", "type": "int"}, {"value": "dIp", "label": "目的IP", "type": "IP"}, {"value": "dPort", "label": "目的端口", "type": "int"}, {"value": "SessionId", "label": "会话ID", "type": "string"}, {"value": "AppName", "label": "应用名", "type": "string"}, {"value": "TaskId", "label": "任务ID", "type": "int"}, {"value": "BatchNum", "label": "批次ID", "type": "int"}, {"value": "StartTime", "label": "起始时间", "type": "int"}, {"value": "StartNSec", "label": "起始时间_纳秒", "type": "int"}, {"value": "CreateTime", "label": "入库时间", "type": "string"}, {"value": "cSSLVersion", "label": "客户端版本号", "type": "int"}, {"value": "CH_Version", "label": "ClientHello版本", "type": "int"}, {"value": "CH_Time", "label": "ClientHello时间戳", "type": "int"}, {"value": "CH_SessionIDLen", "label": "ClientHello会话ID字节数", "type": "int"}, {"value": "CH_Ciphersuit", "label": "ClientHello密码套件", "type": "string"}, {"value": "CH_CiphersuitNum", "label": "ClientHello密码套件数量", "type": "int"}, {"value": "CH_CompressionMethod", "label": "ClientHello压缩方法", "type": "string"}, {"value": "CH_CompressionMethodLen", "label": "ClientHello压缩方法数量", "type": "int"}, {"value": "CH_ExtentionNum", "label": "ClientHello扩展信息数量", "type": "int"}, {"value": "CH_ServerName", "label": "服务器名", "type": "string"}, {"value": "CH_ServerNameType", "label": "服务器名类型", "type": "int"}, {"value": "CH_SessionTicket", "label": "增强性会话ID", "type": "string"}, {"value": "CH_ALPN", "label": "应用协议类型", "type": "json"}, {"value": "sCertHash", "label": "客户端证书Hash列表", "type": "string"}, {"value": "sCertNum", "label": "客户端证书数量", "type": "int"}, {"value": "sKeyExchange", "label": "客户端密钥交换值", "type": "string"}, {"value": "sKeyExchangeLen", "label": "客户端密钥交换值字节数", "type": "int"}, {"value": "s<PERSON><PERSON><PERSON>", "label": "客户端指纹", "type": "string"}, {"value": "sSSLVersion", "label": "服务端版本号", "type": "int"}, {"value": "SH_Version", "label": "ServerHello版本", "type": "int"}, {"value": "SH_Time", "label": "ServerHello时间戳", "type": "int"}, {"value": "SH_SessionIdLen", "label": "ServerHello会话ID字节数", "type": "int"}, {"value": "SH_Cipersuite", "label": "ServerHello密码套件", "type": "string"}, {"value": "SH_CompressionMethod", "label": "ServerHello压缩方法", "type": "string"}, {"value": "SH_SessionTicket", "label": "增强性会话ID确认", "type": "string"}, {"value": "SH_ALPN", "label": "确认应用协议类型", "type": "json"}, {"value": "dCertHash", "label": "服务端证书Hash", "type": "数组"}, {"value": "dCertHashStr", "label": "服务端证书Hash字符串格式", "type": "string"}, {"value": "dCertNum", "label": "服务端证书数量", "type": "int"}, {"value": "dKeyExchange", "label": "服务端密钥交互值", "type": "string"}, {"value": "dKeyExchangelen", "label": "服务端密钥交互数据字节数", "type": "int"}, {"value": "dNewSessionTicket_LifeTime", "label": "新增强型会话ID有效周期", "type": "int"}, {"value": "dNewSessionTicket_Ticket", "label": "新增强型会话ID", "type": "string"}, {"value": "dNewSessionTicket_TicketLen", "label": "新增强型会话ID字节数", "type": "int"}, {"value": "d<PERSON><PERSON><PERSON>", "label": "服务端指纹", "type": "string"}, {"value": "<PERSON><PERSON>", "label": "存储标记", "type": "string"}]}]