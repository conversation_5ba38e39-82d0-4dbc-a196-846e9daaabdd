{"ASN1MD5": "MD5", "ASN1SHA1": "SHA1", "CN": "使用者通用名", "CertID": "证书ID", "Duration": "有效周期_秒", "ImportTime": "导入时间", "FatherCertID": "父证书SHA1", "NotAfter": "有效期终止", "NotBefore": "有效期起始", "Usage": "密钥用法", "Version": "版本", "PemMD5": "PEM格式MD5", "PemSHA1": "PEM格式SHA1", "PublicKey": "公钥", "PublicKeyAlgorithm": "公钥算法", "PublicKeyAlgorithmLength": "公钥算法密钥长度", "SAN": "扩展域名", "SerialNumber": "序列号", "SignatureAlgorithm": "签名算法", "Extension": "扩展信息", "Extension.authorityInfoAccess": "- 授权信息访问", "Extension.authorityKeyIdentifier": "- 授权密钥标识符", "Extension.basicConstraints": "- 基本约束", "Extension.certificatePolicies": "- 证书策略", "Extension.crlDistributionPoints": "- CRL分发点", "Extension.extendedKeyUsage": "- 增强型密钥用法", "Extension.keyUsage": "- 密钥用法", "Extension.subjectAltName": "- 使用者可选名称", "Extension.subjectKeyIdentifier": "- 使用者密钥标识符", "Issuer": "颁发者", "Issuer.C": "-编辑国家", "Issuer.CN": "- 通用名", "Issuer.O": "- 组织名", "Issuer.OU": "- 组织部门", "Issuer.L": "- 地址", "Issuer.ST": "- 省份", "IssuerMD5": "颁发者哈希", "Subject": "使用者", "Subject.C": "- 国家", "Subject.CN": "- 通用名", "Subject.L": "- 地址", "Subject.O": "- 组织名", "Subject.OU": "- 组织部门", "Subject.ST": "- 省份", "SubjectMD5": "使用者哈希"}