package com.geeksec.graph.repository;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.pojo.vertex.AppVertex;
import com.geeksec.graph.vo.AppVo;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AppDao extends NebulaDaoBasic<AppVertex, String> {

    /**
     * 查询APP所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listAppAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

    AppVo getApp(@Param("appId") String appId);

}
