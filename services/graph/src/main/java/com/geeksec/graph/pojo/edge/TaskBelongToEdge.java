package com.geeksec.graph.pojo.edge;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Table;

/**
*@description: 任务所属
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "task_belong_to")
@Data
public class TaskBelongToEdge {

  /**
   * 任务id
   */
  @Column(name = "task_id")
  private Long taskId;

  /**
   * 任务名称
   */
  @Column(name = "task_name")
  private String taskName;

}
