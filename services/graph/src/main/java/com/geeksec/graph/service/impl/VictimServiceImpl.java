package com.geeksec.graph.service.impl;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.repository.VictimDao;
import com.geeksec.graph.service.VictimService;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: Victim服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class VictimServiceImpl implements VictimService {

    @Autowired
    private VictimDao victimDao;

    /**
     * Victim关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getVictimNebulaAssociationNext(GraphNextInfoCondition condition) {
        return victimDao.listVictimAllEdgeTypeAssociationNext(condition);
    }
}
