package com.geeksec.graph.pojo.edge;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Table;
import java.sql.Timestamp;

/**
*@description: 源IP关联MAC（IP->MAC）
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "src_bind")
@Data
public class SrcBindEdge {

  /**
   * ip
   */
  private String ip;

  /**
   * mac
   */
  private String mac;

  /**
   * 首次出现时间
   */
  @Column(name = "first_time")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_time")
  private Timestamp lastTime;

  /**
   * 出现次数
   */
  @Column(name = "session_cnt")
  private Long sessionCnt;

  /**
   * 接收的流量大小
   */
  private Long bytes;

  /**
   * 接收的包数
   */
  private Long packets;

}
