package com.geeksec.graph.service.impl;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.condition.GraphPropertiesNextCondition;
import com.geeksec.graph.repository.IssuerDao;
import com.geeksec.graph.service.IssuerService;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: 签发机构服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class IssuerServiceImpl implements IssuerService {

    @Autowired
    private IssuerDao issuerDao;

    /**
     * 签发机构关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getIssuerNebulaAssociationNext(GraphNextInfoCondition condition) {
        return issuerDao.listIssuerAllEdgeTypeAssociationNext(condition);
    }

    /**
     * 签发机构属性关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> listIssuerNebulaNextByProperties(GraphPropertiesNextCondition condition) {
        return issuerDao.listIssuerNebulaNextByProperties(condition);
    }
}
