package com.geeksec.graph.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: qiuwen
 * @date: 2022/8/8
 * @Description:
 **/
@Data
public class SubGraphNextCondition {
    /**
     * 目标ID列表
     */
    @JsonProperty("vid_list")
    private List<String> vidList;

    /**
     * 最大步数
     */
    @JsonProperty("step_count")
    private Integer  stepCount;


}
