package com.geeksec.graph.pojo.vertex;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.sql.Timestamp;

/**
*@description: MAC地址信息
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "MAC")
@Data
public class MacVertex {

  /**
  * mac地址
  */
  @Id
  private String mac;

  /**
  * 出现的连接数
  */
  private Long times;

  /**
  * 收发流量大小
  */
  private Long bytes;

  /**
  * 平均流量
  */
  @Column(name = "average_bps")
  private Long averageBps;

  /**
  * Vlan信息
  */
  @Column(name = "vlan_info")
  private String vlanInfo;

  /**
  * 首次出现时间
  */
  @Column(name = "first_seen")
  private Timestamp firstTime;

  /**
  * 末次出现时间
  */
  @Column(name = "last_seen")
  private Timestamp lastTime;

  /**
  * 黑名单权值
  */
  @Column(name = "black_list")
  private Integer blackList;

  /**
  * 白名单权值
  */
  @Column(name = "white_list")
  private Integer whiteList;

  /**
  * 备注
  */
  private String remark;

}
