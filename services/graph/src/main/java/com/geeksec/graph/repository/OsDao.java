package com.geeksec.graph.repository;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.pojo.vertex.OsVertex;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface OsDao extends NebulaDaoBasic<OsVertex, String> {

    /**
     * 查询os所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listOsAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

}
