package com.geeksec.graph;

import org.nebula.contrib.ngbatis.config.NgbatisConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;

/**
 * 图谱服务应用启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.geeksec.graph", "org.nebula.contrib.ngbatis"})
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.geeksec")
@ComponentScan(basePackages = {"com.geeksec.graph", "com.geeksec.common", "org.nebula.contrib.ngbatis"})
@Import(NgbatisConfig.class)
public class GraphServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(GraphServiceApplication.class, args);
    }
}
