package com.geeksec.graph.service.impl;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.repository.OrgDao;
import com.geeksec.graph.service.OrgService;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: Org服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class OrgServiceImpl implements OrgService {

    @Autowired
    private OrgDao orgDao;

    /**
     * Org关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getOrgNebulaAssociationNext(GraphNextInfoCondition condition) {
        return orgDao.listOrgAllEdgeTypeAssociationNext(condition);
    }
}
