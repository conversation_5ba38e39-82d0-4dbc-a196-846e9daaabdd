package com.geeksec.graph.service;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.vo.VertexEdgeNextVo;

import java.util.List;

/**
*@description: AppService服务
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
public interface AppServiceService {

    /**
     * AppService关联查询Next
     */
    List<VertexEdgeNextVo> getAppServiceNebulaAssociationNext(GraphNextInfoCondition condition);

}
