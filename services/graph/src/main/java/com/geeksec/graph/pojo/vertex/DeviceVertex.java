package com.geeksec.graph.pojo.vertex;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.sql.Timestamp;

/**
*@description: 硬件类型
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "DEVICE")
@Data
public class DeviceVertex {

  /**
  * 设备名称
  */
  @Id
  @Column(name = "device_name")
  private String deviceName;

  /**
   * 首次出现时间
   */
  @Column(name = "first_seen")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_seen")
  private Timestamp lastTime;

}
