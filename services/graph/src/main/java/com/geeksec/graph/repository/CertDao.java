package com.geeksec.graph.repository;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.condition.GraphPropertiesNextCondition;
import com.geeksec.graph.pojo.vertex.CertVertex;
import com.geeksec.graph.vo.*;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

public interface CertDao extends NebulaDaoBasic<CertVertex, String> {

    /**
     * 查询证书所有边类型关联数据
     */
    List<VertexEdgeVo> listCertAllEdgeTypeAssociation(@Param("cert") String cert);

    /**
     * 查询证书所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listCertAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

    /**
     * 证书属性关联查询Next
     */
    List<VertexEdgeNextVo> listCertNebulaNextByProperties(@Param("condition") GraphPropertiesNextCondition condition);

    /**
     * 根据ids查询存在证书属性
     */
    List<ExistCertPropertiesVo> listExistCertProperties(@Param("certs") List<String> certs);

    /**
     * 根据ids查询证书详情
     */
    List<CertVo> listByCerts(@Param("certs") List<String> certs);

    /**
     * 根据ids查询客户端热度
     */
    List<CertRelatedIpsVo> listRelatedIpsByCerts(@Param("certs") List<String> certs);

    /**
     * 根据ids查询关联域名个数
     */
    List<CertCountVo> countDomainNumByCerts(@Param("certs") List<String> certs);

    /**
     * 根据ids查询关联服务器ip个数
     */
    List<CertCountVo> countServerIpNumByCerts(@Param("certs") List<String> certs);

    /**
     * 根据ids查询关联SSL个数
     */
    List<CertCountVo> countSslIpNumByCerts(@Param("certs") List<String> certs);

    /**
     * 查询证书详情
     */
    Map<String, Object> getCertInfo(@Param("cert") String cert);

    /**
     * 根据id查询标签
     */
    List<Long> listHasLabelByCert(@Param("cert") String cert);

    /**
     * 根据id查询服务端热度
     */
    List<String> listRelatedServerIpsByCert(@Param("cert") String cert);

    /**
     * 根据id查询客户端热度
     */
    List<String> listRelatedClientIpsByCert(@Param("cert") String cert);

    /**
     * 根据id查询任务id列表
     */
    List<String> getTaskIds(@Param("cert") String cert);

    /**
     * 根据ids查询客户端热度
     */
    List<CertRelatedIpVo> listRelatedIpsByCert(@Param("certId") String certId);

}
