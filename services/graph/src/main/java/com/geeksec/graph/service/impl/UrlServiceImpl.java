package com.geeksec.graph.service.impl;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.repository.UrlDao;
import com.geeksec.graph.service.UrlService;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: Url服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class UrlServiceImpl implements UrlService {

    @Autowired
    private UrlDao urlDao;

    /**
     * Url关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getUrlNebulaAssociationNext(GraphNextInfoCondition condition) {
        return urlDao.listUrlAllEdgeTypeAssociationNext(condition);
    }
}
