package com.geeksec.graph.pojo.edge;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Table;
import java.sql.Timestamp;

/**
*@description: 指纹访问域名（SSL指纹->域名）
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "sslfinger_connect_domain")
@Data
public class SslfingerConnectDomainEdge {

  /**
   * 首次出现时间
   */
  @Column(name = "first_time")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_time")
  private Timestamp lastTime;

  /**
   * 出现次数
   */
  @Column(name = "session_cnt")
  private Long sessionCnt;

}
