package com.geeksec.graph.service.impl;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.repository.AppServiceDao;
import com.geeksec.graph.service.AppServiceService;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: AppService服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class AppServiceServiceImpl implements AppServiceService {

    @Autowired
    private AppServiceDao appServiceDao;

    /**
     * AppService关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getAppServiceNebulaAssociationNext(GraphNextInfoCondition condition) {
        return appServiceDao.listAppServiceAllEdgeTypeAssociationNext(condition);
    }
}
