package com.geeksec.graph.service.impl;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.condition.GraphPropertiesNextCondition;
import com.geeksec.graph.repository.SubjectDao;
import com.geeksec.graph.service.SubjectService;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: 所有者服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class SubjectServiceImpl implements SubjectService {

    @Autowired
    private SubjectDao SubjectDao;

    /**
     * 所有者关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getSubjectNebulaAssociationNext(GraphNextInfoCondition condition) {
        return SubjectDao.listSubjectAllEdgeTypeAssociationNext(condition);
    }

    /**
     * 所有者属性关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> listSubjectNebulaNextByProperties(GraphPropertiesNextCondition condition) {
        return SubjectDao.listSubjectNebulaNextByProperties(condition);
    }

}
