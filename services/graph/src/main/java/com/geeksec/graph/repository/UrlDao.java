package com.geeksec.graph.repository;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.pojo.vertex.UrlVertex;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface UrlDao extends NebulaDaoBasic<UrlVertex, String> {

    /**
     * 查询url所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listUrlAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

}
