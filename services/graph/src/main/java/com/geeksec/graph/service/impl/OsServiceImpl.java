package com.geeksec.graph.service.impl;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.repository.OsDao;
import com.geeksec.graph.service.OsService;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: Os服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class OsServiceImpl implements OsService {

    @Autowired
    private OsDao osDao;

    /**
     * Os关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getOsNebulaAssociationNext(GraphNextInfoCondition condition) {
        return osDao.listOsAllEdgeTypeAssociationNext(condition);
    }
}
