package com.geeksec.graph.repository;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.pojo.vertex.MacVertex;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface MacDao extends NebulaDaoBasic<MacVertex, String> {

    /**
     * 查询Mac所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listMacAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

}
