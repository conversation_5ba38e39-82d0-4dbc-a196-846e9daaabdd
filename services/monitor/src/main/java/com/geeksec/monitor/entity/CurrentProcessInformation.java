package com.geeksec.monitor.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CurrentProcessInformation {

    /**
     * 名称
     */
    private Object name;
    
    /**
     * 进程开始时间
     */
    private Object processStartTime;

    /**
     * CPU占用时间
     */
    private Object processCpuUsageTime;

    /**
     * 内存占用
     */
    private Object memoryUsage;

    /**
     * 进程名称
     */
    private Object processName;
    
}
