package com.geeksec.monitor.controller;

import com.geeksec.monitor.context.SpringContextHolder;
import com.geeksec.monitor.schedulingTask.MonitorTask;
import com.geeksec.monitor.utils.WebsocketUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;

/**
 * 系统监控websocket接口处理类
 */
@Slf4j
@Component
@ServerEndpoint(value = "/monitor/websocket/{userId}")
public class MonitorWebSocketController {

    private MonitorTask monitorTask;

    public MonitorWebSocketController() {
        this.monitorTask = SpringContextHolder.getBean(MonitorTask.class);
    }

    /**
     * 连接
     * @param userId
     * @param session
     */
    @OnOpen
    public void onOpen(@PathParam(value = "userId") String userId, Session session) {
        // 添加到session的映射关系中
        log.info("用户：{}，连接成功", userId);
        WebsocketUtil.addSession(userId, session);
        //连接成功立刻发送一次
        monitorTask.sendSystemInfoToAllUsers();
    }

    /**
     * 断开
     *
     * @param userId
     * @param session
     */
    @OnClose
    public void onClose(@PathParam(value = "userId") String userId, Session session) {
        // 删除映射关系
        log.info("用户：{}，断开连接", userId);
        WebsocketUtil.removeSession(userId);
    }

    /**
     * 接收消息
     *
     * @param userId
     * @param session
     */
    @OnMessage
    public void onMessage(@PathParam(value = "userId") String userId, Session session, String message) {
        log.info("接收到用户: {} 的消息：{}", userId, message);
    }

    /**
     * 连接异常
     *
     * @param session
     * @param throwable
     */
    @OnError
    public void onError(Session session, Throwable throwable) {
        try {
            session.close();
            log.info("连接异常，关闭连接");
        } catch (IOException e) {
            e.printStackTrace();
        }
        throwable.printStackTrace();
    }

}
