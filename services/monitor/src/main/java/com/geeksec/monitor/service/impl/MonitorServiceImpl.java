package com.geeksec.monitor.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.geeksec.monitor.kafka.Listener;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.HardwareAbstractionLayer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.monitor.entity.CurrentProcessInformation;
import com.geeksec.monitor.entity.IndexData;
import com.geeksec.monitor.enums.IndexEnum;
import com.geeksec.monitor.service.MonitorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.net.URLEncoder;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
@RequiredArgsConstructor
public class MonitorServiceImpl implements MonitorService {


    private static String URL;
    @Value("${spring.prometheus.url}")
    private String url;
    private static final String API = "/api/v1/query";
    private static final SystemInfo SYSTEM_INFO = new SystemInfo();
    final RedissonClient redis;
    private static final String thd10RedisKey = Listener.TOPIC.concat(":").concat(String.valueOf(Listener.TYPE)).concat(":").concat(String.valueOf(Listener.TASK_ID_0));
    private static final String thd11RedisKey = Listener.TOPIC.concat(":").concat(String.valueOf(Listener.TYPE)).concat(":").concat(String.valueOf(Listener.TASK_ID_1));
    private final ObjectMapper objectMapper = new ObjectMapper();

    @PostConstruct
    public void initUrl() {
        URL = url;
    }


    /**
     * 获取系统信息
     * @return
     */
    public IndexData getSystemInfo() {
        IndexData indexData = new IndexData();
        Map<String, List<IndexEnum>> groupProcessEnums = groupProcessEnums(4);//当前进程信息枚举
        List<IndexEnum> groupEnums = groupProcessEnums.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
        List<IndexEnum> systemEnums = Arrays.stream(IndexEnum.values())
                .filter(e -> !groupEnums.contains(e)) // 系统信息枚举
                .collect(Collectors.toList());
        for (IndexEnum value : systemEnums) {
            try {
                if (value == IndexEnum.PROCESSOR) {//处理器名称用java获取
                    indexData.setMetric(value.getIndex(),getCPUName());
                }else {
                    String result = executePromQL(URLEncoder.encode(value.getPromQL(), "UTF-8"));
                    parseResult(result,value,indexData);
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        //单独计算一下CPU负载平均值
        indexData.setMetric(IndexEnum.AVERAGE_CPU_LOAD.getIndex(),(Double.parseDouble(indexData.getAverageCpuLoad().toString().replace("\"",""))
                /Double.parseDouble(indexData.getNumberOfProcessorCores().toString().replace("\"",""))) * 100);
        List<CurrentProcessInformation> list = new LinkedList<>();
        groupProcessEnums.forEach((k, v) -> {
            CurrentProcessInformation information = new CurrentProcessInformation();
            if (k.equals("THD10")) {
                information.setName("探针主任务");
            }else if (k.equals("THD11")) {
                information.setName("探针从任务");
            }else {
                information.setName(k.toLowerCase());
            }
            if (k.equals("THD10") || k.equals("THD11")) {//探针指标从redis获取
                setThd1Index(information);
            }else {
                for (IndexEnum indexEnum : v) {
                    try {
                        String result = executePromQL(URLEncoder.encode(indexEnum.getPromQL(), "UTF-8"));
                        parseResultGroup(result,indexEnum,information);
                    } catch (IOException e) {
                        log.error("获取系统信息失败：{}", e.getMessage());
                        throw new RuntimeException(e);
                    }
                }
            }
            list.add(information);
        });
        indexData.setCurrentProcessInformationList(list);
        log.info("获取系统信息成功：{}", System.currentTimeMillis());
        //log.info("获取系统信息：{}", indexData);
        return indexData;

    }

    /**
     * 获取探针指标
     * @param information
     */
    private void setThd1Index(CurrentProcessInformation information) {
        RBucket<Object> bucket = null;
        if (information.getName().toString().contains("主")) {
            bucket = redis.getBucket(thd10RedisKey);
        }
        if (information.getName().toString().contains("从")){
            bucket = redis.getBucket(thd11RedisKey);
        }
        if (bucket.isExists()) {
            try {
                JsonNode jsonNode = objectMapper.readTree(bucket.get().toString());
                information.setProcessStartTime(jsonNode.get("begintime").asLong());
                information.setProcessName(information.getName());
                information.setProcessCpuUsageTime(jsonNode.get("cpu_times"));
                information.setMemoryUsage(jsonNode.get("meminfo"));
            } catch (JsonProcessingException e) {
                log.error("获取系统信息失败,redis获取数据异常：{}", e.getMessage());
                throw new RuntimeException(e);
            }
        }else {
            information.setProcessStartTime(0);
            information.setProcessName(information.getName());
            information.setProcessCpuUsageTime(0);
            information.setMemoryUsage(0);
        }
    }

    /**
     * 对当前指标枚举值分组，并筛选出当前进程信息枚举
     * @param targetCount
     * @return
     */
    public static Map<String, List<IndexEnum>> groupProcessEnums(int targetCount) {
        return Arrays.stream(IndexEnum.values())
                .collect(Collectors.groupingBy(enumConstant -> {
                    String name = enumConstant.name();
                    int firstUnderscoreIndex = name.indexOf('_');
                    if (firstUnderscoreIndex > 0) {
                        return name.substring(0, firstUnderscoreIndex);
                    } else {
                        return "UNKNOWN";
                    }
                }, LinkedHashMap::new, Collectors.toList()))
                .entrySet().stream()
                .filter(entry -> entry.getValue().size() == targetCount)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));
    }

    /**
     * 执行PromQL
     * @param promQL
     * @return
     * @throws IOException
     */
    private static String executePromQL(String promQL) throws IOException {
        /*try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            log.info("访问地址为：{}",URL.concat(API));
            HttpGet request = new HttpGet( URL.concat(API).concat("?query=").concat(promQL));
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    return EntityUtils.toString(entity);
                }
            }catch (IOException e){
                log.error("prometheus连接超时或promQL错误：{}", e.getMessage());
            }
        }*/
        try {
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[]{new X509TrustManager() {
                public void checkClientTrusted(X509Certificate[] chain, String authType) {}
                public void checkServerTrusted(X509Certificate[] chain, String authType) {}
                public X509Certificate[] getAcceptedIssuers() { return new X509Certificate[0]; }
            }}, new SecureRandom());

            try (CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(sslContext)
                    .setSSLHostnameVerifier((hostname, session) -> true)
                    .build()) {

                HttpGet request = new HttpGet(URL.concat(API).concat("?query=").concat(promQL));
                try (CloseableHttpResponse response = httpClient.execute(request)) {
                    HttpEntity entity = response.getEntity();
                    if (entity != null) {
                        return EntityUtils.toString(entity);
                    }
                }
            }
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    /**
     * 解析prometheus结果
     * @param result
     * @param indexEnum
     * @param indexData
     * @throws IOException
     */
    private static void parseResult(String result,IndexEnum indexEnum,IndexData indexData) throws IOException {
        if (result != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(result);
            if (rootNode.path("status").asText().equals("success")) {
                JsonNode dataNode = rootNode.path("data").path("result");
                if (dataNode.isEmpty()){
                    indexData.setMetric(indexEnum.getIndex(),null);
                }else {
                    indexData.setMetric(indexEnum.getIndex(),dataNode.get(0).path("value").get(1));
                }
            } else {
                indexData.setMetric(indexEnum.getIndex(),null);
            }
        }
    }

    /**
     * 解析当前进程prometheus结果
     * @param result
     * @param indexEnum
     * @param information
     * @throws IOException
     */
    private static void parseResultGroup(String result,IndexEnum indexEnum,CurrentProcessInformation information) throws IOException {
        if (result != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(result);
            if (rootNode.path("status").asText().equals("success")) {
                JsonNode dataNode = rootNode.path("data").path("result");
                if (dataNode.isEmpty()){
                    fillDataGroup(indexEnum, information, null);
                }else {
                    JsonNode data = dataNode.get(0).path("value").get(1);
                    fillDataGroup(indexEnum, information, data);
                }
            } else {
                fillDataGroup(indexEnum, information, null);
            }
        }
    }

    /**
     * 近5天新增本地磁盘空间
     * @param dataNode
     * @return
     */
    private static Map<String, String> getAdded(JsonNode dataNode) {
        Map<String, String> added = null;
        if (dataNode != null && dataNode.get(0) != null) {
            JsonNode values = dataNode.get(0).path("values");
            added = IntStream.range(0, values.size())
                    .mapToObj(values::get)
                    .filter(arrayNode -> arrayNode.isArray() && arrayNode.size() >= 2)
                    .collect(Collectors.toMap(
                            arrayNode -> fromUnixTime(arrayNode.get(0).asLong(),  "yyyy-MM-dd"),
                            arrayNode -> {
                                double bytes = Double.parseDouble(arrayNode.get(1).asText());
                                return String.valueOf(Math.round((bytes / (1024.0 * 1024.0 * 1024.0)) * 100) / 100.0);
                            }
                    )).entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (oldValue, newValue) -> oldValue,
                            LinkedHashMap::new
                    ));
            //如果数据不足，则补全
            if (added.size() < 5) {
                added = completeAdded(added);
            }
        }else {
            added = new LinkedHashMap<>();
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            for (int i = -4; i <= 0; i++) {
                calendar.add(Calendar.DAY_OF_YEAR, i);
                String dateStr = sdf.format(calendar.getTime());
                added.put(dateStr, "0");
                calendar = Calendar.getInstance();
            }
        }
        return added;
    }

    /**
     * 补全近期新增本地磁盘空间
     * @param added
     * @return
     */
    private static Map<String, String> completeAdded(Map<String, String> added) {
        Map<String, String> fullMap = new LinkedHashMap<>();
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        for (int i = 0; i < 5; i++) {
            String dateStr = now.minusDays(4 - i).format(formatter); // 从5天前到今天
            fullMap.put(dateStr, added.getOrDefault(dateStr, "0"));
        }
        added = fullMap;
        return added;
    }

    /**
     * 获取CPU型号
     * @return
     */
    private static String getCPUName(){
        HardwareAbstractionLayer hardware = SYSTEM_INFO.getHardware();
        CentralProcessor processor = hardware.getProcessor();
        return processor.getProcessorIdentifier().getName();
    }

    /**
     * 将Unix时间戳转换为指定格式的日期时间字符串
     * @param unixTimestamp Unix时间戳（秒）
     * @param pattern 日期格式模式
     * @return 格式化后的日期时间字符串
     */
    public static String fromUnixTime(long unixTimestamp, String pattern) {
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        format.setTimeZone(TimeZone.getDefault());
        return format.format(new Date((unixTimestamp * 1000)));
    }

    /**
     * 填充当前进程信息
     * @param indexEnum
     * @param information
     * @param data
     */
    private static void fillDataGroup(IndexEnum indexEnum, CurrentProcessInformation information, JsonNode data) {
        if (indexEnum.getIndex().contains("process_start_time")) {
            information.setProcessStartTime(data == null ? null : data.asLong());
        }else if (indexEnum.getIndex().contains("process_name")){
            information.setProcessName(indexEnum.getIndex().split("_")[0]);
        }else if (indexEnum.getIndex().contains("cpu_usage_time")){
            information.setProcessCpuUsageTime(data);
        }else if (indexEnum.getIndex().contains("memory_usage")){
            information.setMemoryUsage(data);
        }
    }

    /**
     * 将字符串转换为LocalDateTime
     * @param dateTimeStr
     * @return
     */
    public static LocalDateTime stringToLocalDateTime(String dateTimeStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return LocalDateTime.parse(dateTimeStr, formatter);
    }

    /**
     * 计算两个 LocalDateTime 时间之间的秒数差
     * @param startTime
     * @param endTime
     * @return
     */
    public static long calculateSecondsBetween(LocalDateTime startTime, LocalDateTime endTime) {
        Duration duration = Duration.between(startTime, endTime);
        return duration.getSeconds();
    }

}