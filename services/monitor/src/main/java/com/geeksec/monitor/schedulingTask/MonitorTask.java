package com.geeksec.monitor.schedulingTask;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.monitor.entity.IndexData;
import com.geeksec.monitor.service.MonitorService;
import com.geeksec.monitor.utils.WebsocketUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class MonitorTask {

    private final MonitorService monitorService;
    private boolean isScheduledRunning = false;

    @Scheduled(fixedRate = 10000) // 每3秒执行一次
    public void sendSystemInfoToAllUsers() {
        //if (isScheduledRunning) {
        IndexData systemInfo = monitorService.getSystemInfo();
        try {
            WebsocketUtil.sendMessageForAll(new ObjectMapper().writeValueAsString(systemInfo));
            log.info("推送系统信息成功:{}", System.currentTimeMillis());
        } catch (JsonProcessingException e) {
            log.error("Error converting system info to JSON: {}", e.getMessage());
        }
        //}
    }

    /**
     * 手动开启sendSystemInfoToAllUsers
     */
    public void triggerAndStartScheduledTask() {
        isScheduledRunning = true;
        sendSystemInfoToAllUsers(); // 手动触发一次
    }

    /**
     * 手动关闭sendSystemInfoToAllUsers
     */
    public void stopScheduledTask() {
        isScheduledRunning = false;
    }

}
