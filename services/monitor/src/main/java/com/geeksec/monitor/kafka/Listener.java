package com.geeksec.monitor.kafka;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RedissonClient;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class Listener {

    private final ObjectMapper objectMapper = new ObjectMapper();
    public static final String TOPIC = "slog";
    public static final int TYPE = 303;
    public static final int TASK_ID_0 = 0;
    public static final int TASK_ID_1 = 1;
    final RedissonClient redis;

    @KafkaListener(topics = "slog")
    public void listen(String message) {
        // 处理接收到的消息
        try {
            JsonNode jsonNode = objectMapper.readTree(message);
            if (jsonNode.get("type").asInt() == TYPE){
                if (jsonNode.get("task_id").asInt() == TASK_ID_0) {
                    redis.getBucket(TOPIC.concat(":").concat(String.valueOf(TYPE)).concat(":").concat(String.valueOf(TASK_ID_0))).set(String.valueOf(jsonNode.get("ps_info").get("探针主程序")));
                }
                if (jsonNode.get("task_id").asInt() == TASK_ID_1){
                    redis.getBucket(TOPIC.concat(":").concat(String.valueOf(TYPE)).concat(":").concat(String.valueOf(TASK_ID_1))).set(String.valueOf(jsonNode.get("ps_info").get("探针主程序")));
                }
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

}
