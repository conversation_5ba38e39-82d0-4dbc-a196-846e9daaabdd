package com.geeksec.monitor.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.websocket.RemoteEndpoint;
import jakarta.websocket.Session;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class WebsocketUtil {
  /**
   * 记录当前在线的Session
   */
  public static final Map<String, Session> ONLINE_SESSION = new ConcurrentHashMap<>();

  /**
   * 添加session
   * @param userId
   * @param session
   */
  public static void addSession(String userId, Session session){
    // 此处只允许一个用户的session链接。一个用户的多个连接，我们视为无效。
    ONLINE_SESSION.putIfAbsent ( userId, session );
  }

  /**
   * 关闭session
   * @param userId
   */
  public static void removeSession(String userId){
    ONLINE_SESSION.remove ( userId );
  }

  /**
   * 给单个用户推送消息
   * @param session
   * @param message
   */
  public static void sendMessage(Session session, String message){
      if(session == null || !session.isOpen()){
          log.info("发送消息失败，会话可能已关闭");
          return;
      }
      try {
          // 同步
          RemoteEndpoint.Async async = session.getAsyncRemote();
          async.sendText(message);
      } catch (Exception e) {
          // 捕获异常并记录日志
          log.error("发送消息失败，会话可能已关闭: " + e.getMessage());
      }
  }

  /**
   * 向所有在线人发送消息
   * @param message
   */
  public static void sendMessageForAll(String message) {
      //CompletableFuture.runAsync(() -> {
          ONLINE_SESSION.forEach((sessionId, session) -> sendMessage(session, message));
      //});
  }
}

