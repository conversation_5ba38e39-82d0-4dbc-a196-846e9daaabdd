# Monitor Service 生产环境配置
# 继承通用生产环境配置

spring:
  profiles:
    active: prod
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8086}
  servlet:
    context-path: /api/monitor

# 监控服务特定配置
monitor:
  # 生产环境数据采集配置
  collection:
    # 系统指标采集
    system:
      enabled: true
      interval: 10  # 10秒采集一次
      metrics: [cpu, memory, disk, network, io, load]
    
    # 应用指标采集
    application:
      enabled: true
      interval: 5  # 5秒采集一次
      metrics: [jvm, gc, threads, connections, cache, pools]
    
    # 业务指标采集
    business:
      enabled: true
      interval: 15  # 15秒采集一次
      metrics: [requests, errors, latency, throughput, users]
  
  # 生产环境存储配置
  storage:
    # 时序数据库配置（集群模式）
    timeseries:
      type: influxdb
      url: ${INFLUXDB_URL:http://prod-influxdb-1.nta.local:8086,http://prod-influxdb-2.nta.local:8086}
      database: ${INFLUXDB_DB:nta_monitor_prod}
      username: ${INFLUXDB_USER:nta_prod}
      password: ${INFLUXDB_PASSWORD}
      ssl-enabled: true
    
    # 数据保留策略
    retention:
      raw-data-days: 90  # 原始数据保留90天
      aggregated-data-days: 365  # 聚合数据保留1年
      archive-data-years: 3  # 归档数据保留3年
  
  # 生产环境告警配置
  alerting:
    enabled: true
    # 告警规则
    rules:
      - name: high-cpu
        condition: cpu_usage > 70
        duration: 120  # 持续2分钟
        severity: warning
      - name: high-memory
        condition: memory_usage > 75
        duration: 120
        severity: warning
      - name: critical-cpu
        condition: cpu_usage > 85
        duration: 60  # 持续1分钟
        severity: critical
      - name: critical-memory
        condition: memory_usage > 90
        duration: 60
        severity: critical
      - name: disk-full
        condition: disk_usage > 85
        duration: 300  # 持续5分钟
        severity: critical
    
    # 通知配置
    notifications:
      email:
        enabled: true
        smtp-host: ${SMTP_HOST:prod-smtp.nta.local}
        recipients: [<EMAIL>, <EMAIL>]
        ssl-enabled: true
      sms:
        enabled: true
        provider: ${SMS_PROVIDER:aliyun}
        recipients: [${OPS_PHONE_1}, ${OPS_PHONE_2}]
      webhook:
        enabled: true
        url: ${ALERT_WEBHOOK_URL:https://prod-webhook.nta.local/alerts}
        timeout: 5000
        retry-count: 3
  
  # 生产环境仪表板配置
  dashboard:
    enabled: true
    refresh-interval: 10  # 10秒刷新
    default-time-range: 24h  # 默认显示24小时数据
    enable-real-time: true
  
  # 生产环境性能配置
  performance:
    enable-metrics-aggregation: true
    aggregation-interval: 60  # 1分钟聚合一次
    enable-data-compression: true
    enable-parallel-processing: true
    max-concurrent-collectors: 10
  
  # 生产环境安全配置
  security:
    enable-metrics-encryption: true
    enable-access-control: true
    enable-audit-log: true
  
  # 生产环境高可用配置
  ha:
    enable-failover: true
    backup-storage-url: ${BACKUP_INFLUXDB_URL}
    health-check-interval: 30

# 日志配置
logging:
  level:
    '[com.geeksec.monitor]': INFO
