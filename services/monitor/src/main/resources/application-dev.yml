# Monitor Service 开发环境配置
# 继承通用开发环境配置

spring:
  profiles:
    active: dev
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8086}
  servlet:
    context-path: /api/monitor

# 监控服务特定配置
monitor:
  # 开发环境数据采集配置
  collection:
    # 系统指标采集
    system:
      enabled: true
      interval: 30  # 30秒采集一次
      metrics: [cpu, memory, disk, network]
    
    # 应用指标采集
    application:
      enabled: true
      interval: 15  # 15秒采集一次
      metrics: [jvm, gc, threads, connections]
    
    # 业务指标采集
    business:
      enabled: true
      interval: 60  # 60秒采集一次
      metrics: [requests, errors, latency]
  
  # 开发环境存储配置
  storage:
    # 时序数据库配置
    timeseries:
      type: influxdb  # 或 prometheus
      url: ${INFLUXDB_URL:http://localhost:8086}
      database: ${INFLUXDB_DB:nta_monitor_dev}
      username: ${INFLUXDB_USER:admin}
      password: ${INFLUXDB_PASSWORD:admin}
    
    # 数据保留策略
    retention:
      raw-data-days: 7  # 原始数据保留7天
      aggregated-data-days: 30  # 聚合数据保留30天
  
  # 开发环境告警配置
  alerting:
    enabled: true
    # 告警规则
    rules:
      - name: high-cpu
        condition: cpu_usage > 80
        duration: 300  # 持续5分钟
        severity: warning
      - name: high-memory
        condition: memory_usage > 85
        duration: 300
        severity: warning
    
    # 通知配置
    notifications:
      webhook:
        enabled: true
        url: ${ALERT_WEBHOOK_URL:http://localhost:3000/alerts}
  
  # 开发环境仪表板配置
  dashboard:
    enabled: true
    refresh-interval: 30  # 30秒刷新
    default-time-range: 1h  # 默认显示1小时数据

# 日志配置
logging:
  level:
    '[com.geeksec.monitor]': DEBUG
