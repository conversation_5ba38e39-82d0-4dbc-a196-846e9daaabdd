-- 插入测试检测规则数据
INSERT INTO detection_rule_configs (
    task_id, rule_id, rule_level, rule_name, rule_desc, rule_state, rule_type,
    capture_mode, rule_json, rule_hash, attack_stage, traffic_rate_limit_bps,
    traffic_retention_limit_bytes, retain_metadata, retain_pcap, lib_respond_open,
    lib_respond_lib, lib_respond_config, lib_respond_session_end, lib_respond_pkt_num,
    created_time, updated_time
) VALUES
(1001, 10001, 1, 'HTTP恶意请求检测', '检测HTTP请求中的恶意特征', '生效', 0,
 1, '{"base_rule":{"rule_id":10001}}', 'hash001', 'RECONNAISSANCE', 1048576,
 104857600, true, true, 0, '', '', 0, 0,
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

(1001, 10002, 2, 'DNS隧道检测', '检测DNS隧道攻击', '生效', 0,
 1, '{"base_rule":{"rule_id":10002}}', 'hash002', 'COMMAND_AND_CONTROL', 2097152,
 209715200, true, false, 0, '', '', 0, 0,
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

(1002, 10003, 1, 'FTP暴力破解检测', '检测FTP暴力破解攻击', '失效', 1,
 1, '{"base_rule":{"rule_id":10003}}', 'hash003', 'EXPLOITATION', 524288,
 52428800, false, true, 0, '', '', 0, 0,
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 插入测试过滤规则数据
INSERT INTO tb_filter_config (
    task_id, ip, filter_json, created_time, updated_time, hash, type, status
) VALUES 
(1001, '***********', '{"port":[80,443,8080]}', 1640995200, 1640995200, 'filter_hash001', 0, 1),
(1001, '********', '{"pro_id":6,"ip":["10.0.0.0/8"]}', 1640995300, 1640995300, 'filter_hash002', 1, 1),
(1002, '**********', '{"network":["**********/16"]}', 1640995400, 1640995400, 'filter_hash003', 2, 1);

-- 插入测试过滤模式数据
INSERT INTO tb_filter_state (task_id, state) VALUES 
(1001, 0),
(1002, 1);
