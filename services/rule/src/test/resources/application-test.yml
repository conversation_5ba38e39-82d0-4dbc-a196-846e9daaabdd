spring:
  application:
    name: rule-service-test
  
  # 测试数据源配置
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
  
  # H2 控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # Redis 配置（使用嵌入式Redis或禁用）
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: 6379
      database: 1
      timeout: 3000ms

# MyBatis-Flex 配置
mybatis-flex:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.geeksec.rule.domain.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    lazy-loading-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 日志配置
logging:
  level:
    com.geeksec.rule: DEBUG
    com.mybatisflex: DEBUG
    org.springframework.test: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 规则管理业务配置
rule:
  detection:
    max-rule-id: 200000
    default-capture-mode: 1
    csv-template-file: detection_template.csv
    lib-zip-file: LibFolder.zip
  
  filter:
    csv-template-file: filter_rule_template.csv
    max-port-value: 65535
    max-ip-segments: 100
