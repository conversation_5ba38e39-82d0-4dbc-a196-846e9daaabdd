package com.geeksec.rule.domain.utils;

import com.geeksec.common.enums.CyberKillChain;
import com.geeksec.rule.domain.constants.RuleConstants;
import com.geeksec.rule.domain.enums.FeatureRuleEnum;
import com.geeksec.rule.domain.enums.FilterCriteriaEnum;
import com.geeksec.rule.domain.enums.RuleStateEnum;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 规则工具类
 * 使用 JDK 17 新特性优化代码
 * 
 * <AUTHOR>
 */
public final class RuleUtils {

    private RuleUtils() {
        // 工具类，禁止实例化
    }

    private static final Pattern IP_PATTERN = Pattern.compile(
            "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
    );

    private static final Pattern DOMAIN_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?(\\.([a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?))*$"
    );

    /**
     * 生成规则hash值
     * 
     * @param ruleContent 规则内容
     * @return hash值
     */
    public static String generateRuleHash(String ruleContent) {
        if (ruleContent == null || ruleContent.isBlank()) {
            return "";
        }
        
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            byte[] hash = digest.digest(ruleContent.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5算法不可用", e);
        }
    }

    /**
     * 验证IP地址格式
     * 
     * @param ip IP地址
     * @return 是否有效
     */
    public static boolean isValidIp(String ip) {
        return ip != null && IP_PATTERN.matcher(ip).matches();
    }

    /**
     * 验证域名格式
     * 
     * @param domain 域名
     * @return 是否有效
     */
    public static boolean isValidDomain(String domain) {
        return domain != null && DOMAIN_PATTERN.matcher(domain).matches();
    }

    /**
     * 验证端口号
     * 
     * @param port 端口号
     * @return 是否有效
     */
    public static boolean isValidPort(Integer port) {
        return port != null && port >= 0 && port <= RuleConstants.MAX_PORT_VALUE;
    }

    /**
     * 验证端口列表
     * 
     * @param ports 端口列表
     * @return 是否有效
     */
    public static boolean isValidPorts(List<Integer> ports) {
        return ports != null && ports.stream().allMatch(RuleUtils::isValidPort);
    }

    /**
     * 获取当前时间戳（秒）
     * 
     * @return 时间戳
     */
    public static int getCurrentTimestamp() {
        return (int) Instant.now().getEpochSecond();
    }

    /**
     * 布尔值转整数
     * 
     * @param value 布尔值
     * @return 整数值
     */
    public static Integer booleanToInteger(Boolean value) {
        return switch (value) {
            case null -> RuleConstants.BOOLEAN_FALSE;
            case true -> RuleConstants.BOOLEAN_TRUE;
            case false -> RuleConstants.BOOLEAN_FALSE;
        };
    }

    /**
     * 整数转布尔值
     * 
     * @param value 整数值
     * @return 布尔值
     */
    public static Boolean integerToBoolean(Integer value) {
        return switch (value) {
            case null -> false;
            case 1 -> true;
            default -> false;
        };
    }

    /**
     * 验证规则状态
     * 
     * @param state 状态
     * @return 是否有效
     */
    public static boolean isValidRuleState(String state) {
        return RuleStateEnum.isValidState(state);
    }

    /**
     * 验证过滤条件
     *
     * @param type 过滤条件类型
     * @return 是否有效
     */
    public static boolean isValidFilterCriteria(Integer type) {
        return FilterCriteriaEnum.isValidCode(type);
    }

    /**
     * 验证特征规则类型
     *
     * @param type 类型
     * @return 是否有效
     */
    public static boolean isValidFeatureRuleType(String type) {
        return FeatureRuleEnum.isValidType(type);
    }

    /**
     * 验证Cyber Kill Chain阶段
     *
     * @param stage Cyber Kill Chain阶段
     * @return 是否有效
     */
    public static boolean isValidCyberKillChain(String stage) {
        return CyberKillChain.fromValue(stage) != null;
    }

    /**
     * 构建规则类型字符串
     * 
     * @param types 类型列表
     * @return 类型字符串
     */
    public static String buildRuleTypeString(List<String> types) {
        return types != null ? String.join(RuleConstants.RULE_TYPE_SEPARATOR, types) : "";
    }

    /**
     * 解析规则类型字符串
     * 
     * @param typeString 类型字符串
     * @return 类型列表
     */
    public static List<String> parseRuleTypeString(String typeString) {
        if (typeString == null || typeString.isBlank()) {
            return List.of();
        }
        return List.of(typeString.split(RuleConstants.RULE_TYPE_SEPARATOR));
    }

    /**
     * 验证正则表达式
     * 
     * @param regex 正则表达式
     * @return 是否有效
     */
    public static boolean isValidRegex(String regex) {
        if (regex == null || regex.isBlank()) {
            return false;
        }
        
        try {
            Pattern.compile(regex);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
