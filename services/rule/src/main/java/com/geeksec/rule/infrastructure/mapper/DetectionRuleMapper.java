package com.geeksec.rule.infrastructure.mapper;

import com.geeksec.rule.domain.entity.DetectionRule;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

import static com.geeksec.rule.domain.entity.table.DetectionRuleTableDef.DETECTION_RULE;

/**
 * 检测规则Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface DetectionRuleMapper extends BaseMapper<DetectionRule> {

    /**
     * 获取当前数据库最大规则ID
     *
     * @return 最大规则ID
     */
    default Integer getMaxRuleId() {
        DetectionRule maxRule = selectOneByQuery(QueryWrapper.create()
                .orderBy(DETECTION_RULE.ID, false)
                .limit(1));
        return maxRule != null ? maxRule.getId().intValue() : 0;
    }

    /**
     * 根据hash查找规则（排除指定ID）
     *
     * @param id 排除的规则ID
     * @param hash 规则hash
     * @param taskId 任务ID
     * @return 匹配的规则
     */
    default DetectionRule selectOneByHash(Long id, String hash, Integer taskId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(DETECTION_RULE.RULE_HASH.eq(hash));

        if (id != null) {
            queryWrapper.and(DETECTION_RULE.ID.ne(id));
        }
        if (taskId != null) {
            queryWrapper.and(DETECTION_RULE.TASK_ID.eq(taskId));
        }

        return selectOneByQuery(queryWrapper);
    }

    /**
     * 批量删除特征规则（物理删除）
     *
     * @param taskId 任务ID
     * @param ids 规则ID列表
     * @return 删除数量
     */
    default Integer deleteFeatureRules(Integer taskId, List<Long> ids) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(DETECTION_RULE.TASK_ID.eq(taskId));

        if (ids != null && !ids.isEmpty()) {
            queryWrapper.and(DETECTION_RULE.ID.in(ids));
        }

        return deleteByQuery(queryWrapper);
    }

    /**
     * 分页查询检测规则
     *
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    Page<DetectionRule> selectPageWithConditions(Page<DetectionRule> page, QueryWrapper queryWrapper);

    /**
     * 根据规则ID列表获取标签信息
     *
     * @param ruleIds 规则ID列表
     * @return 标签信息列表
     */
    @Select("""
            <script>
            SELECT rule_id, rule_name, rule_desc
            FROM detection_rule_configs
            WHERE rule_id IN
            <foreach collection="ruleIds" separator="," open="(" close=")" item="ruleId">
                #{ruleId}
            </foreach>
            </script>
            """)
    List<Object> getTagInfoByRuleIds(@Param("ruleIds") List<Integer> ruleIds);

    /**
     * 批量插入检测规则
     *
     * @param rules 规则列表
     * @return 插入数量
     */
    default Integer batchInsert(List<DetectionRule> rules) {
        return insertBatch(rules);
    }

    /**
     * 更新规则状态
     *
     * @param id 规则ID
     * @param state 新状态
     * @return 更新数量
     */
    @Update("UPDATE detection_rule_configs SET rule_state = #{state}, updated_time = CURRENT_TIMESTAMP WHERE id = #{id}")
    Integer updateRuleState(@Param("id") Long id, @Param("state") String state);

    /**
     * 根据任务ID获取规则数量
     *
     * @param taskId 任务ID
     * @return 规则数量
     */
    @Select("SELECT COUNT(*) FROM detection_rule_configs WHERE task_id = #{taskId}")
    Integer countByTaskId(@Param("taskId") Integer taskId);
}
