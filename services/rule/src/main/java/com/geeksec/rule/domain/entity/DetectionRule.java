package com.geeksec.rule.domain.entity;

import com.geeksec.common.enums.CyberKillChain;
import com.geeksec.common.enums.ThreatLevelEnum;
import com.geeksec.rule.domain.enums.CaptureModeEnum;
import com.geeksec.rule.domain.enums.RuleSourceEnum;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 检测规则实体类
 * 对应 detection_rule_configs 表
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Table("detection_rule_configs")
public class DetectionRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @Id(keyType = KeyType.Auto)
    private Long id;

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 标签ID（规则命中时打上的标签）
     */
    @Column("label_id")
    private Integer labelId;

    /**
     * 威胁等级
     */
    @Column("threat_level")
    private ThreatLevelEnum threatLevel;

    /**
     * 规则名称（告警类型）
     */
    private String ruleName;

    /**
     * 规则描述
     */
    private String ruleDesc;

    /**
     * 规则状态：true-生效 false-失效
     */
    @Column("rule_enabled")
    private boolean ruleEnabled;

    /**
     * 规则来源：SYSTEM-系统内置 USER-用户自定义
     */
    @Column("rule_source")
    private RuleSourceEnum ruleSource;

    /**
     * 采集模式
     */
    private CaptureModeEnum captureMode;

    /**
     * 规则JSON配置
     */
    private String ruleJson;

    /**
     * 规则hash
     */
    private String ruleHash;

    /**
     * Cyber Kill Chain阶段，使用cyber_kill_chain_enum枚举
     */
    private CyberKillChain cyberKillChain;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 修改时间
     */
    private LocalDateTime updatedAt;

    /**
     * 流量留存限速，单位：字节/秒
     */
    private Long trafficRateLimitBps;

    /**
     * 流量留存上限，单位：字节（负数表示不限）
     */
    private Long trafficRetentionLimitBytes;

    /**
     * 是否留存流量元数据
     */
    private Boolean retainMetadata;

    /**
     * 是否留存原始流量数据（PCAP文件）
     */
    private Boolean retainPcap;

    /**
     * 是否开启动态库响应
     */
    @Column("lib_respond_enabled")
    private boolean libRespondEnabled;

    /**
     * 动态库路径
     */
    private String libRespondLib;

    /**
     * 库配置路径
     */
    private String libRespondConfig;

    /**
     * 会话结束标识
     */
    private Long libRespondSessionEnd;

    /**
     * 响应包数
     */
    private Long libRespondPktNum;
}
