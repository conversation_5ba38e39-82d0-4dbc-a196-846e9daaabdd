package com.geeksec.rule.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 域名规则DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "域名规则")
public class DomainRuleDTO {

    /**
     * 域名
     */
    @NotBlank(message = "域名不能为空")
    @JsonProperty("domain")
    @Schema(description = "域名", example = "example.com")
    private String domain;

    /**
     * 域名类型：1-精确域名 2-n级域名
     */
    @NotNull(message = "域名类型不能为空")
    @JsonProperty("type")
    @Schema(description = "域名类型", example = "1", allowableValues = {"1", "2"})
    private Integer type;
}
