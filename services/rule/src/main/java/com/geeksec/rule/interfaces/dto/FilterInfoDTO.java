package com.geeksec.rule.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 过滤规则信息DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "过滤规则信息")
public class FilterInfoDTO {

    /**
     * 端口列表
     */
    @JsonProperty("port")
    @Schema(description = "端口列表", example = "[80, 443, 8080]")
    private List<Integer> port;

    /**
     * 协议ID
     */
    @JsonProperty("pro_id")
    @Schema(description = "协议ID", example = "6")
    private Integer proId;

    /**
     * IP地址列表
     */
    @JsonProperty("ip")
    @Schema(description = "IP地址列表", example = "[\"***********\", \"********\"]")
    private List<String> ip;

    /**
     * 网段列表
     */
    @JsonProperty("network")
    @Schema(description = "网段列表", example = "[\"***********/24\", \"10.0.0.0/8\"]")
    private List<String> network;
}
