package com.geeksec.rule.infrastructure.mapper;

import com.geeksec.rule.domain.entity.FilterRule;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

import static com.geeksec.rule.domain.entity.table.FilterRuleTableDef.FILTER_RULE;

/**
 * 过滤规则Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface FilterRuleMapper extends BaseMapper<FilterRule> {

    /**
     * 根据任务ID获取过滤规则数量
     * 
     * @param taskId 任务ID
     * @return 规则数量
     */
    @Select("SELECT COUNT(*) FROM tb_filter_config WHERE task_id = #{taskId} AND status = 1")
    Integer getCountByTaskId(@Param("taskId") Integer taskId);

    /**
     * 分页查询过滤规则
     *
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    Page<FilterRule> selectPageWithConditions(Page<FilterRule> page, QueryWrapper queryWrapper);

    /**
     * 根据hash查找规则（排除指定ID）
     *
     * @param id 排除的规则ID
     * @param hash 规则hash
     * @param taskId 任务ID
     * @return 匹配的规则
     */
    @Select("""
            <script>
            SELECT id, task_id, ip, filter_json, created_at, updated_at, hash, criteria, active
            FROM filter_rule
            WHERE active = true
            <if test="id != null">
                AND id != #{id}
            </if>
            <if test="taskId != null">
                AND task_id = #{taskId}
            </if>
            <if test="hash != null and hash != ''">
                AND hash = #{hash}
            </if>
            LIMIT 1
            </script>
            """)
    FilterRule selectOneByHash(@Param("id") Long id, @Param("hash") String hash, @Param("taskId") Integer taskId);

    /**
     * 批量删除过滤规则（软删除）
     *
     * @param taskId 任务ID
     * @param ids 规则ID列表
     * @return 删除数量
     */
    @Update("""
            <script>
            UPDATE filter_rule SET active = false
            WHERE task_id = #{taskId}
            <if test="ids != null and ids.size() > 0">
                AND id IN
                <foreach collection="ids" separator="," open="(" close=")" item="id">
                    #{id}
                </foreach>
            </if>
            </script>
            """)
    Integer deleteFilterRules(@Param("taskId") Integer taskId, @Param("ids") List<Long> ids);

    /**
     * 批量插入过滤规则
     *
     * @param rules 规则列表
     * @return 插入数量
     */
    default Integer batchInsert(List<FilterRule> rules) {
        return insertBatch(rules);
    }

    /**
     * 根据任务ID获取所有过滤规则（用于导出）
     *
     * @param taskId 任务ID
     * @return 规则列表
     */
    @Select("SELECT id, task_id, ip, filter_json, created_at, updated_at, hash, criteria, active " +
            "FROM filter_rule WHERE task_id = #{taskId} AND active = true ORDER BY created_at DESC")
    List<FilterRule> selectAllByTaskId(@Param("taskId") Integer taskId);
}
