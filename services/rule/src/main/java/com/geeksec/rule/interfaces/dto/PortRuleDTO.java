package com.geeksec.rule.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 端口规则DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "端口规则")
public class PortRuleDTO {

    /**
     * 源端口列表
     */
    @JsonProperty("src_port")
    @Schema(description = "源端口列表", example = "[80, 443, 8080]")
    private List<Integer> srcPort;

    /**
     * 目标端口列表
     */
    @JsonProperty("dst_port")
    @Schema(description = "目标端口列表", example = "[80, 443, 8080]")
    private List<Integer> dstPort;
}
