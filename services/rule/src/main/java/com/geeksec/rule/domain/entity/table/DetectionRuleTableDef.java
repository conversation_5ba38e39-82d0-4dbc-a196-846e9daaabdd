package com.geeksec.rule.domain.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 检测规则实体表定义类
 *
 * 由 MyBatis-Flex 注解处理器生成，用于类型安全的查询构建
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public class DetectionRuleTableDef extends TableDef {

    /**
     * 检测规则实体表定义实例
     */
    public static final DetectionRuleTableDef DETECTION_RULE = new DetectionRuleTableDef();

    /**
     * 自增ID
     */
    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * 任务ID
     */
    public final QueryColumn TASK_ID = new QueryColumn(this, "task_id");

    /**
     * 标签ID（规则命中时打上的标签）
     */
    public final QueryColumn LABEL_ID = new QueryColumn(this, "label_id");

    /**
     * 威胁等级
     */
    public final QueryColumn THREAT_LEVEL = new QueryColumn(this, "threat_level");

    /**
     * 规则名称（告警类型）
     */
    public final QueryColumn RULE_NAME = new QueryColumn(this, "rule_name");

    /**
     * 规则描述
     */
    public final QueryColumn RULE_DESC = new QueryColumn(this, "rule_desc");

    /**
     * 规则状态：true-生效 false-失效
     */
    public final QueryColumn RULE_ENABLED = new QueryColumn(this, "rule_enabled");

    /**
     * 规则来源：SYSTEM-系统内置 USER-用户自定义
     */
    public final QueryColumn RULE_SOURCE = new QueryColumn(this, "rule_source");

    /**
     * 采集模式
     */
    public final QueryColumn CAPTURE_MODE = new QueryColumn(this, "capture_mode");

    /**
     * 规则JSON配置
     */
    public final QueryColumn RULE_JSON = new QueryColumn(this, "rule_json");

    /**
     * 规则hash
     */
    public final QueryColumn RULE_HASH = new QueryColumn(this, "rule_hash");

    /**
     * Cyber Kill Chain阶段
     */
    public final QueryColumn CYBER_KILL_CHAIN = new QueryColumn(this, "cyber_kill_chain");

    /**
     * 创建时间
     */
    public final QueryColumn CREATED_AT = new QueryColumn(this, "created_at");

    /**
     * 修改时间
     */
    public final QueryColumn UPDATED_AT = new QueryColumn(this, "updated_at");

    /**
     * 流量留存限速，单位：字节/秒
     */
    public final QueryColumn TRAFFIC_RATE_LIMIT_BPS = new QueryColumn(this, "traffic_rate_limit_bps");

    /**
     * 流量留存上限，单位：字节（负数表示不限）
     */
    public final QueryColumn TRAFFIC_RETENTION_LIMIT_BYTES = new QueryColumn(this, "traffic_retention_limit_bytes");

    /**
     * 是否留存流量元数据
     */
    public final QueryColumn RETAIN_METADATA = new QueryColumn(this, "retain_metadata");

    /**
     * 是否留存原始流量数据（PCAP文件）
     */
    public final QueryColumn RETAIN_PCAP = new QueryColumn(this, "retain_pcap");

    /**
     * 是否开启动态库响应
     */
    public final QueryColumn LIB_RESPOND_ENABLED = new QueryColumn(this, "lib_respond_enabled");

    /**
     * 动态库路径
     */
    public final QueryColumn LIB_RESPOND_LIB = new QueryColumn(this, "lib_respond_lib");

    /**
     * 库配置路径
     */
    public final QueryColumn LIB_RESPOND_CONFIG = new QueryColumn(this, "lib_respond_config");

    /**
     * 会话结束标识
     */
    public final QueryColumn LIB_RESPOND_SESSION_END = new QueryColumn(this, "lib_respond_session_end");

    /**
     * 响应包数
     */
    public final QueryColumn LIB_RESPOND_PKT_NUM = new QueryColumn(this, "lib_respond_pkt_num");

    /**
     * 构造函数
     */
    public DetectionRuleTableDef() {
        super("", "detection_rule_configs");
    }

    /**
     * 带别名的构造函数
     */
    public DetectionRuleTableDef(String alias) {
        super("", "detection_rule_configs", alias);
    }
}
