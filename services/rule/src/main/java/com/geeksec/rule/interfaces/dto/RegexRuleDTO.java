package com.geeksec.rule.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 正则规则DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "正则规则")
public class RegexRuleDTO {

    /**
     * 协议ID
     */
    @NotNull(message = "协议ID不能为空")
    @JsonProperty("pro_id")
    @Schema(description = "协议ID", example = "6")
    private Integer proId;

    /**
     * 正则表达式
     */
    @NotBlank(message = "正则表达式不能为空")
    @JsonProperty("regex")
    @Schema(description = "正则表达式", example = ".*malware.*")
    private String regex;

    /**
     * 属性
     */
    @JsonProperty("property")
    @Schema(description = "属性", example = "1")
    private Integer property;
}
