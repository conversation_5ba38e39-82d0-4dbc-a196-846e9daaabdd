package com.geeksec.rule.interfaces.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.rule.interfaces.dto.FilterInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 过滤规则VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "过滤规则信息")
public class FilterRuleVO {

    /**
     * 规则ID
     */
    @JsonProperty("id")
    @Schema(description = "规则ID", example = "1")
    private Long id;

    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    @Schema(description = "任务ID", example = "1001")
    private Integer taskId;

    /**
     * IP地址
     */
    @JsonProperty("ip")
    @Schema(description = "IP地址", example = "***********")
    private String ip;

    /**
     * 过滤规则JSON字符串
     */
    @JsonProperty("filter_json")
    @Schema(description = "过滤规则JSON字符串")
    private String filterJson;

    /**
     * 过滤规则对象信息
     */
    @JsonProperty("filter_info")
    @Schema(description = "过滤规则对象信息")
    private FilterInfoDTO filterInfo;

    /**
     * 创建时间
     */
    @JsonProperty("created_time")
    @Schema(description = "创建时间", example = "1640995200")
    private Integer createdTime;

    /**
     * 更新时间
     */
    @JsonProperty("updated_time")
    @Schema(description = "更新时间", example = "1640995200")
    private Integer updatedTime;

    /**
     * 过滤类型：0-端口 1-IP协议 2-网段
     */
    @JsonProperty("type")
    @Schema(description = "过滤类型", example = "0")
    private Integer type;

    /**
     * 规则hash值
     */
    @JsonProperty("hash")
    @Schema(description = "规则hash值")
    private String hash;
}
