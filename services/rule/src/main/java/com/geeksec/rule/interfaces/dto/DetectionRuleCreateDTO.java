package com.geeksec.rule.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 检测规则创建DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "检测规则创建请求")
public class DetectionRuleCreateDTO {

    /**
     * 任务ID
     */
    @NotNull(message = "任务ID不能为空")
    @JsonProperty("task_id")
    @Schema(description = "任务ID", example = "1001")
    private Integer taskId;

    /**
     * 批次ID
     */
    @JsonProperty("batch_id")
    @Schema(description = "批次ID", example = "1")
    private Integer batchId;

    /**
     * 规则名称
     */
    @NotBlank(message = "规则名称不能为空")
    @JsonProperty("rule_name")
    @Schema(description = "规则名称", example = "HTTP恶意请求检测")
    private String ruleName;

    /**
     * 规则描述
     */
    @JsonProperty("rule_desc")
    @Schema(description = "规则描述", example = "检测HTTP请求中的恶意特征")
    private String ruleDesc;

    /**
     * 规则级别
     */
    @JsonProperty("rule_level")
    @Schema(description = "规则级别", example = "1")
    private Integer ruleLevel;

    /**
     * 攻击阶段
     */
    @JsonProperty("attack_stage")
    @Schema(description = "攻击阶段", example = "RECONNAISSANCE")
    private String attackStage;

    /**
     * 流量留存限速（字节/秒）
     */
    @JsonProperty("traffic_rate_limit_bps")
    @Schema(description = "流量留存限速", example = "1048576")
    private Long trafficRateLimitBps;

    /**
     * 流量留存上限（字节）
     */
    @JsonProperty("traffic_retention_limit_bytes")
    @Schema(description = "流量留存上限", example = "104857600")
    private Long trafficRetentionLimitBytes;

    /**
     * 是否留存流量元数据
     */
    @JsonProperty("retain_metadata")
    @Schema(description = "是否留存流量元数据", example = "true")
    private Boolean retainMetadata;

    /**
     * 是否留存原始流量数据
     */
    @JsonProperty("retain_pcap")
    @Schema(description = "是否留存原始流量数据", example = "true")
    private Boolean retainPcap;

    /**
     * 是否开启动态库响应
     */
    @JsonProperty("lib_respond_open")
    @Schema(description = "是否开启动态库响应", example = "0")
    private Integer libRespondOpen;

    /**
     * 动态库路径
     */
    @JsonProperty("lib_respond_lib")
    @Schema(description = "动态库路径")
    private String libRespondLib;

    /**
     * 库配置路径
     */
    @JsonProperty("lib_respond_config")
    @Schema(description = "库配置路径")
    private String libRespondConfig;

    /**
     * 响应包数
     */
    @JsonProperty("lib_respond_pkt_num")
    @Schema(description = "响应包数", example = "10")
    private Long libRespondPktNum;

    /**
     * IP规则集
     */
    @Valid
    @JsonProperty("ip_rules")
    @Schema(description = "IP规则集")
    private List<IpRuleDTO> ipRules;

    /**
     * 协议规则集
     */
    @Valid
    @JsonProperty("pro_rules")
    @Schema(description = "协议规则集")
    private List<ProtocolRuleDTO> proRules;

    /**
     * 特征字规则集
     */
    @Valid
    @JsonProperty("keyword_rules")
    @Schema(description = "特征字规则集")
    private List<KeywordRuleDTO> keywordRules;

    /**
     * 正则规则集
     */
    @Valid
    @JsonProperty("regex_rules")
    @Schema(description = "正则规则集")
    private List<RegexRuleDTO> regexRules;

    /**
     * 域名规则集
     */
    @Valid
    @JsonProperty("domain_rules")
    @Schema(description = "域名规则集")
    private List<DomainRuleDTO> domainRules;

    /**
     * 复杂规则响应
     */
    @JsonProperty("detail_respond")
    @Schema(description = "复杂规则响应")
    private Map<String, Object> detailRespond;


}
