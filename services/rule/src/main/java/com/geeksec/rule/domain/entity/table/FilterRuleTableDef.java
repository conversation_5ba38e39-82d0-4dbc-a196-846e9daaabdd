package com.geeksec.rule.domain.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 过滤规则实体表定义类
 *
 * 由 MyBatis-Flex 注解处理器生成，用于类型安全的查询构建
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public class FilterRuleTableDef extends TableDef {

    /**
     * 过滤规则实体表定义实例
     */
    public static final FilterRuleTableDef FILTER_RULE = new FilterRuleTableDef();

    /**
     * 自增ID
     */
    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * 任务ID
     */
    public final QueryColumn TASK_ID = new QueryColumn(this, "task_id");

    /**
     * IP地址
     */
    public final QueryColumn IP = new QueryColumn(this, "ip");

    /**
     * 过滤规则JSON字符串
     */
    public final QueryColumn FILTER_JSON = new QueryColumn(this, "filter_json");

    /**
     * 创建时间
     */
    public final QueryColumn CREATED_AT = new QueryColumn(this, "created_at");

    /**
     * 更新时间
     */
    public final QueryColumn UPDATED_AT = new QueryColumn(this, "updated_at");

    /**
     * 规则hash值，用于校验重复
     */
    public final QueryColumn HASH = new QueryColumn(this, "hash");

    /**
     * 过滤条件：PORT-端口 INTERNET_PROTOCOL-IP协议 SUBNET-网段
     */
    public final QueryColumn CRITERIA = new QueryColumn(this, "criteria");

    /**
     * 是否激活：false-删除 true-正常
     */
    public final QueryColumn ACTIVE = new QueryColumn(this, "active");

    /**
     * 构造函数
     */
    public FilterRuleTableDef() {
        super("", "filter_rule");
    }

    /**
     * 带别名的构造函数
     */
    public FilterRuleTableDef(String alias) {
        super("", "filter_rule", alias);
    }
}
