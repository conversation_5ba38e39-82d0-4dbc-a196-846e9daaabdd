package com.geeksec.rule.domain.enums;

import lombok.Getter;

/**
 * 规则来源枚举
 *
 * <AUTHOR>
 */
@Getter
public enum RuleSourceEnum {

    /** 系统内置 */
    SYSTEM(0, "系统内置"),
    /** 用户自定义 */
    USER(1, "用户自定义");

    private final int code;
    private final String description;

    RuleSourceEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码查找枚举
     *
     * @param code 规则来源代码
     * @return 对应的枚举，如果未找到返回null
     */
    public static RuleSourceEnum findByCode(int code) {
        for (RuleSourceEnum sourceEnum : values()) {
            if (sourceEnum.code == code) {
                return sourceEnum;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 规则来源代码
     * @return 是否有效
     */
    public static boolean isValidCode(int code) {
        return findByCode(code) != null;
    }
}
