package com.geeksec.rule.application.service.impl;

import com.geeksec.rule.domain.constants.RuleConstants;
import com.geeksec.rule.domain.utils.RuleUtils;
import com.geeksec.rule.domain.valueobject.RuleValidationResult;
import com.geeksec.rule.interfaces.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 规则验证服务
 * 展示 JDK 17 新特性的使用
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class RuleValidationService {

    /**
     * 验证检测规则
     * 使用 Switch Expressions 和 Pattern Matching
     *
     * @param createDTO 创建请求
     * @return 验证结果
     */
    public RuleValidationResult validateDetectionRule(DetectionRuleCreateDTO createDTO) {
        List<String> errors = new ArrayList<>();

        // 基础字段验证
        if (createDTO.getRuleName() == null || createDTO.getRuleName().isBlank()) {
            errors.add("规则名称不能为空");
        }

        if (createDTO.getTaskId() == null || createDTO.getTaskId() <= 0) {
            errors.add("任务ID无效");
        }

        // 验证攻击阶段
        String stageValidation = null;
        if (createDTO.getAttackStage() == null) {
            stageValidation = "攻击阶段不能为空";
        } else if (!RuleUtils.isValidAttackStage(createDTO.getAttackStage())) {
            stageValidation = "无效的攻击阶段";
        }

        if (stageValidation != null) {
            errors.add(stageValidation);
        }

        // 验证IP规则
        if (createDTO.getIpRules() != null) {
            validateIpRules(createDTO.getIpRules(), errors);
        }

        // 验证协议规则
        if (createDTO.getProRules() != null) {
            validateProtocolRules(createDTO.getProRules(), errors);
        }

        // 验证特征字规则
        if (createDTO.getKeywordRules() != null) {
            validateKeywordRules(createDTO.getKeywordRules(), errors);
        }

        // 验证正则规则
        if (createDTO.getRegexRules() != null) {
            validateRegexRules(createDTO.getRegexRules(), errors);
        }

        // 验证域名规则
        if (createDTO.getDomainRules() != null) {
            validateDomainRules(createDTO.getDomainRules(), errors);
        }

        if (!errors.isEmpty()) {
            return RuleValidationResult.failure(errors);
        }

        // 生成规则hash
        String ruleHash = RuleUtils.generateRuleHash(createDTO.toString());
        return RuleValidationResult.success(ruleHash);
    }

    /**
     * 验证过滤规则
     *
     * @param createDTO 创建请求
     * @return 验证结果
     */
    public RuleValidationResult validateFilterRule(FilterRuleCreateDTO createDTO) {
        List<String> errors = new ArrayList<>();

        if (createDTO.getTaskId() == null || createDTO.getTaskId() <= 0) {
            errors.add("任务ID无效");
        }

        if (createDTO.getFilterInfo() == null) {
            errors.add("过滤规则信息不能为空");
        } else {
            validateFilterInfo(createDTO.getFilterInfo(), createDTO.getType(), errors);
        }

        if (!errors.isEmpty()) {
            return RuleValidationResult.failure(errors);
        }

        String ruleHash = RuleUtils.generateRuleHash(createDTO.toString());
        return RuleValidationResult.success(ruleHash);
    }

    private void validateIpRules(List<IpRuleDTO> ipRules, List<String> errors) {
        for (int i = 0; i < ipRules.size(); i++) {
            IpRuleDTO rule = ipRules.get(i);
            if (!RuleUtils.isValidIp(rule.getIp())) {
                errors.add("第%d个IP规则的IP地址格式无效: %s".formatted(i + 1, rule.getIp()));
            }
            
            // 验证IP类型
            String typeValidation = null;
            if (rule.getType() == null) {
                typeValidation = "IP类型不能为空";
            } else if (rule.getType() < 1 || rule.getType() > 3) {
                typeValidation = "IP类型取值范围为1-3";
            }

            if (typeValidation != null) {
                errors.add(String.format("第%d个IP规则: %s", i + 1, typeValidation));
            }
        }
    }

    private void validateProtocolRules(List<ProtocolRuleDTO> proRules, List<String> errors) {
        for (int i = 0; i < proRules.size(); i++) {
            ProtocolRuleDTO rule = proRules.get(i);
            if (rule.getProId() == null || rule.getProId() <= 0) {
                errors.add("第%d个协议规则的协议ID无效".formatted(i + 1));
            }
            
            if (rule.getPortRule() != null) {
                validatePortRule(rule.getPortRule(), i + 1, errors);
            }
        }
    }

    private void validatePortRule(PortRuleDTO portRule, int ruleIndex, List<String> errors) {
        if (portRule.getSrcPort() != null && !RuleUtils.isValidPorts(portRule.getSrcPort())) {
            errors.add("第%d个协议规则的源端口列表包含无效端口".formatted(ruleIndex));
        }
        
        if (portRule.getDstPort() != null && !RuleUtils.isValidPorts(portRule.getDstPort())) {
            errors.add("第%d个协议规则的目标端口列表包含无效端口".formatted(ruleIndex));
        }
    }

    private void validateKeywordRules(List<KeywordRuleDTO> keywordRules, List<String> errors) {
        for (int i = 0; i < keywordRules.size(); i++) {
            KeywordRuleDTO rule = keywordRules.get(i);
            if (rule.getKeyword() == null || rule.getKeyword().isBlank()) {
                errors.add("第%d个特征字规则的关键字不能为空".formatted(i + 1));
            }

            if (rule.getProId() == null || rule.getProId() <= 0) {
                errors.add("第%d个特征字规则的协议ID无效".formatted(i + 1));
            }
        }
    }

    private void validateRegexRules(List<RegexRuleDTO> regexRules, List<String> errors) {
        for (int i = 0; i < regexRules.size(); i++) {
            RegexRuleDTO rule = regexRules.get(i);
            if (!RuleUtils.isValidRegex(rule.getRegex())) {
                errors.add("第%d个正则规则的正则表达式无效: %s".formatted(i + 1, rule.getRegex()));
            }
            
            if (rule.getProId() == null || rule.getProId() <= 0) {
                errors.add("第%d个正则规则的协议ID无效".formatted(i + 1));
            }
        }
    }

    private void validateDomainRules(List<DomainRuleDTO> domainRules, List<String> errors) {
        for (int i = 0; i < domainRules.size(); i++) {
            DomainRuleDTO rule = domainRules.get(i);
            if (!RuleUtils.isValidDomain(rule.getDomain())) {
                errors.add("第%d个域名规则的域名格式无效: %s".formatted(i + 1, rule.getDomain()));
            }
            
            // 验证域名类型
            String typeValidation = null;
            if (rule.getType() == null) {
                typeValidation = "域名类型不能为空";
            } else if (rule.getType() < 1 || rule.getType() > 2) {
                typeValidation = "域名类型取值范围为1-2";
            }

            if (typeValidation != null) {
                errors.add(String.format("第%d个域名规则: %s", i + 1, typeValidation));
            }
        }
    }

    private void validateFilterInfo(FilterInfoDTO filterInfo, Integer type, List<String> errors) {
        // 使用 Switch Expression 根据类型验证不同的字段
        switch (type) {
            case 0 -> { // 端口过滤
                if (filterInfo.getPort() == null || filterInfo.getPort().isEmpty()) {
                    errors.add("端口过滤规则的端口列表不能为空");
                } else if (!RuleUtils.isValidPorts(filterInfo.getPort())) {
                    errors.add("端口过滤规则包含无效端口");
                }
            }
            case 1 -> { // 互联网协议过滤
                if (filterInfo.getProId() == null || filterInfo.getProId() <= 0) {
                    errors.add("互联网协议过滤规则的协议ID无效");
                }
                if (filterInfo.getIp() != null) {
                    for (String ip : filterInfo.getIp()) {
                        if (!RuleUtils.isValidIp(ip)) {
                            errors.add("互联网协议过滤规则包含无效IP地址: " + ip);
                        }
                    }
                }
            }
            case 2 -> { // 子网过滤
                if (filterInfo.getNetwork() == null || filterInfo.getNetwork().isEmpty()) {
                    errors.add("子网过滤规则的网段列表不能为空");
                }
                // 这里可以添加网段格式验证
            }
            default -> errors.add("无效的过滤规则类型: " + type);
        }
    }
}
