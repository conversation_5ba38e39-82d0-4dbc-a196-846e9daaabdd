package com.geeksec.rule.domain.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 过滤模式实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Table("filter_mode")
public class FilterMode implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @Id(keyType = KeyType.Auto)
    private Long id;

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 是否丢弃模式：false-保留 true-丢弃
     */
    @Column("drop_mode")
    private boolean dropMode;
}
