package com.geeksec.threatdetector.killchain.function;

import com.geeksec.threatdetector.killchain.analyzer.AttackChainAnalyzer;
import com.geeksec.threatdetector.killchain.model.AttackChainEvent;
import com.geeksec.threatdetector.killchain.model.CyberKillChainStage;
import com.geeksec.threatdetector.model.output.Alarm;
import com.geeksec.threatdetector.state.CoreStateManager;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.state.*;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 优化的攻击链分析函数
 * 使用Flink Managed State存储检测算法状态，只在需要跨作业关联时使用Redis
 * 
 * <AUTHOR>
 */
@Slf4j
public class OptimizedAttackChainAnalysisFunction extends KeyedProcessFunction<String, Alarm, Alarm> {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 检测上下文状态（Flink Managed State）
     */
    private transient ValueState<DetectionContext> detectionContextState;
    
    /**
     * 最近事件状态（Flink Managed State）
     */
    private transient MapState<String, EventInfo> recentEventsState;
    
    /**
     * 窗口数据状态（Flink Managed State）
     */
    private transient ListState<WindowData> windowDataState;
    
    /**
     * 攻击链分析器
     */
    private transient AttackChainAnalyzer attackChainAnalyzer;
    
    /**
     * 核心状态管理器（用于跨作业状态共享）
     */
    private transient CoreStateManager coreStateManager;
    
    /**
     * 事件关联窗口（分钟）
     */
    private final int correlationWindowMinutes;
    
    /**
     * 是否启用攻击链分析
     */
    private final boolean analysisEnabled;
    
    /**
     * 是否将分析结果附加到告警
     */
    private final boolean attachAnalysisResult;
    
    /**
     * 统计计数器
     */
    private transient AtomicLong processedCount;
    private transient AtomicLong analyzedCount;
    private transient AtomicLong correlatedCount;
    
    /**
     * 构造函数
     */
    public OptimizedAttackChainAnalysisFunction(int correlationWindowMinutes, 
                                              boolean analysisEnabled, 
                                              boolean attachAnalysisResult) {
        this.correlationWindowMinutes = correlationWindowMinutes;
        this.analysisEnabled = analysisEnabled;
        this.attachAnalysisResult = attachAnalysisResult;
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化Flink Managed State
        initializeFlinkStates();
        
        // 初始化攻击链分析器
        attackChainAnalyzer = new AttackChainAnalyzer(correlationWindowMinutes);
        
        // 初始化核心状态管理器
        coreStateManager = CoreStateManager.getInstance();
        
        // 初始化统计计数器
        processedCount = new AtomicLong(0);
        analyzedCount = new AtomicLong(0);
        correlatedCount = new AtomicLong(0);
        
        log.info("优化的攻击链分析函数初始化完成，关联窗口: {}分钟, 分析启用: {}", 
                correlationWindowMinutes, analysisEnabled);
    }
    
    /**
     * 初始化Flink状态
     */
    private void initializeFlinkStates() {
        // 检测上下文状态
        ValueStateDescriptor<DetectionContext> contextDescriptor = new ValueStateDescriptor<>(
                "detection-context",
                TypeInformation.of(new TypeHint<DetectionContext>() {})
        );
        detectionContextState = getRuntimeContext().getState(contextDescriptor);
        
        // 最近事件状态
        MapStateDescriptor<String, EventInfo> eventsDescriptor = new MapStateDescriptor<>(
                "recent-events",
                TypeInformation.of(String.class),
                TypeInformation.of(new TypeHint<EventInfo>() {})
        );
        recentEventsState = getRuntimeContext().getMapState(eventsDescriptor);

        // 窗口数据状态
        ListStateDescriptor<WindowData> windowDescriptor = new ListStateDescriptor<>(
                "window-data",
                TypeInformation.of(new TypeHint<WindowData>() {})
        );
        windowDataState = getRuntimeContext().getListState(windowDescriptor);
    }
    
    @Override
    public void processElement(Alarm alarm, Context ctx, Collector<Alarm> out) throws Exception {
        processedCount.incrementAndGet();
        
        if (!analysisEnabled) {
            out.collect(alarm);
            return;
        }
        
        // 检查检测器是否启用
        if (!coreStateManager.isDetectorEnabled("AttackChainAnalyzer")) {
            log.debug("攻击链分析器已禁用，跳过分析");
            out.collect(alarm);
            return;
        }
        
        try {
            // 1. 更新检测上下文
            updateDetectionContext(alarm, ctx);
            
            // 2. 添加到最近事件
            addToRecentEvents(alarm, ctx);
            
            // 3. 检查是否需要跨作业关联
            String correlatedChainId = checkCrossJobCorrelation(alarm);
            
            if (correlatedChainId != null) {
                // 4. 关联到现有攻击链
                correlatedCount.incrementAndGet();
                alarm = attachCorrelationInfo(alarm, correlatedChainId);
                log.debug("告警关联到现有攻击链: {} -> {}", alarm.getAlarmId(), correlatedChainId);
            } else {
                // 5. 执行本地攻击链分析
                alarm = performLocalAnalysis(alarm, ctx);
            }
            
            analyzedCount.incrementAndGet();
            
        } catch (Exception e) {
            log.error("攻击链分析异常: 告警={}", alarm.getAlarmId(), e);
        }
        
        out.collect(alarm);
    }
    
    /**
     * 更新检测上下文
     */
    private void updateDetectionContext(Alarm alarm, Context ctx) throws Exception {
        DetectionContext context = detectionContextState.value();
        if (context == null) {
            context = DetectionContext.builder()
                    .firstEventTime(LocalDateTime.now())
                    .eventCount(0)
                    .build();
        }
        
        context.setLastEventTime(LocalDateTime.now());
        context.setEventCount(context.getEventCount() + 1);
        context.setLastAlarmType(alarm.getAlarmType());
        
        detectionContextState.update(context);
    }
    
    /**
     * 添加到最近事件
     */
    private void addToRecentEvents(Alarm alarm, Context ctx) throws Exception {
        EventInfo eventInfo = EventInfo.builder()
                .alarmId(alarm.getAlarmId())
                .alarmType(alarm.getAlarmType())
                .srcIp(alarm.getSrcIp())
                .dstIp(alarm.getDstIp())
                .timestamp(LocalDateTime.now())
                .build();
        
        recentEventsState.put(alarm.getAlarmId(), eventInfo);
        
        // 设置清理定时器（清理过期事件）
        long cleanupTime = ctx.timerService().currentProcessingTime() + 
                correlationWindowMinutes * 60 * 1000L;
        ctx.timerService().registerProcessingTimeTimer(cleanupTime);
    }
    
    /**
     * 检查跨作业关联
     */
    private String checkCrossJobCorrelation(Alarm alarm) {
        try {
            // 从核心状态管理器查找相关的攻击链
            Set<String> activeChainIds = coreStateManager.getActiveAttackChainIds();
            
            for (String chainId : activeChainIds) {
                CoreStateManager.AttackChainSummary summary = coreStateManager.getActiveAttackChain(chainId);
                if (summary != null && isAlarmCorrelatedToChain(alarm, summary)) {
                    return chainId;
                }
            }
            
        } catch (Exception e) {
            log.error("检查跨作业关联失败", e);
        }
        
        return null;
    }
    
    /**
     * 检查告警是否与攻击链相关
     */
    private boolean isAlarmCorrelatedToChain(Alarm alarm, CoreStateManager.AttackChainSummary summary) {
        // 检查IP关联
        if (alarm.getSrcIp() != null && summary.getAttackerIp() != null) {
            if (alarm.getSrcIp().equals(summary.getAttackerIp())) {
                return true;
            }
        }
        
        if (alarm.getDstIp() != null && summary.getVictimIp() != null) {
            if (alarm.getDstIp().equals(summary.getVictimIp())) {
                return true;
            }
        }
        
        // 检查时间窗口
        if (summary.getLastUpdateTime() != null) {
            LocalDateTime windowStart = LocalDateTime.now().minusMinutes(correlationWindowMinutes);
            return summary.getLastUpdateTime().isAfter(windowStart);
        }
        
        return false;
    }
    
    /**
     * 附加关联信息到告警
     */
    private Alarm attachCorrelationInfo(Alarm alarm, String chainId) {
        if (!attachAnalysisResult) {
            return alarm;
        }
        
        Alarm enrichedAlarm = alarm.toBuilder().build();
        
        if (enrichedAlarm.getAttributes() == null) {
            enrichedAlarm.setAttributes(new HashMap<>());
        }
        
        enrichedAlarm.getAttributes().put("attack_chain_id", chainId);
        enrichedAlarm.getAttributes().put("correlation_type", "cross_job");
        enrichedAlarm.getAttributes().put("correlation_time", LocalDateTime.now().toString());
        
        return enrichedAlarm;
    }
    
    /**
     * 执行本地分析
     */
    private Alarm performLocalAnalysis(Alarm alarm, Context ctx) throws Exception {
        try {
            // 使用攻击链分析器进行分析
            AttackChainAnalyzer.AttackChainAnalysisResult result = attackChainAnalyzer.analyzeAlarm(alarm);
            
            if (result.isSuccess() && attachAnalysisResult) {
                return attachAnalysisResult(alarm, result);
            }
            
        } catch (Exception e) {
            log.error("本地攻击链分析失败: {}", alarm.getAlarmId(), e);
        }
        
        return alarm;
    }
    
    /**
     * 附加分析结果到告警
     */
    private Alarm attachAnalysisResult(Alarm alarm, AttackChainAnalyzer.AttackChainAnalysisResult result) {
        Alarm enrichedAlarm = alarm.toBuilder().build();
        
        if (enrichedAlarm.getAttributes() == null) {
            enrichedAlarm.setAttributes(new HashMap<>());
        }
        
        enrichedAlarm.getAttributes().put("attack_chain_id", result.getAttackChainId());
        enrichedAlarm.getAttributes().put("kill_chain_stage", result.getKillChainStage().getStageCode());
        enrichedAlarm.getAttributes().put("kill_chain_stage_name", result.getKillChainStage().getChineseName());
        enrichedAlarm.getAttributes().put("new_chain_created", result.isNewChainCreated());
        enrichedAlarm.getAttributes().put("analysis_time", LocalDateTime.now().toString());
        
        return enrichedAlarm;
    }
    
    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<Alarm> out) throws Exception {
        // 清理过期的事件数据
        cleanupExpiredEvents();
    }
    
    /**
     * 清理过期事件
     */
    private void cleanupExpiredEvents() throws Exception {
        LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(correlationWindowMinutes);
        
        Iterator<Map.Entry<String, EventInfo>> iterator = recentEventsState.iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, EventInfo> entry = iterator.next();
            if (entry.getValue().getTimestamp().isBefore(cutoffTime)) {
                iterator.remove();
            }
        }
    }
    
    /**
     * 获取统计信息
     */
    public ProcessingStatistics getStatistics() {
        return ProcessingStatistics.builder()
                .processedCount(processedCount.get())
                .analyzedCount(analyzedCount.get())
                .correlatedCount(correlatedCount.get())
                .build();
    }
    
    // ==================== 数据模型 ====================
    
    /**
     * 检测上下文
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetectionContext implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private LocalDateTime firstEventTime;
        private LocalDateTime lastEventTime;
        private int eventCount;
        private String lastAlarmType;
        private Map<String, Object> contextData;
    }
    
    /**
     * 事件信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EventInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private String alarmId;
        private String alarmType;
        private String srcIp;
        private String dstIp;
        private LocalDateTime timestamp;
    }
    
    /**
     * 窗口数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WindowData implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private LocalDateTime windowStart;
        private LocalDateTime windowEnd;
        private List<String> alarmIds;
        private Map<String, Integer> alarmTypeCounts;
    }
    
    /**
     * 处理统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcessingStatistics implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private long processedCount;
        private long analyzedCount;
        private long correlatedCount;
        
        public double getAnalysisRate() {
            return processedCount > 0 ? (double) analyzedCount / processedCount : 0.0;
        }
        
        public double getCorrelationRate() {
            return analyzedCount > 0 ? (double) correlatedCount / analyzedCount : 0.0;
        }
    }
}
