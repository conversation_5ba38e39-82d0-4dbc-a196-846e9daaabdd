package com.geeksec.threatdetector.formatting.generator;

import com.geeksec.threatdetector.formatting.knowledge.ThreatKnowledgeBase;
import com.geeksec.threatdetector.formatting.model.AlarmFormattedContent;
import com.geeksec.threatdetector.formatting.model.FormattingContext;
import com.geeksec.threatdetector.model.output.Alarm;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 处理建议生成器
 * 根据告警信息和上下文生成处理建议
 * 
 * <AUTHOR>
 */
@Slf4j
public class SuggestionGenerator implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 威胁知识库
     */
    private final ThreatKnowledgeBase knowledgeBase;
    
    /**
     * 建议模板缓存
     */
    private final Map<String, SuggestionTemplate> suggestionTemplateCache;
    
    /**
     * 构造函数
     * 
     * @param knowledgeBase 威胁知识库
     */
    public SuggestionGenerator(ThreatKnowledgeBase knowledgeBase) {
        this.knowledgeBase = knowledgeBase;
        this.suggestionTemplateCache = new ConcurrentHashMap<>();
        initializeSuggestionTemplates();
    }
    
    /**
     * 生成处理建议
     * 
     * @param alarm 告警信息
     * @param context 格式化上下文
     * @return 处理建议
     */
    public AlarmFormattedContent.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, FormattingContext context) {
        if (alarm == null) {
            return null;
        }
        
        try {
            // 生成立即处理建议
            List<AlarmFormattedContent.ActionSuggestion> immediateActions = generateImmediateActions(alarm, context);
            
            // 生成短期处理建议
            List<AlarmFormattedContent.ActionSuggestion> shortTermActions = generateShortTermActions(alarm, context);
            
            // 生成长期处理建议
            List<AlarmFormattedContent.ActionSuggestion> longTermActions = generateLongTermActions(alarm, context);
            
            // 生成预防措施
            List<AlarmFormattedContent.ActionSuggestion> preventiveMeasures = generatePreventiveMeasures(alarm, context);
            
            // 生成优先级指导
            String priorityGuidance = generatePriorityGuidance(alarm, context);
            
            return AlarmFormattedContent.HandlingSuggestions.builder()
                    .immediateActions(immediateActions)
                    .shortTermActions(shortTermActions)
                    .longTermActions(longTermActions)
                    .preventiveMeasures(preventiveMeasures)
                    .priorityGuidance(priorityGuidance)
                    .build();
            
        } catch (Exception e) {
            log.error("生成处理建议失败: {}", e.getMessage(), e);
            return createDefaultHandlingSuggestions(alarm);
        }
    }
    
    /**
     * 生成立即处理建议
     * 
     * @param alarm 告警信息
     * @param context 格式化上下文
     * @return 立即处理建议列表
     */
    private List<AlarmFormattedContent.ActionSuggestion> generateImmediateActions(Alarm alarm, FormattingContext context) {
        List<AlarmFormattedContent.ActionSuggestion> actions = new ArrayList<>();
        
        String threatType = alarm.getThreatType();
        Double confidence = alarm.getConfidence();
        
        // 从知识库获取建议
        List<ThreatKnowledgeBase.HandlingSuggestion> kbSuggestions = knowledgeBase.getHandlingSuggestions(threatType);
        if (kbSuggestions != null) {
            for (ThreatKnowledgeBase.HandlingSuggestion suggestion : kbSuggestions) {
                if (suggestion.getPriority() == AlarmFormattedContent.Priority.URGENT ||
                    suggestion.getPriority() == AlarmFormattedContent.Priority.HIGH) {
                    actions.add(convertKbSuggestionToAction(suggestion));
                }
            }
        }
        
        // 根据威胁类型生成特定建议
        switch (threatType) {
            case "恶意软件":
                actions.addAll(generateMalwareImmediateActions(alarm, context));
                break;
            case "网络扫描":
                actions.addAll(generateScanImmediateActions(alarm, context));
                break;
            case "DNS隧道":
                actions.addAll(generateDnsTunnelImmediateActions(alarm, context));
                break;
            case "Webshell":
                actions.addAll(generateWebshellImmediateActions(alarm, context));
                break;
            default:
                actions.addAll(generateGenericImmediateActions(alarm, context));
                break;
        }
        
        // 根据置信度调整建议
        if (confidence != null && confidence >= 0.9) {
            actions.add(createHighConfidenceAction(alarm));
        }
        
        return actions;
    }
    
    /**
     * 生成恶意软件立即处理建议
     */
    private List<AlarmFormattedContent.ActionSuggestion> generateMalwareImmediateActions(Alarm alarm, FormattingContext context) {
        List<AlarmFormattedContent.ActionSuggestion> actions = new ArrayList<>();
        
        actions.add(AlarmFormattedContent.ActionSuggestion.builder()
                .title("立即隔离感染主机")
                .description("将检测到恶意软件的主机从网络中隔离，防止横向传播")
                .steps(List.of(
                        "断开主机网络连接",
                        "保留现场证据",
                        "启动应急响应流程",
                        "通知安全团队和相关业务人员"
                ))
                .priority(AlarmFormattedContent.Priority.URGENT)
                .estimatedTime("立即执行")
                .requiredSkills("网络管理、安全响应")
                .riskAssessment("高风险，延迟处理可能导致感染扩散")
                .build());
        
        actions.add(AlarmFormattedContent.ActionSuggestion.builder()
                .title("启动恶意软件清除程序")
                .description("使用专业的反恶意软件工具清除感染")
                .steps(List.of(
                        "更新反恶意软件特征库",
                        "执行全盘扫描",
                        "清除检测到的恶意文件",
                        "验证清除效果"
                ))
                .priority(AlarmFormattedContent.Priority.HIGH)
                .estimatedTime("30分钟-2小时")
                .requiredSkills("恶意软件分析、系统管理")
                .riskAssessment("中等风险，需要专业工具和技能")
                .build());
        
        return actions;
    }
    
    /**
     * 生成网络扫描立即处理建议
     */
    private List<AlarmFormattedContent.ActionSuggestion> generateScanImmediateActions(Alarm alarm, FormattingContext context) {
        List<AlarmFormattedContent.ActionSuggestion> actions = new ArrayList<>();
        
        actions.add(AlarmFormattedContent.ActionSuggestion.builder()
                .title("阻断扫描源IP")
                .description("在防火墙或网络设备上阻断扫描源IP地址")
                .steps(List.of(
                        "识别扫描源IP: " + alarm.getSrcIp(),
                        "评估IP地址信誉",
                        "在防火墙中添加阻断规则",
                        "监控阻断效果"
                ))
                .priority(AlarmFormattedContent.Priority.HIGH)
                .estimatedTime("15-30分钟")
                .requiredSkills("网络管理、防火墙配置")
                .riskAssessment("低风险，预防性措施")
                .build());
        
        return actions;
    }
    
    /**
     * 生成DNS隧道立即处理建议
     */
    private List<AlarmFormattedContent.ActionSuggestion> generateDnsTunnelImmediateActions(Alarm alarm, FormattingContext context) {
        List<AlarmFormattedContent.ActionSuggestion> actions = new ArrayList<>();
        
        actions.add(AlarmFormattedContent.ActionSuggestion.builder()
                .title("阻断恶意域名")
                .description("在DNS服务器或防火墙中阻断可疑域名")
                .steps(List.of(
                        "识别可疑域名",
                        "在DNS服务器中配置域名黑名单",
                        "更新防火墙规则",
                        "监控DNS查询日志"
                ))
                .priority(AlarmFormattedContent.Priority.HIGH)
                .estimatedTime("20-45分钟")
                .requiredSkills("DNS管理、网络安全")
                .riskAssessment("中等风险，可能影响正常业务")
                .build());
        
        return actions;
    }
    
    /**
     * 生成Webshell立即处理建议
     */
    private List<AlarmFormattedContent.ActionSuggestion> generateWebshellImmediateActions(Alarm alarm, FormattingContext context) {
        List<AlarmFormattedContent.ActionSuggestion> actions = new ArrayList<>();
        
        actions.add(AlarmFormattedContent.ActionSuggestion.builder()
                .title("立即删除Webshell文件")
                .description("定位并删除检测到的Webshell后门文件")
                .steps(List.of(
                        "定位Webshell文件位置",
                        "备份文件用于分析",
                        "删除恶意文件",
                        "检查文件系统完整性",
                        "重启Web服务"
                ))
                .priority(AlarmFormattedContent.Priority.URGENT)
                .estimatedTime("30分钟-1小时")
                .requiredSkills("Web安全、系统管理")
                .riskAssessment("高风险，Webshell可能被用于进一步攻击")
                .build());
        
        return actions;
    }
    
    /**
     * 生成通用立即处理建议
     */
    private List<AlarmFormattedContent.ActionSuggestion> generateGenericImmediateActions(Alarm alarm, FormattingContext context) {
        List<AlarmFormattedContent.ActionSuggestion> actions = new ArrayList<>();
        
        actions.add(AlarmFormattedContent.ActionSuggestion.builder()
                .title("验证告警真实性")
                .description("分析告警信息，确认是否为真实威胁")
                .steps(List.of(
                        "检查告警详细信息",
                        "分析相关日志",
                        "验证网络流量",
                        "确认威胁真实性"
                ))
                .priority(AlarmFormattedContent.Priority.HIGH)
                .estimatedTime("15-30分钟")
                .requiredSkills("安全分析、日志分析")
                .riskAssessment("低风险，分析性工作")
                .build());
        
        return actions;
    }
    
    /**
     * 生成短期处理建议
     */
    private List<AlarmFormattedContent.ActionSuggestion> generateShortTermActions(Alarm alarm, FormattingContext context) {
        List<AlarmFormattedContent.ActionSuggestion> actions = new ArrayList<>();
        
        // 通用短期建议
        actions.add(AlarmFormattedContent.ActionSuggestion.builder()
                .title("加强监控和日志记录")
                .description("增强对相关系统和网络的监控")
                .steps(List.of(
                        "配置详细日志记录",
                        "设置实时监控告警",
                        "增加安全检查频率",
                        "建立威胁狩猎机制"
                ))
                .priority(AlarmFormattedContent.Priority.MEDIUM)
                .estimatedTime("2-4小时")
                .requiredSkills("监控配置、日志管理")
                .riskAssessment("低风险，预防性措施")
                .build());
        
        actions.add(AlarmFormattedContent.ActionSuggestion.builder()
                .title("进行安全评估")
                .description("对受影响的系统进行全面安全评估")
                .steps(List.of(
                        "识别受影响的系统和资产",
                        "评估安全控制措施",
                        "检查系统配置",
                        "识别安全薄弱环节",
                        "制定改进计划"
                ))
                .priority(AlarmFormattedContent.Priority.MEDIUM)
                .estimatedTime("1-2天")
                .requiredSkills("安全评估、风险分析")
                .riskAssessment("低风险，评估性工作")
                .build());
        
        return actions;
    }
    
    /**
     * 生成长期处理建议
     */
    private List<AlarmFormattedContent.ActionSuggestion> generateLongTermActions(Alarm alarm, FormattingContext context) {
        List<AlarmFormattedContent.ActionSuggestion> actions = new ArrayList<>();
        
        actions.add(AlarmFormattedContent.ActionSuggestion.builder()
                .title("完善安全策略和流程")
                .description("基于此次事件完善组织的安全策略")
                .steps(List.of(
                        "分析事件根本原因",
                        "评估现有安全策略",
                        "制定改进措施",
                        "更新安全流程",
                        "开展安全培训"
                ))
                .priority(AlarmFormattedContent.Priority.MEDIUM)
                .estimatedTime("1-2周")
                .requiredSkills("安全管理、策略制定")
                .riskAssessment("低风险，管理性工作")
                .build());
        
        return actions;
    }
    
    /**
     * 生成预防措施
     */
    private List<AlarmFormattedContent.ActionSuggestion> generatePreventiveMeasures(Alarm alarm, FormattingContext context) {
        List<AlarmFormattedContent.ActionSuggestion> measures = new ArrayList<>();
        
        measures.add(AlarmFormattedContent.ActionSuggestion.builder()
                .title("部署额外安全控制")
                .description("根据威胁类型部署相应的安全控制措施")
                .steps(List.of(
                        "评估当前安全控制",
                        "识别控制缺口",
                        "选择合适的安全工具",
                        "部署和配置安全控制",
                        "测试控制有效性"
                ))
                .priority(AlarmFormattedContent.Priority.LOW)
                .estimatedTime("1-4周")
                .requiredSkills("安全架构、工具部署")
                .riskAssessment("低风险，预防性投资")
                .build());
        
        return measures;
    }
    
    /**
     * 生成优先级指导
     */
    private String generatePriorityGuidance(Alarm alarm, FormattingContext context) {
        StringBuilder guidance = new StringBuilder();
        
        Double confidence = alarm.getConfidence();
        String threatType = alarm.getThreatType();
        
        guidance.append("处理优先级建议: ");
        
        if (confidence != null && confidence >= 0.9) {
            guidance.append("高置信度告警，建议立即处理。");
        } else if (confidence != null && confidence >= 0.7) {
            guidance.append("较高置信度告警，建议优先处理。");
        } else {
            guidance.append("中等置信度告警，建议验证后处理。");
        }
        
        // 根据威胁类型调整优先级
        switch (threatType) {
            case "恶意软件":
            case "Webshell":
                guidance.append(" 此类威胁可能造成严重损害，建议提高处理优先级。");
                break;
            case "网络扫描":
                guidance.append(" 此类活动通常是攻击前奏，建议及时阻断。");
                break;
            default:
                guidance.append(" 建议根据业务影响确定处理优先级。");
                break;
        }
        
        return guidance.toString();
    }
    
    /**
     * 创建高置信度处理建议
     */
    private AlarmFormattedContent.ActionSuggestion createHighConfidenceAction(Alarm alarm) {
        return AlarmFormattedContent.ActionSuggestion.builder()
                .title("高置信度告警处理")
                .description("由于检测置信度极高，建议立即采取行动")
                .steps(List.of(
                        "确认告警详情",
                        "立即启动应急响应",
                        "通知相关人员",
                        "采取必要的隔离措施"
                ))
                .priority(AlarmFormattedContent.Priority.URGENT)
                .estimatedTime("立即执行")
                .requiredSkills("应急响应、安全管理")
                .riskAssessment("极高风险，需要立即处理")
                .build();
    }
    
    /**
     * 转换知识库建议为行动建议
     */
    private AlarmFormattedContent.ActionSuggestion convertKbSuggestionToAction(ThreatKnowledgeBase.HandlingSuggestion suggestion) {
        return AlarmFormattedContent.ActionSuggestion.builder()
                .title(suggestion.getTitle())
                .description(suggestion.getDescription())
                .steps(suggestion.getSteps())
                .priority(suggestion.getPriority())
                .estimatedTime(suggestion.getTimeFrame())
                .requiredSkills(suggestion.getRequiredSkills())
                .riskAssessment(suggestion.getRiskAssessment())
                .build();
    }
    
    /**
     * 创建默认处理建议
     */
    private AlarmFormattedContent.HandlingSuggestions createDefaultHandlingSuggestions(Alarm alarm) {
        List<AlarmFormattedContent.ActionSuggestion> defaultActions = List.of(
                AlarmFormattedContent.ActionSuggestion.builder()
                        .title("验证告警")
                        .description("分析告警信息，确认威胁真实性")
                        .steps(List.of("检查告警详情", "分析相关日志", "确认威胁"))
                        .priority(AlarmFormattedContent.Priority.MEDIUM)
                        .estimatedTime("30分钟")
                        .requiredSkills("安全分析")
                        .riskAssessment("低风险")
                        .build()
        );
        
        return AlarmFormattedContent.HandlingSuggestions.builder()
                .immediateActions(defaultActions)
                .shortTermActions(new ArrayList<>())
                .longTermActions(new ArrayList<>())
                .preventiveMeasures(new ArrayList<>())
                .priorityGuidance("建议根据业务影响确定处理优先级")
                .build();
    }
    
    /**
     * 初始化建议模板
     */
    private void initializeSuggestionTemplates() {
        // 这里可以初始化各种建议模板
        log.info("建议模板初始化完成");
    }
    
    /**
     * 建议模板类
     */
    private static class SuggestionTemplate implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private final String threatType;
        private final List<AlarmFormattedContent.ActionSuggestion> templates;
        
        public SuggestionTemplate(String threatType, List<AlarmFormattedContent.ActionSuggestion> templates) {
            this.threatType = threatType;
            this.templates = templates;
        }
        
        public List<AlarmFormattedContent.ActionSuggestion> getTemplates() {
            return templates;
        }
    }
}
