package com.geeksec.threatdetector.subscription.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 通知模板模型
 * 定义告警通知的消息模板
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationTemplate implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 模板ID
     */
    private String templateId;
    
    /**
     * 模板名称
     */
    private String templateName;
    
    /**
     * 模板类型
     */
    private TemplateType templateType;
    
    /**
     * 适用的渠道类型
     */
    private NotificationChannel.ChannelType channelType;
    
    /**
     * 模板标题
     */
    private String title;
    
    /**
     * 模板内容
     */
    private String content;
    
    /**
     * 模板格式
     */
    private TemplateFormat format;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 模板变量
     */
    private Map<String, String> variables;
    
    /**
     * 模板样式（用于HTML格式）
     */
    private String style;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 使用次数
     */
    private Long usageCount;
    
    /**
     * 模板类型枚举
     */
    public enum TemplateType {
        /** 单个告警模板 */
        SINGLE_ALARM,
        /** 批量告警模板 */
        BATCH_ALARM,
        /** 摘要模板 */
        DIGEST,
        /** 系统通知模板 */
        SYSTEM_NOTIFICATION
    }
    
    /**
     * 模板格式枚举
     */
    public enum TemplateFormat {
        /** 纯文本 */
        TEXT,
        /** HTML */
        HTML,
        /** Markdown */
        MARKDOWN,
        /** JSON */
        JSON
    }
    
    /**
     * 渲染模板
     * 
     * @param context 上下文变量
     * @return 渲染后的内容
     */
    public RenderedTemplate render(Map<String, Object> context) {
        if (!Boolean.TRUE.equals(enabled)) {
            throw new IllegalStateException("模板未启用: " + templateId);
        }
        
        String renderedTitle = renderText(title, context);
        String renderedContent = renderText(content, context);
        
        // 更新使用次数
        this.usageCount = (this.usageCount == null ? 0 : this.usageCount) + 1;
        this.updateTime = LocalDateTime.now();
        
        return RenderedTemplate.builder()
                .templateId(templateId)
                .title(renderedTitle)
                .content(renderedContent)
                .format(format)
                .style(style)
                .renderTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 渲染文本
     * 
     * @param text 模板文本
     * @param context 上下文变量
     * @return 渲染后的文本
     */
    private String renderText(String text, Map<String, Object> context) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        if (context == null || context.isEmpty()) {
            return text;
        }
        
        String result = text;
        
        // 替换变量 ${variableName}
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(text);
        
        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object value = getVariableValue(variableName, context);
            String replacement = value != null ? value.toString() : "";
            result = result.replace(matcher.group(0), replacement);
        }
        
        return result;
    }
    
    /**
     * 获取变量值
     * 
     * @param variableName 变量名
     * @param context 上下文
     * @return 变量值
     */
    private Object getVariableValue(String variableName, Map<String, Object> context) {
        // 支持嵌套属性访问，如 alarm.alarmName
        String[] parts = variableName.split("\\.");
        Object current = context;
        
        for (String part : parts) {
            if (current instanceof Map) {
                current = ((Map<?, ?>) current).get(part);
            } else if (current != null) {
                // 尝试通过反射获取属性值
                try {
                    java.lang.reflect.Field field = current.getClass().getDeclaredField(part);
                    field.setAccessible(true);
                    current = field.get(current);
                } catch (Exception e) {
                    // 尝试getter方法
                    try {
                        String getterName = "get" + part.substring(0, 1).toUpperCase() + part.substring(1);
                        java.lang.reflect.Method getter = current.getClass().getMethod(getterName);
                        current = getter.invoke(current);
                    } catch (Exception ex) {
                        return null;
                    }
                }
            } else {
                return null;
            }
        }
        
        return current;
    }
    
    /**
     * 验证模板是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return templateId != null && !templateId.trim().isEmpty() &&
               templateName != null && !templateName.trim().isEmpty() &&
               content != null && !content.trim().isEmpty() &&
               channelType != null &&
               format != null;
    }
    
    /**
     * 获取模板中的变量列表
     * 
     * @return 变量列表
     */
    public java.util.Set<String> getTemplateVariables() {
        java.util.Set<String> variables = new java.util.HashSet<>();
        
        String text = (title != null ? title : "") + " " + (content != null ? content : "");
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(text);
        
        while (matcher.find()) {
            variables.add(matcher.group(1));
        }
        
        return variables;
    }
    
    /**
     * 渲染结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RenderedTemplate implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 模板ID
         */
        private String templateId;
        
        /**
         * 渲染后的标题
         */
        private String title;
        
        /**
         * 渲染后的内容
         */
        private String content;
        
        /**
         * 模板格式
         */
        private TemplateFormat format;
        
        /**
         * 样式
         */
        private String style;
        
        /**
         * 渲染时间
         */
        private LocalDateTime renderTime;
    }
    
    /**
     * 创建默认的邮件模板
     * 
     * @return 邮件模板
     */
    public static NotificationTemplate createDefaultEmailTemplate() {
        return NotificationTemplate.builder()
                .templateId("default_email")
                .templateName("默认邮件模板")
                .templateType(TemplateType.SINGLE_ALARM)
                .channelType(NotificationChannel.ChannelType.EMAIL)
                .title("【威胁检测】${alarm.alarmName}")
                .content("告警详情：\n" +
                        "告警名称：${alarm.alarmName}\n" +
                        "告警类型：${alarm.alarmType}\n" +
                        "威胁类型：${alarm.threatType}\n" +
                        "源IP：${alarm.srcIp}\n" +
                        "目标IP：${alarm.dstIp}\n" +
                        "发生时间：${alarm.timestamp}\n" +
                        "描述：${alarm.description}")
                .format(TemplateFormat.TEXT)
                .enabled(true)
                .createTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建默认的短信模板
     * 
     * @return 短信模板
     */
    public static NotificationTemplate createDefaultSmsTemplate() {
        return NotificationTemplate.builder()
                .templateId("default_sms")
                .templateName("默认短信模板")
                .templateType(TemplateType.SINGLE_ALARM)
                .channelType(NotificationChannel.ChannelType.SMS)
                .title("威胁检测告警")
                .content("【威胁检测】${alarm.alarmName}，源IP：${alarm.srcIp}，时间：${alarm.timestamp}")
                .format(TemplateFormat.TEXT)
                .enabled(true)
                .createTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建默认的钉钉模板
     * 
     * @return 钉钉模板
     */
    public static NotificationTemplate createDefaultDingTalkTemplate() {
        return NotificationTemplate.builder()
                .templateId("default_dingtalk")
                .templateName("默认钉钉模板")
                .templateType(TemplateType.SINGLE_ALARM)
                .channelType(NotificationChannel.ChannelType.DINGTALK)
                .title("威胁检测告警")
                .content("## 威胁检测告警\n\n" +
                        "**告警名称：** ${alarm.alarmName}\n\n" +
                        "**告警类型：** ${alarm.alarmType}\n\n" +
                        "**威胁类型：** ${alarm.threatType}\n\n" +
                        "**源IP：** ${alarm.srcIp}\n\n" +
                        "**目标IP：** ${alarm.dstIp}\n\n" +
                        "**发生时间：** ${alarm.timestamp}\n\n" +
                        "**描述：** ${alarm.description}")
                .format(TemplateFormat.MARKDOWN)
                .enabled(true)
                .createTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建批量告警模板
     * 
     * @param channelType 渠道类型
     * @return 批量告警模板
     */
    public static NotificationTemplate createBatchTemplate(NotificationChannel.ChannelType channelType) {
        String templateId = "batch_" + channelType.getCode();
        String content;
        
        switch (channelType) {
            case EMAIL:
                content = "批量告警通知：\n共收到 ${alarmCount} 条告警\n\n${alarmList}";
                break;
            case SMS:
                content = "【威胁检测】批量告警，共${alarmCount}条，请及时处理";
                break;
            default:
                content = "批量告警通知：共收到 ${alarmCount} 条告警";
                break;
        }
        
        return NotificationTemplate.builder()
                .templateId(templateId)
                .templateName("批量告警模板")
                .templateType(TemplateType.BATCH_ALARM)
                .channelType(channelType)
                .title("【威胁检测】批量告警通知")
                .content(content)
                .format(TemplateFormat.TEXT)
                .enabled(true)
                .createTime(LocalDateTime.now())
                .build();
    }
}
