package com.geeksec.threatdetector.output.alarm;

import com.geeksec.threatdetector.model.output.Alarm;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;

/**
 * 告警输出管理器
 * 负责将告警事件写入Doris告警表
 *
 * <AUTHOR>
 */
@Slf4j
public class AlarmOutputManager {

    /**
     * 私有构造方法，防止实例化
     */
    private AlarmOutputManager() {
        // 工具类，禁止实例化
    }

    /**
     * 配置告警事件输出
     *
     * @param alarmEventStream 告警事件数据流
     * @param config 配置参数
     */
    public static void configure(DataStream<Alarm> alarmEventStream, ParameterTool config) {
        log.info("配置告警事件输出到Doris");

        // 添加日志输出Sink（用于调试）
        alarmEventStream
                .addSink(new AlarmSink())
                .name("告警事件日志输出")
                .uid("alarm-event-log-sink");

        // TODO: 添加Doris Sink
        // alarmEventStream
        //     .addSink(createDorisSink(config))
        //     .name("告警事件Doris输出")
        //     .uid("alarm-event-doris-sink");

        log.info("告警事件输出配置完成");
    }

    /**
     * 告警输出Sink
     */
    private static class AlarmSink implements SinkFunction<Alarm> {
        private static final long serialVersionUID = 1L;

        @Override
        public void invoke(Alarm value, Context context) {
            log.info("告警事件: ID={}, 类型={}, 级别={}, 源IP={}, 目标IP={}",
                    value.getAlarmId(),
                    value.getAlarmType(),
                    value.getAlarmLevel(),
                    value.getSrcIp(),
                    value.getDstIp());
        }
    }
}
