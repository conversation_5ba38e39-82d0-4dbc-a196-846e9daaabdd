package com.geeksec.threatdetector.exception;

/**
 * 数据处理异常
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
public class DataProcessingException extends ThreatDetectionException {

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public DataProcessingException(String message) {
        super(message);
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause   异常原因
     */
    public DataProcessingException(String message, Throwable cause) {
        super(message, cause);
    }
}
