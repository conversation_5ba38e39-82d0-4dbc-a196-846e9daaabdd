package com.geeksec.threatdetector.output.session;

import com.geeksec.threatdetector.model.output.SessionLabel;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;

/**
 * 会话标签输出管理器
 * 负责将会话标签写入Doris会话表
 *
 * <AUTHOR>
 */
@Slf4j
public class SessionLabelOutputManager {

    /**
     * 私有构造方法，防止实例化
     */
    private SessionLabelOutputManager() {
        // 工具类，禁止实例化
    }

    /**
     * 配置会话标签输出
     *
     * @param sessionLabelStream 会话标签数据流
     * @param config 配置参数
     */
    public static void configure(DataStream<SessionLabel> sessionLabelStream, ParameterTool config) {
        log.info("配置会话标签输出到Doris");

        // 添加日志输出Sink（用于调试）
        sessionLabelStream
                .addSink(new SessionLabelSink())
                .name("会话标签日志输出")
                .uid("session-label-log-sink");

        // TODO: 添加Doris Sink
        // sessionLabelStream
        //     .addSink(createDorisSink(config))
        //     .name("会话标签Doris输出")
        //     .uid("session-label-doris-sink");

        log.info("会话标签输出配置完成");
    }

    /**
     * 会话标签输出Sink
     */
    private static class SessionLabelSink implements SinkFunction<SessionLabel> {
        private static final long serialVersionUID = 1L;

        @Override
        public void invoke(SessionLabel value, Context context) {
            log.info("会话标签: 会话ID={}, 源IP={}, 目标IP={}, 标签={}",
                    value.getSessionId(),
                    value.getSrcIp(),
                    value.getDstIp(),
                    value.getLabelValue());
        }
    }
}
