package com.geeksec.threatdetector.model.input;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 网络事件统一数据模型
 * 包含会话元数据和协议元数据的统一表示
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NetworkEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    // ========== 基础会话信息 ==========
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 源IP地址
     */
    private String srcIp;
    
    /**
     * 目标IP地址
     */
    private String dstIp;
    
    /**
     * 源端口
     */
    private Integer srcPort;
    
    /**
     * 目标端口
     */
    private Integer dstPort;
    
    /**
     * 协议类型
     */
    private String protocol;
    
    /**
     * 事件时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 会话开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 会话结束时间
     */
    private LocalDateTime endTime;

    // ========== 流量统计信息 ==========
    
    /**
     * 上行字节数
     */
    private Long upBytes;
    
    /**
     * 下行字节数
     */
    private Long downBytes;
    
    /**
     * 上行包数
     */
    private Long upPackets;
    
    /**
     * 下行包数
     */
    private Long downPackets;

    // ========== 协议特定信息 ==========
    
    /**
     * HTTP相关信息
     */
    private HttpInfo httpInfo;
    
    /**
     * SSL/TLS相关信息
     */
    private SslInfo sslInfo;
    
    /**
     * DNS相关信息
     */
    private DnsInfo dnsInfo;
    
    /**
     * TCP相关信息
     */
    private TcpInfo tcpInfo;

    // ========== 扩展信息 ==========
    
    /**
     * 原始事件数据
     * 保存原始的Map格式数据，用于特殊处理
     */
    private Map<String, Object> rawData;
    
    /**
     * 事件类型
     */
    private EventType eventType;
    
    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 事件类型枚举
     */
    public enum EventType {
        SESSION,    // 会话事件
        HTTP,       // HTTP事件
        SSL,        // SSL/TLS事件
        DNS,        // DNS事件
        TCP,        // TCP事件
        UDP,        // UDP事件
        OTHER       // 其他类型
    }
}
