package com.geeksec.threatdetector.formatting.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 威胁信息模型
 * 用于存储从知识库获取的威胁情报信息
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ThreatInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 威胁类型
     */
    private String threatType;

    /**
     * 威胁名称
     */
    private String threatName;

    /**
     * 威胁描述
     */
    private String description;

    /**
     * 威胁分类
     */
    private String category;

    /**
     * 威胁严重程度
     */
    private String severity;

    /**
     * 攻击向量列表
     */
    private List<String> attackVectors;

    /**
     * 影响范围
     */
    private String impactScope;

    /**
     * 检测原理
     */
    private String detectionPrinciple;

    /**
     * 技术背景
     */
    private String technicalBackground;

    /**
     * 相关CVE列表
     */
    private List<String> relatedCVEs;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
