package com.geeksec.threatdetector.formatting;

import com.geeksec.threatdetector.formatting.generator.PrincipleGenerator;
import com.geeksec.threatdetector.formatting.generator.ReasonGenerator;
import com.geeksec.threatdetector.formatting.generator.SuggestionGenerator;
import com.geeksec.threatdetector.formatting.knowledge.ThreatKnowledgeBase;
import com.geeksec.threatdetector.formatting.model.AlarmFormattedContent;
import com.geeksec.threatdetector.formatting.model.FormattingContext;
import com.geeksec.threatdetector.model.output.Alarm;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 告警格式化器
 * 统一的告警格式化入口，协调各个生成器完成格式化工作
 * 
 * <AUTHOR>
 */
@Slf4j
public class AlarmFormatter implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 威胁知识库
     */
    private final ThreatKnowledgeBase knowledgeBase;
    
    /**
     * 原因生成器
     */
    private final ReasonGenerator reasonGenerator;
    
    /**
     * 建议生成器
     */
    private final SuggestionGenerator suggestionGenerator;
    
    /**
     * 原理生成器
     */
    private final PrincipleGenerator principleGenerator;
    
    /**
     * 格式化缓存
     */
    private final Map<String, AlarmFormattedContent> formattingCache;
    
    /**
     * 统计信息
     */
    private final AtomicLong totalFormatted = new AtomicLong(0);
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong cacheMisses = new AtomicLong(0);
    private final AtomicLong formattingErrors = new AtomicLong(0);
    
    /**
     * 格式化版本
     */
    private static final String FORMATTING_VERSION = "1.0.0";
    
    /**
     * 构造函数
     */
    public AlarmFormatter() {
        this.knowledgeBase = new ThreatKnowledgeBase();
        this.reasonGenerator = new ReasonGenerator(knowledgeBase);
        this.suggestionGenerator = new SuggestionGenerator(knowledgeBase);
        this.principleGenerator = new PrincipleGenerator(knowledgeBase);
        this.formattingCache = new ConcurrentHashMap<>();
        
        log.info("告警格式化器初始化完成");
    }
    
    /**
     * 格式化告警
     * 
     * @param alarm 告警信息
     * @return 格式化内容
     */
    public AlarmFormattedContent formatAlarm(Alarm alarm) {
        return formatAlarm(alarm, FormattingContext.createDefault());
    }
    
    /**
     * 格式化告警
     * 
     * @param alarm 告警信息
     * @param context 格式化上下文
     * @return 格式化内容
     */
    public AlarmFormattedContent formatAlarm(Alarm alarm, FormattingContext context) {
        if (alarm == null) {
            log.warn("告警信息为空，无法格式化");
            return null;
        }
        
        if (context == null) {
            context = FormattingContext.createDefault();
        }
        
        totalFormatted.incrementAndGet();
        
        try {
            // 检查缓存
            String cacheKey = generateCacheKey(alarm, context);
            AlarmFormattedContent cached = formattingCache.get(cacheKey);
            if (cached != null) {
                cacheHits.incrementAndGet();
                log.debug("使用缓存的格式化内容: {}", alarm.getAlarmId());
                return cached;
            }
            
            cacheMisses.incrementAndGet();
            
            // 执行格式化
            AlarmFormattedContent formattedContent = performFormatting(alarm, context);
            
            // 缓存结果
            if (formattedContent != null) {
                formattingCache.put(cacheKey, formattedContent);
            }
            
            log.debug("告警格式化完成: {}", alarm.getAlarmId());
            return formattedContent;
            
        } catch (Exception e) {
            formattingErrors.incrementAndGet();
            log.error("告警格式化失败: {}, 错误: {}", alarm.getAlarmId(), e.getMessage(), e);
            return createErrorFormattedContent(alarm, context, e);
        }
    }
    
    /**
     * 执行格式化
     * 
     * @param alarm 告警信息
     * @param context 格式化上下文
     * @return 格式化内容
     */
    private AlarmFormattedContent performFormatting(Alarm alarm, FormattingContext context) {
        // 生成告警摘要
        AlarmFormattedContent.AlarmSummary summary = generateAlarmSummary(alarm, context);
        
        // 生成原因分析
        AlarmFormattedContent.AlarmReasonAnalysis reasonAnalysis = null;
        if (shouldIncludeReasonAnalysis(context)) {
            reasonAnalysis = reasonGenerator.generateReasonAnalysis(alarm, context);
        }
        
        // 生成处理建议
        AlarmFormattedContent.HandlingSuggestions handlingSuggestions = null;
        if (shouldIncludeHandlingSuggestions(context)) {
            handlingSuggestions = suggestionGenerator.generateHandlingSuggestions(alarm, context);
        }
        
        // 生成原理说明
        AlarmFormattedContent.PrincipleExplanation principleExplanation = null;
        if (shouldIncludePrincipleExplanation(context)) {
            principleExplanation = principleGenerator.generatePrincipleExplanation(alarm, context);
        }
        
        // 生成影响分析
        AlarmFormattedContent.ImpactAnalysis impactAnalysis = null;
        if (shouldIncludeImpactAnalysis(context)) {
            impactAnalysis = generateImpactAnalysis(alarm, context);
        }
        
        // 生成相关信息
        AlarmFormattedContent.RelatedInformation relatedInformation = null;
        if (shouldIncludeRelatedInformation(context)) {
            relatedInformation = generateRelatedInformation(alarm, context);
        }
        
        return AlarmFormattedContent.builder()
                .alarmId(alarm.getAlarmId())
                .strategy(context.getStrategy())
                .language(context.getLanguage())
                .summary(summary)
                .reasonAnalysis(reasonAnalysis)
                .handlingSuggestions(handlingSuggestions)
                .principleExplanation(principleExplanation)
                .impactAnalysis(impactAnalysis)
                .relatedInformation(relatedInformation)
                .formattedTime(LocalDateTime.now())
                .formattingVersion(FORMATTING_VERSION)
                .build();
    }
    
    /**
     * 生成告警摘要
     * 
     * @param alarm 告警信息
     * @param context 格式化上下文
     * @return 告警摘要
     */
    private AlarmFormattedContent.AlarmSummary generateAlarmSummary(Alarm alarm, FormattingContext context) {
        // 生成简短描述
        String briefDescription = generateBriefDescription(alarm, context);
        
        // 生成详细描述
        String detailedDescription = generateDetailedDescription(alarm, context);
        
        // 生成关键信息
        Map<String, String> keyInformation = generateKeyInformation(alarm, context);
        
        // 生成严重程度描述
        String severityDescription = generateSeverityDescription(alarm, context);
        
        // 生成紧急程度描述
        String urgencyDescription = generateUrgencyDescription(alarm, context);
        
        return AlarmFormattedContent.AlarmSummary.builder()
                .briefDescription(briefDescription)
                .detailedDescription(detailedDescription)
                .keyInformation(keyInformation)
                .severityDescription(severityDescription)
                .urgencyDescription(urgencyDescription)
                .build();
    }
    
    /**
     * 生成简短描述
     */
    private String generateBriefDescription(Alarm alarm, FormattingContext context) {
        StringBuilder description = new StringBuilder();
        
        if (context.getStrategy() == AlarmFormattedContent.FormattingStrategy.BRIEF) {
            // 简洁格式
            description.append(alarm.getAlarmName() != null ? alarm.getAlarmName() : "安全告警");
            if (alarm.getSrcIp() != null) {
                description.append("，源IP: ").append(alarm.getSrcIp());
            }
        } else {
            // 标准格式
            description.append("检测到").append(alarm.getThreatType()).append("威胁");
            if (alarm.getAlarmName() != null) {
                description.append("：").append(alarm.getAlarmName());
            }
            if (alarm.getSrcIp() != null && alarm.getDstIp() != null) {
                description.append("，涉及从").append(alarm.getSrcIp())
                          .append("到").append(alarm.getDstIp()).append("的通信");
            }
        }
        
        return description.toString();
    }
    
    /**
     * 生成详细描述
     */
    private String generateDetailedDescription(Alarm alarm, FormattingContext context) {
        if (context.getStrategy() == AlarmFormattedContent.FormattingStrategy.BRIEF) {
            return null; // 简洁格式不包含详细描述
        }
        
        StringBuilder description = new StringBuilder();
        
        description.append("系统检测到").append(alarm.getThreatType()).append("类型的安全威胁。");
        
        if (alarm.getDescription() != null) {
            description.append(" ").append(alarm.getDescription());
        }
        
        if (alarm.getDetectorType() != null) {
            description.append(" 该告警由").append(alarm.getDetectorDisplayName()).append("检测器生成");
        }
        
        if (alarm.getConfidence() != null) {
            description.append("，检测置信度为").append(String.format("%.2f", alarm.getConfidence()));
        }
        
        description.append("。");
        
        if (alarm.getTimestamp() != null) {
            description.append(" 告警发生时间：").append(alarm.getTimestamp());
        }
        
        return description.toString();
    }
    
    /**
     * 生成关键信息
     */
    private Map<String, String> generateKeyInformation(Alarm alarm, FormattingContext context) {
        Map<String, String> keyInfo = new ConcurrentHashMap<>();
        
        if (alarm.getThreatType() != null) {
            keyInfo.put("威胁类型", alarm.getThreatType());
        }
        
        if (alarm.getAlarmType() != null) {
            keyInfo.put("告警类型", alarm.getAlarmType());
        }
        
        if (alarm.getSrcIp() != null) {
            keyInfo.put("源IP", alarm.getSrcIp());
        }
        
        if (alarm.getDstIp() != null) {
            keyInfo.put("目标IP", alarm.getDstIp());
        }
        
        if (alarm.getProtocol() != null) {
            keyInfo.put("协议", alarm.getProtocol());
        }
        
        if (alarm.getConfidence() != null) {
            keyInfo.put("置信度", String.format("%.2f", alarm.getConfidence()));
        }
        
        if (alarm.getDetectorType() != null) {
            keyInfo.put("检测器", alarm.getDetectorDisplayName());
        }
        
        return keyInfo;
    }
    
    /**
     * 生成严重程度描述
     */
    private String generateSeverityDescription(Alarm alarm, FormattingContext context) {
        String alarmType = alarm.getAlarmType();
        Double confidence = alarm.getConfidence();
        
        if (alarmType != null && alarmType.contains("高危")) {
            return "高严重程度 - 可能造成重大安全影响";
        } else if (alarmType != null && alarmType.contains("中危")) {
            return "中等严重程度 - 需要及时关注和处理";
        } else if (alarmType != null && alarmType.contains("低危")) {
            return "低严重程度 - 建议定期检查";
        } else if (confidence != null) {
            if (confidence >= 0.9) {
                return "高严重程度 - 高置信度威胁";
            } else if (confidence >= 0.7) {
                return "中等严重程度 - 较高置信度威胁";
            } else {
                return "低严重程度 - 中等置信度威胁";
            }
        }
        
        return "严重程度待评估";
    }
    
    /**
     * 生成紧急程度描述
     */
    private String generateUrgencyDescription(Alarm alarm, FormattingContext context) {
        String threatType = alarm.getThreatType();
        Double confidence = alarm.getConfidence();
        
        // 根据威胁类型判断紧急程度
        switch (threatType) {
            case "恶意软件":
            case "Webshell":
                return "紧急 - 建议立即处理";
            case "DNS隧道":
            case "加密工具":
                return "高优先级 - 建议4小时内处理";
            case "网络扫描":
                return "中等优先级 - 建议24小时内处理";
            default:
                if (confidence != null && confidence >= 0.9) {
                    return "高优先级 - 高置信度告警";
                } else {
                    return "中等优先级 - 建议及时处理";
                }
        }
    }
    
    /**
     * 生成影响分析
     */
    private AlarmFormattedContent.ImpactAnalysis generateImpactAnalysis(Alarm alarm, FormattingContext context) {
        String threatType = alarm.getThreatType();
        
        String businessImpact = generateBusinessImpact(alarm, context);
        String technicalImpact = generateTechnicalImpact(alarm, context);
        String securityImpact = generateSecurityImpact(alarm, context);
        String complianceImpact = generateComplianceImpact(alarm, context);
        String impactScope = generateImpactScope(alarm, context);
        String potentialLoss = generatePotentialLoss(alarm, context);
        
        return AlarmFormattedContent.ImpactAnalysis.builder()
                .businessImpact(businessImpact)
                .technicalImpact(technicalImpact)
                .securityImpact(securityImpact)
                .complianceImpact(complianceImpact)
                .impactScope(impactScope)
                .potentialLoss(potentialLoss)
                .build();
    }
    
    /**
     * 生成业务影响
     */
    private String generateBusinessImpact(Alarm alarm, FormattingContext context) {
        String threatType = alarm.getThreatType();
        
        switch (threatType) {
            case "恶意软件":
                return "可能导致业务系统中断、数据丢失、服务不可用等严重业务影响";
            case "Webshell":
                return "可能导致Web服务被控制、数据泄露、业务信誉受损";
            case "DNS隧道":
                return "可能导致敏感数据泄露、网络性能下降";
            case "网络扫描":
                return "通常不直接影响业务，但可能是攻击的前奏";
            default:
                return "可能对业务连续性和数据安全造成影响";
        }
    }
    
    /**
     * 生成技术影响
     */
    private String generateTechnicalImpact(Alarm alarm, FormattingContext context) {
        String threatType = alarm.getThreatType();
        
        switch (threatType) {
            case "恶意软件":
                return "可能导致系统性能下降、文件损坏、网络拥塞";
            case "Webshell":
                return "可能导致Web服务器被控制、文件系统被修改";
            case "DNS隧道":
                return "可能导致DNS服务负载增加、网络流量异常";
            case "网络扫描":
                return "可能导致网络负载增加、日志量激增";
            default:
                return "可能对系统和网络的正常运行造成影响";
        }
    }
    
    /**
     * 生成安全影响
     */
    private String generateSecurityImpact(Alarm alarm, FormattingContext context) {
        return "可能导致安全防护被绕过、攻击面扩大、安全态势恶化";
    }
    
    /**
     * 生成合规影响
     */
    private String generateComplianceImpact(Alarm alarm, FormattingContext context) {
        return "可能违反相关安全合规要求，需要及时处理并记录";
    }
    
    /**
     * 生成影响范围
     */
    private String generateImpactScope(Alarm alarm, FormattingContext context) {
        if (alarm.getSrcIp() != null && alarm.getDstIp() != null) {
            return String.format("影响范围包括源IP %s 和目标IP %s 相关的系统和服务", 
                    alarm.getSrcIp(), alarm.getDstIp());
        } else if (alarm.getSrcIp() != null) {
            return String.format("影响范围主要涉及IP %s 相关的系统", alarm.getSrcIp());
        } else {
            return "影响范围需要进一步分析确定";
        }
    }
    
    /**
     * 生成潜在损失
     */
    private String generatePotentialLoss(Alarm alarm, FormattingContext context) {
        String threatType = alarm.getThreatType();
        
        switch (threatType) {
            case "恶意软件":
                return "可能造成数据丢失、系统重建成本、业务中断损失";
            case "Webshell":
                return "可能造成数据泄露、系统重建、法律风险";
            case "DNS隧道":
                return "可能造成敏感信息泄露、知识产权损失";
            default:
                return "具体损失需要根据实际影响进行评估";
        }
    }
    
    /**
     * 生成相关信息
     */
    private AlarmFormattedContent.RelatedInformation generateRelatedInformation(Alarm alarm, FormattingContext context) {
        return AlarmFormattedContent.RelatedInformation.builder()
                .relatedAlarms(new ArrayList<>())
                .historicalEvents(new ArrayList<>())
                .threatIntelligence(new ArrayList<>())
                .relatedAssets(new ArrayList<>())
                .externalLinks(new ArrayList<>())
                .build();
    }
    
    /**
     * 判断是否包含原因分析
     */
    private boolean shouldIncludeReasonAnalysis(FormattingContext context) {
        return context.getStrategy() != AlarmFormattedContent.FormattingStrategy.BRIEF;
    }
    
    /**
     * 判断是否包含处理建议
     */
    private boolean shouldIncludeHandlingSuggestions(FormattingContext context) {
        return context.getUserPreferences() == null || 
               Boolean.TRUE.equals(context.getUserPreferences().getIncludeSuggestions());
    }
    
    /**
     * 判断是否包含原理说明
     */
    private boolean shouldIncludePrincipleExplanation(FormattingContext context) {
        return context.getStrategy() == AlarmFormattedContent.FormattingStrategy.TECHNICAL ||
               context.getStrategy() == AlarmFormattedContent.FormattingStrategy.DETAILED ||
               (context.getUserPreferences() != null && 
                Boolean.TRUE.equals(context.getUserPreferences().getIncludePrinciple()));
    }
    
    /**
     * 判断是否包含影响分析
     */
    private boolean shouldIncludeImpactAnalysis(FormattingContext context) {
        return context.getStrategy() == AlarmFormattedContent.FormattingStrategy.EXECUTIVE ||
               context.getStrategy() == AlarmFormattedContent.FormattingStrategy.DETAILED ||
               (context.getUserPreferences() != null && 
                Boolean.TRUE.equals(context.getUserPreferences().getIncludeImpactAnalysis()));
    }
    
    /**
     * 判断是否包含相关信息
     */
    private boolean shouldIncludeRelatedInformation(FormattingContext context) {
        return context.getStrategy() == AlarmFormattedContent.FormattingStrategy.DETAILED ||
               (context.getUserPreferences() != null && 
                Boolean.TRUE.equals(context.getUserPreferences().getIncludeRelatedInfo()));
    }
    
    /**
     * 生成缓存键
     */
    private String generateCacheKey(Alarm alarm, FormattingContext context) {
        return String.format("%s_%s_%s", 
                alarm.getAlarmId(), 
                context.getStrategy().getCode(),
                context.getLanguage());
    }
    
    /**
     * 创建错误格式化内容
     */
    private AlarmFormattedContent createErrorFormattedContent(Alarm alarm, FormattingContext context, Exception error) {
        AlarmFormattedContent.AlarmSummary errorSummary = AlarmFormattedContent.AlarmSummary.builder()
                .briefDescription("格式化失败：" + alarm.getAlarmName())
                .detailedDescription("告警格式化过程中发生错误：" + error.getMessage())
                .build();
        
        return AlarmFormattedContent.builder()
                .alarmId(alarm.getAlarmId())
                .strategy(context.getStrategy())
                .language(context.getLanguage())
                .summary(errorSummary)
                .formattedTime(LocalDateTime.now())
                .formattingVersion(FORMATTING_VERSION)
                .build();
    }
    
    /**
     * 获取格式化统计信息
     * 
     * @return 统计信息
     */
    public FormattingStatistics getStatistics() {
        return new FormattingStatistics(
                totalFormatted.get(),
                cacheHits.get(),
                cacheMisses.get(),
                formattingErrors.get(),
                formattingCache.size()
        );
    }
    
    /**
     * 清理缓存
     */
    public void clearCache() {
        formattingCache.clear();
        log.info("格式化缓存已清理");
    }
    
    /**
     * 格式化统计信息
     */
    public static class FormattingStatistics implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private final long totalFormatted;
        private final long cacheHits;
        private final long cacheMisses;
        private final long formattingErrors;
        private final int cacheSize;
        
        public FormattingStatistics(long totalFormatted, long cacheHits, long cacheMisses, 
                                  long formattingErrors, int cacheSize) {
            this.totalFormatted = totalFormatted;
            this.cacheHits = cacheHits;
            this.cacheMisses = cacheMisses;
            this.formattingErrors = formattingErrors;
            this.cacheSize = cacheSize;
        }
        
        public double getCacheHitRate() {
            long totalRequests = cacheHits + cacheMisses;
            return totalRequests > 0 ? (double) cacheHits / totalRequests : 0.0;
        }
        
        public double getErrorRate() {
            return totalFormatted > 0 ? (double) formattingErrors / totalFormatted : 0.0;
        }
        
        // Getters
        public long getTotalFormatted() { return totalFormatted; }
        public long getCacheHits() { return cacheHits; }
        public long getCacheMisses() { return cacheMisses; }
        public long getFormattingErrors() { return formattingErrors; }
        public int getCacheSize() { return cacheSize; }
    }
}
