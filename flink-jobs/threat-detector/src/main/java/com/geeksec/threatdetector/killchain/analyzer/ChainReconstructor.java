package com.geeksec.threatdetector.killchain.analyzer;

import com.geeksec.threatdetector.killchain.model.AttackChainAnalysis;
import com.geeksec.threatdetector.killchain.model.AttackChainEvent;
import com.geeksec.threatdetector.killchain.model.CyberKillChainStage;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 攻击链重建器
 * 负责重建和分析攻击链的结构和时间线
 * 
 * <AUTHOR>
 */
@Slf4j
public class ChainReconstructor implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 重建攻击链
     * 
     * @param attackChain 攻击链分析结果
     */
    public void reconstructChain(AttackChainAnalysis attackChain) {
        if (attackChain == null || attackChain.getEvents() == null || attackChain.getEvents().isEmpty()) {
            return;
        }
        
        try {
            // 1. 重建时间线
            reconstructTimeline(attackChain);
            
            // 2. 分析阶段分布
            analyzeStageDistribution(attackChain);
            
            // 3. 识别攻击路径
            identifyAttackPath(attackChain);
            
            // 4. 分析攻击模式
            analyzeAttackPattern(attackChain);
            
            log.debug("攻击链重建完成: {}", attackChain.getAttackChainId());
            
        } catch (Exception e) {
            log.error("攻击链重建失败: {}, 错误: {}", attackChain.getAttackChainId(), e.getMessage(), e);
        }
    }
    
    /**
     * 重建时间线
     * 
     * @param attackChain 攻击链分析结果
     */
    private void reconstructTimeline(AttackChainAnalysis attackChain) {
        List<AttackChainEvent> events = attackChain.getEvents();
        
        // 按时间排序事件
        events.sort(Comparator.comparing(AttackChainEvent::getTimestamp));
        
        // 计算时间范围
        LocalDateTime startTime = events.get(0).getTimestamp();
        LocalDateTime endTime = events.get(events.size() - 1).getTimestamp();
        
        // 计算持续时间
        long durationSeconds = java.time.Duration.between(startTime, endTime).getSeconds();
        
        // 分析各阶段的时间分布
        Map<CyberKillChainStage, AttackChainAnalysis.AttackChainTimeline.StageTimeInfo> stageTimeDistribution = 
                analyzeStageTimeDistribution(events);
        
        // 创建时间线事件
        List<AttackChainAnalysis.AttackChainTimeline.TimelineEvent> timelineEvents = 
                createTimelineEvents(events);
        
        // 构建时间线
        AttackChainAnalysis.AttackChainTimeline timeline = AttackChainAnalysis.AttackChainTimeline.builder()
                .startTime(startTime)
                .endTime(endTime)
                .durationSeconds(durationSeconds)
                .stageTimeDistribution(stageTimeDistribution)
                .timelineEvents(timelineEvents)
                .build();
        
        attackChain.setTimeline(timeline);
    }
    
    /**
     * 分析各阶段的时间分布
     * 
     * @param events 事件列表
     * @return 阶段时间分布
     */
    private Map<CyberKillChainStage, AttackChainAnalysis.AttackChainTimeline.StageTimeInfo> 
            analyzeStageTimeDistribution(List<AttackChainEvent> events) {
        
        Map<CyberKillChainStage, AttackChainAnalysis.AttackChainTimeline.StageTimeInfo> distribution = 
                new HashMap<>();
        
        // 按阶段分组事件
        Map<CyberKillChainStage, List<AttackChainEvent>> eventsByStage = events.stream()
                .filter(e -> e.getKillChainStage() != null)
                .collect(Collectors.groupingBy(AttackChainEvent::getKillChainStage));
        
        // 计算每个阶段的时间信息
        for (Map.Entry<CyberKillChainStage, List<AttackChainEvent>> entry : eventsByStage.entrySet()) {
            CyberKillChainStage stage = entry.getKey();
            List<AttackChainEvent> stageEvents = entry.getValue();
            
            LocalDateTime firstEventTime = stageEvents.stream()
                    .map(AttackChainEvent::getTimestamp)
                    .min(LocalDateTime::compareTo)
                    .orElse(null);
            
            LocalDateTime lastEventTime = stageEvents.stream()
                    .map(AttackChainEvent::getTimestamp)
                    .max(LocalDateTime::compareTo)
                    .orElse(null);
            
            long stageDuration = 0;
            if (firstEventTime != null && lastEventTime != null) {
                stageDuration = java.time.Duration.between(firstEventTime, lastEventTime).getSeconds();
            }
            
            AttackChainAnalysis.AttackChainTimeline.StageTimeInfo stageTimeInfo = 
                    AttackChainAnalysis.AttackChainTimeline.StageTimeInfo.builder()
                            .firstEventTime(firstEventTime)
                            .lastEventTime(lastEventTime)
                            .durationSeconds(stageDuration)
                            .eventCount(stageEvents.size())
                            .build();
            
            distribution.put(stage, stageTimeInfo);
        }
        
        return distribution;
    }
    
    /**
     * 创建时间线事件
     * 
     * @param events 事件列表
     * @return 时间线事件列表
     */
    private List<AttackChainAnalysis.AttackChainTimeline.TimelineEvent> createTimelineEvents(
            List<AttackChainEvent> events) {
        
        return events.stream()
                .map(event -> AttackChainAnalysis.AttackChainTimeline.TimelineEvent.builder()
                        .timestamp(event.getTimestamp())
                        .eventId(event.getEventId())
                        .stage(event.getKillChainStage())
                        .description(event.getBriefDescription())
                        .severity(event.getSeverity() != null ? event.getSeverity().getDescription() : "未知")
                        .build())
                .collect(Collectors.toList());
    }
    
    /**
     * 分析阶段分布
     * 
     * @param attackChain 攻击链分析结果
     */
    private void analyzeStageDistribution(AttackChainAnalysis attackChain) {
        List<AttackChainEvent> events = attackChain.getEvents();
        
        // 统计各阶段的事件数量
        Map<CyberKillChainStage, Long> stageEventCounts = events.stream()
                .filter(e -> e.getKillChainStage() != null)
                .collect(Collectors.groupingBy(
                        AttackChainEvent::getKillChainStage,
                        Collectors.counting()
                ));
        
        // 识别主要攻击阶段
        CyberKillChainStage dominantStage = stageEventCounts.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);
        
        // 计算阶段覆盖率
        double stageCoverage = (double) stageEventCounts.size() / CyberKillChainStage.values().length;
        
        log.debug("攻击链 {} 阶段分布: 主要阶段={}, 覆盖率={:.2f}, 分布={}", 
                attackChain.getAttackChainId(), dominantStage, stageCoverage, stageEventCounts);
    }
    
    /**
     * 识别攻击路径
     * 
     * @param attackChain 攻击链分析结果
     */
    private void identifyAttackPath(AttackChainAnalysis attackChain) {
        List<AttackChainEvent> events = attackChain.getEvents();
        
        // 按时间排序并提取阶段序列
        List<CyberKillChainStage> stageSequence = events.stream()
                .sorted(Comparator.comparing(AttackChainEvent::getTimestamp))
                .map(AttackChainEvent::getKillChainStage)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        
        // 分析攻击路径特征
        AttackPathCharacteristics pathCharacteristics = analyzeAttackPath(stageSequence);
        
        // 识别攻击模式
        String attackPattern = identifyAttackPattern(stageSequence, events);
        
        log.debug("攻击链 {} 路径分析: 阶段序列={}, 模式={}, 特征={}", 
                attackChain.getAttackChainId(), stageSequence, attackPattern, pathCharacteristics);
    }
    
    /**
     * 分析攻击路径特征
     * 
     * @param stageSequence 阶段序列
     * @return 攻击路径特征
     */
    private AttackPathCharacteristics analyzeAttackPath(List<CyberKillChainStage> stageSequence) {
        AttackPathCharacteristics characteristics = new AttackPathCharacteristics();
        
        // 分析路径完整性
        characteristics.isComplete = stageSequence.size() >= 5; // 至少包含5个阶段
        
        // 分析路径顺序性
        characteristics.isSequential = isSequentialPath(stageSequence);
        
        // 分析路径复杂度
        characteristics.complexity = calculatePathComplexity(stageSequence);
        
        // 分析跳跃模式
        characteristics.hasStageJumps = hasStageJumps(stageSequence);
        
        return characteristics;
    }
    
    /**
     * 判断是否为顺序路径
     * 
     * @param stageSequence 阶段序列
     * @return 是否顺序
     */
    private boolean isSequentialPath(List<CyberKillChainStage> stageSequence) {
        for (int i = 1; i < stageSequence.size(); i++) {
            CyberKillChainStage current = stageSequence.get(i);
            CyberKillChainStage previous = stageSequence.get(i - 1);
            
            if (current.getStageNumber() <= previous.getStageNumber()) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 计算路径复杂度
     * 
     * @param stageSequence 阶段序列
     * @return 复杂度评分
     */
    private double calculatePathComplexity(List<CyberKillChainStage> stageSequence) {
        if (stageSequence.isEmpty()) {
            return 0.0;
        }
        
        // 基于阶段数量和跨度计算复杂度
        double stageCount = stageSequence.size();
        double stageSpan = stageSequence.get(stageSequence.size() - 1).getStageNumber() - 
                          stageSequence.get(0).getStageNumber() + 1;
        
        return (stageCount / 7.0) * (stageSpan / 7.0);
    }
    
    /**
     * 检查是否有阶段跳跃
     * 
     * @param stageSequence 阶段序列
     * @return 是否有跳跃
     */
    private boolean hasStageJumps(List<CyberKillChainStage> stageSequence) {
        for (int i = 1; i < stageSequence.size(); i++) {
            CyberKillChainStage current = stageSequence.get(i);
            CyberKillChainStage previous = stageSequence.get(i - 1);
            
            // 如果阶段号差距大于1，说明有跳跃
            if (current.getStageNumber() - previous.getStageNumber() > 1) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 识别攻击模式
     * 
     * @param stageSequence 阶段序列
     * @param events 事件列表
     * @return 攻击模式
     */
    private String identifyAttackPattern(List<CyberKillChainStage> stageSequence, List<AttackChainEvent> events) {
        // 基于阶段序列和事件特征识别攻击模式
        
        // APT模式：包含完整的攻击链
        if (stageSequence.size() >= 6 && isSequentialPath(stageSequence)) {
            return "APT攻击模式";
        }
        
        // 勒索软件模式：快速从投递到目标行动
        if (containsStages(stageSequence, CyberKillChainStage.DELIVERY, CyberKillChainStage.ACTIONS_ON_OBJECTIVES) &&
            hasRansomwareIndicators(events)) {
            return "勒索软件攻击模式";
        }
        
        // 网络钓鱼模式：主要在前期阶段
        if (stageSequence.stream().allMatch(CyberKillChainStage::isEarlyStage) &&
            hasPhishingIndicators(events)) {
            return "网络钓鱼攻击模式";
        }
        
        // 内部威胁模式：跳过前期阶段
        if (!containsStages(stageSequence, CyberKillChainStage.RECONNAISSANCE, CyberKillChainStage.DELIVERY)) {
            return "内部威胁模式";
        }
        
        // 扫描探测模式：主要在侦察阶段
        if (stageSequence.size() == 1 && stageSequence.get(0) == CyberKillChainStage.RECONNAISSANCE) {
            return "扫描探测模式";
        }
        
        return "未知攻击模式";
    }
    
    /**
     * 检查是否包含指定阶段
     * 
     * @param stageSequence 阶段序列
     * @param stages 要检查的阶段
     * @return 是否包含
     */
    private boolean containsStages(List<CyberKillChainStage> stageSequence, CyberKillChainStage... stages) {
        for (CyberKillChainStage stage : stages) {
            if (stageSequence.contains(stage)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查是否有勒索软件指标
     * 
     * @param events 事件列表
     * @return 是否有勒索软件指标
     */
    private boolean hasRansomwareIndicators(List<AttackChainEvent> events) {
        return events.stream().anyMatch(event -> {
            String threatType = event.getThreatType();
            String description = event.getDescription();
            
            return (threatType != null && threatType.contains("勒索")) ||
                   (description != null && (description.contains("加密") || description.contains("勒索")));
        });
    }
    
    /**
     * 检查是否有钓鱼指标
     * 
     * @param events 事件列表
     * @return 是否有钓鱼指标
     */
    private boolean hasPhishingIndicators(List<AttackChainEvent> events) {
        return events.stream().anyMatch(event -> {
            String threatType = event.getThreatType();
            String description = event.getDescription();
            
            return (threatType != null && threatType.contains("钓鱼")) ||
                   (description != null && (description.contains("钓鱼") || description.contains("邮件")));
        });
    }
    
    /**
     * 分析攻击模式
     * 
     * @param attackChain 攻击链分析结果
     */
    private void analyzeAttackPattern(AttackChainAnalysis attackChain) {
        List<AttackChainEvent> events = attackChain.getEvents();
        
        // 分析攻击者行为模式
        AttackerBehaviorPattern behaviorPattern = analyzeAttackerBehavior(events);
        
        // 分析攻击时间模式
        AttackTimePattern timePattern = analyzeAttackTimePattern(events);
        
        // 分析攻击目标模式
        AttackTargetPattern targetPattern = analyzeAttackTargetPattern(events);
        
        log.debug("攻击链 {} 模式分析: 行为模式={}, 时间模式={}, 目标模式={}", 
                attackChain.getAttackChainId(), behaviorPattern, timePattern, targetPattern);
    }
    
    /**
     * 分析攻击者行为模式
     * 
     * @param events 事件列表
     * @return 行为模式
     */
    private AttackerBehaviorPattern analyzeAttackerBehavior(List<AttackChainEvent> events) {
        AttackerBehaviorPattern pattern = new AttackerBehaviorPattern();
        
        // 分析攻击频率
        pattern.attackFrequency = calculateAttackFrequency(events);
        
        // 分析攻击持续性
        pattern.persistence = calculateAttackPersistence(events);
        
        // 分析攻击复杂度
        pattern.sophistication = calculateAttackSophistication(events);
        
        return pattern;
    }
    
    /**
     * 分析攻击时间模式
     * 
     * @param events 事件列表
     * @return 时间模式
     */
    private AttackTimePattern analyzeAttackTimePattern(List<AttackChainEvent> events) {
        AttackTimePattern pattern = new AttackTimePattern();
        
        // 分析攻击时间分布
        Map<Integer, Long> hourDistribution = events.stream()
                .collect(Collectors.groupingBy(
                        e -> e.getTimestamp().getHour(),
                        Collectors.counting()
                ));
        
        pattern.peakHours = hourDistribution.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(-1);
        
        return pattern;
    }
    
    /**
     * 分析攻击目标模式
     * 
     * @param events 事件列表
     * @return 目标模式
     */
    private AttackTargetPattern analyzeAttackTargetPattern(List<AttackChainEvent> events) {
        AttackTargetPattern pattern = new AttackTargetPattern();
        
        // 分析目标IP分布
        Set<String> targetIps = events.stream()
                .map(AttackChainEvent::getTargetIp)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        pattern.targetCount = targetIps.size();
        pattern.isTargeted = targetIps.size() <= 3; // 目标数量少表示定向攻击
        
        return pattern;
    }
    
    /**
     * 计算攻击频率
     * 
     * @param events 事件列表
     * @return 攻击频率
     */
    private double calculateAttackFrequency(List<AttackChainEvent> events) {
        if (events.size() < 2) {
            return 0.0;
        }
        
        LocalDateTime startTime = events.get(0).getTimestamp();
        LocalDateTime endTime = events.get(events.size() - 1).getTimestamp();
        long durationMinutes = java.time.Duration.between(startTime, endTime).toMinutes();
        
        return durationMinutes > 0 ? (double) events.size() / durationMinutes : 0.0;
    }
    
    /**
     * 计算攻击持续性
     * 
     * @param events 事件列表
     * @return 持续性评分
     */
    private double calculateAttackPersistence(List<AttackChainEvent> events) {
        if (events.size() < 2) {
            return 0.0;
        }
        
        LocalDateTime startTime = events.get(0).getTimestamp();
        LocalDateTime endTime = events.get(events.size() - 1).getTimestamp();
        long durationHours = java.time.Duration.between(startTime, endTime).toHours();
        
        // 持续时间越长，持续性评分越高
        return Math.min(durationHours / 24.0, 1.0); // 最大1.0
    }
    
    /**
     * 计算攻击复杂度
     * 
     * @param events 事件列表
     * @return 复杂度评分
     */
    private double calculateAttackSophistication(List<AttackChainEvent> events) {
        // 基于阶段数量、威胁类型多样性等计算复杂度
        Set<CyberKillChainStage> uniqueStages = events.stream()
                .map(AttackChainEvent::getKillChainStage)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        Set<String> uniqueThreatTypes = events.stream()
                .map(AttackChainEvent::getThreatType)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        double stageComplexity = (double) uniqueStages.size() / 7.0;
        double threatComplexity = Math.min((double) uniqueThreatTypes.size() / 5.0, 1.0);
        
        return (stageComplexity + threatComplexity) / 2.0;
    }
    
    // 内部类定义
    private static class AttackPathCharacteristics {
        boolean isComplete;
        boolean isSequential;
        double complexity;
        boolean hasStageJumps;
    }
    
    private static class AttackerBehaviorPattern {
        double attackFrequency;
        double persistence;
        double sophistication;
    }
    
    private static class AttackTimePattern {
        int peakHours;
    }
    
    private static class AttackTargetPattern {
        int targetCount;
        boolean isTargeted;
    }
}
