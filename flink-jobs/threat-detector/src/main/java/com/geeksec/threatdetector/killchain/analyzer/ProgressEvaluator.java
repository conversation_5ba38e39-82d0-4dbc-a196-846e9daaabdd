package com.geeksec.threatdetector.killchain.analyzer;

import com.geeksec.threatdetector.killchain.model.AttackChainAnalysis;
import com.geeksec.threatdetector.killchain.model.AttackChainEvent;
import com.geeksec.threatdetector.killchain.model.CyberKillChainStage;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 攻击进展评估器
 * 负责评估攻击在Cyber Kill Chain中的进展情况
 * 
 * <AUTHOR>
 */
@Slf4j
public class ProgressEvaluator implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 评估攻击进展
     * 
     * @param attackChain 攻击链分析结果
     */
    public void evaluateProgress(AttackChainAnalysis attackChain) {
        if (attackChain == null || attackChain.getEvents() == null || attackChain.getEvents().isEmpty()) {
            return;
        }
        
        try {
            // 1. 分析已完成的阶段
            Set<CyberKillChainStage> completedStages = analyzeCompletedStages(attackChain.getEvents());
            
            // 2. 确定当前攻击阶段
            CyberKillChainStage currentStage = determineCurrentStage(attackChain.getEvents());
            
            // 3. 计算攻击进展百分比
            double progressPercentage = calculateProgressPercentage(completedStages, currentStage);
            
            // 4. 预测下一步可能的攻击行为
            List<String> nextPossibleActions = predictNextActions(currentStage, completedStages);
            
            // 5. 评估攻击成功概率
            double successProbability = evaluateSuccessProbability(attackChain.getEvents(), completedStages);
            
            // 6. 评估攻击复杂度
            AttackChainAnalysis.AttackProgressAssessment.AttackComplexity complexity = 
                    evaluateAttackComplexity(attackChain.getEvents(), completedStages);
            
            // 7. 评估攻击持久性
            AttackChainAnalysis.AttackProgressAssessment.AttackPersistence persistence = 
                    evaluateAttackPersistence(attackChain.getEvents());
            
            // 8. 构建进展评估结果
            AttackChainAnalysis.AttackProgressAssessment progressAssessment = 
                    AttackChainAnalysis.AttackProgressAssessment.builder()
                            .currentStage(currentStage)
                            .completedStages(completedStages)
                            .progressPercentage(progressPercentage)
                            .nextPossibleActions(nextPossibleActions)
                            .successProbability(successProbability)
                            .complexity(complexity)
                            .persistence(persistence)
                            .build();
            
            attackChain.setProgressAssessment(progressAssessment);
            
            log.debug("攻击进展评估完成: 攻击链={}, 当前阶段={}, 进展={}%", 
                    attackChain.getAttackChainId(), currentStage, progressPercentage);
            
        } catch (Exception e) {
            log.error("攻击进展评估失败: {}, 错误: {}", attackChain.getAttackChainId(), e.getMessage(), e);
        }
    }
    
    /**
     * 分析已完成的攻击阶段
     * 
     * @param events 攻击事件列表
     * @return 已完成的阶段集合
     */
    private Set<CyberKillChainStage> analyzeCompletedStages(List<AttackChainEvent> events) {
        Set<CyberKillChainStage> completedStages = new HashSet<>();
        
        // 统计各阶段的事件数量和置信度
        Map<CyberKillChainStage, List<AttackChainEvent>> eventsByStage = events.stream()
                .filter(e -> e.getKillChainStage() != null)
                .collect(Collectors.groupingBy(AttackChainEvent::getKillChainStage));
        
        for (Map.Entry<CyberKillChainStage, List<AttackChainEvent>> entry : eventsByStage.entrySet()) {
            CyberKillChainStage stage = entry.getKey();
            List<AttackChainEvent> stageEvents = entry.getValue();
            
            // 判断阶段是否完成
            if (isStageCompleted(stage, stageEvents)) {
                completedStages.add(stage);
            }
        }
        
        return completedStages;
    }
    
    /**
     * 判断攻击阶段是否完成
     * 
     * @param stage 攻击阶段
     * @param stageEvents 该阶段的事件列表
     * @return 是否完成
     */
    private boolean isStageCompleted(CyberKillChainStage stage, List<AttackChainEvent> stageEvents) {
        if (stageEvents.isEmpty()) {
            return false;
        }
        
        // 基于事件数量判断
        int eventCount = stageEvents.size();
        if (eventCount >= getMinimumEventsForStageCompletion(stage)) {
            return true;
        }
        
        // 基于置信度判断
        double avgConfidence = stageEvents.stream()
                .filter(e -> e.getConfidence() != null)
                .mapToDouble(AttackChainEvent::getConfidence)
                .average()
                .orElse(0.0);
        
        if (avgConfidence >= 0.8) {
            return true;
        }
        
        // 基于事件严重程度判断
        boolean hasCriticalEvent = stageEvents.stream()
                .anyMatch(e -> e.getSeverity() == AttackChainEvent.EventSeverity.CRITICAL);
        
        if (hasCriticalEvent) {
            return true;
        }
        
        // 基于特定阶段的特殊判断逻辑
        return isStageCompletedBySpecificCriteria(stage, stageEvents);
    }
    
    /**
     * 获取阶段完成所需的最小事件数量
     * 
     * @param stage 攻击阶段
     * @return 最小事件数量
     */
    private int getMinimumEventsForStageCompletion(CyberKillChainStage stage) {
        switch (stage) {
            case RECONNAISSANCE:
                return 3; // 侦察需要多次探测
            case WEAPONIZATION:
                return 1; // 武器化通常是单次事件
            case DELIVERY:
                return 1; // 投递通常是单次事件
            case EXPLOITATION:
                return 2; // 利用可能需要多次尝试
            case INSTALLATION:
                return 1; // 安装通常是单次事件
            case COMMAND_AND_CONTROL:
                return 2; // C2通信需要建立和维持
            case ACTIONS_ON_OBJECTIVES:
                return 1; // 目标行动可能是单次或多次
            default:
                return 1;
        }
    }
    
    /**
     * 基于特定标准判断阶段是否完成
     * 
     * @param stage 攻击阶段
     * @param stageEvents 阶段事件
     * @return 是否完成
     */
    private boolean isStageCompletedBySpecificCriteria(CyberKillChainStage stage, List<AttackChainEvent> stageEvents) {
        switch (stage) {
            case RECONNAISSANCE:
                // 侦察阶段：如果发现了多种服务或端口
                return hasMultipleTargetPorts(stageEvents);
                
            case DELIVERY:
                // 投递阶段：如果有成功的载荷投递指标
                return hasSuccessfulDeliveryIndicators(stageEvents);
                
            case EXPLOITATION:
                // 利用阶段：如果有成功的漏洞利用指标
                return hasSuccessfulExploitationIndicators(stageEvents);
                
            case INSTALLATION:
                // 安装阶段：如果有持久化机制建立
                return hasPersistenceMechanisms(stageEvents);
                
            case COMMAND_AND_CONTROL:
                // C2阶段：如果有稳定的通信建立
                return hasStableCommunication(stageEvents);
                
            case ACTIONS_ON_OBJECTIVES:
                // 目标行动：如果有明确的恶意行为
                return hasMaliciousActions(stageEvents);
                
            default:
                return false;
        }
    }
    
    /**
     * 确定当前攻击阶段
     * 
     * @param events 攻击事件列表
     * @return 当前攻击阶段
     */
    private CyberKillChainStage determineCurrentStage(List<AttackChainEvent> events) {
        // 按时间排序，获取最新的事件
        AttackChainEvent latestEvent = events.stream()
                .max(Comparator.comparing(AttackChainEvent::getTimestamp))
                .orElse(null);
        
        if (latestEvent != null && latestEvent.getKillChainStage() != null) {
            return latestEvent.getKillChainStage();
        }
        
        // 如果最新事件没有阶段信息，找到最高的阶段
        return events.stream()
                .map(AttackChainEvent::getKillChainStage)
                .filter(Objects::nonNull)
                .max(Comparator.comparing(CyberKillChainStage::getStageNumber))
                .orElse(CyberKillChainStage.RECONNAISSANCE);
    }
    
    /**
     * 计算攻击进展百分比
     * 
     * @param completedStages 已完成的阶段
     * @param currentStage 当前阶段
     * @return 进展百分比
     */
    private double calculateProgressPercentage(Set<CyberKillChainStage> completedStages, 
                                             CyberKillChainStage currentStage) {
        if (completedStages.isEmpty()) {
            return 0.0;
        }
        
        // 基于已完成阶段数量计算基础进展
        double baseProgress = (double) completedStages.size() / 7.0 * 100;
        
        // 基于当前阶段调整进展
        if (currentStage != null) {
            double currentStageProgress = (double) currentStage.getStageNumber() / 7.0 * 100;
            // 取两者的最大值
            return Math.max(baseProgress, currentStageProgress);
        }
        
        return baseProgress;
    }
    
    /**
     * 预测下一步可能的攻击行为
     * 
     * @param currentStage 当前阶段
     * @param completedStages 已完成的阶段
     * @return 可能的下一步行为列表
     */
    private List<String> predictNextActions(CyberKillChainStage currentStage, 
                                          Set<CyberKillChainStage> completedStages) {
        List<String> nextActions = new ArrayList<>();
        
        if (currentStage == null) {
            nextActions.add("继续侦察和信息收集");
            return nextActions;
        }
        
        switch (currentStage) {
            case RECONNAISSANCE:
                nextActions.add("开发或获取攻击工具");
                nextActions.add("准备恶意载荷");
                nextActions.add("继续深入侦察");
                break;
                
            case WEAPONIZATION:
                nextActions.add("寻找投递途径");
                nextActions.add("准备钓鱼邮件或恶意链接");
                nextActions.add("测试载荷有效性");
                break;
                
            case DELIVERY:
                nextActions.add("尝试漏洞利用");
                nextActions.add("诱导用户执行恶意代码");
                nextActions.add("等待用户交互");
                break;
                
            case EXPLOITATION:
                nextActions.add("安装后门或恶意软件");
                nextActions.add("建立持久化机制");
                nextActions.add("提升权限");
                break;
                
            case INSTALLATION:
                nextActions.add("建立命令控制通道");
                nextActions.add("开始与C2服务器通信");
                nextActions.add("下载额外工具");
                break;
                
            case COMMAND_AND_CONTROL:
                nextActions.add("开始数据收集");
                nextActions.add("进行横向移动");
                nextActions.add("寻找高价值目标");
                nextActions.add("维持长期访问");
                break;
                
            case ACTIONS_ON_OBJECTIVES:
                nextActions.add("继续数据窃取");
                nextActions.add("扩大攻击范围");
                nextActions.add("清理攻击痕迹");
                nextActions.add("准备退出或销毁证据");
                break;
        }
        
        // 基于已完成的阶段添加额外的预测
        if (completedStages.contains(CyberKillChainStage.COMMAND_AND_CONTROL)) {
            nextActions.add("可能进行长期潜伏");
            nextActions.add("可能建立多个后门");
        }
        
        return nextActions;
    }
    
    /**
     * 评估攻击成功概率
     * 
     * @param events 攻击事件列表
     * @param completedStages 已完成的阶段
     * @return 成功概率
     */
    private double evaluateSuccessProbability(List<AttackChainEvent> events, 
                                            Set<CyberKillChainStage> completedStages) {
        double baseProbability = 0.0;
        
        // 基于已完成阶段数量
        baseProbability += (double) completedStages.size() / 7.0 * 0.4;
        
        // 基于事件置信度
        double avgConfidence = events.stream()
                .filter(e -> e.getConfidence() != null)
                .mapToDouble(AttackChainEvent::getConfidence)
                .average()
                .orElse(0.0);
        baseProbability += avgConfidence * 0.3;
        
        // 基于关键阶段完成情况
        if (completedStages.contains(CyberKillChainStage.INSTALLATION)) {
            baseProbability += 0.2; // 安装阶段完成大大增加成功概率
        }
        
        if (completedStages.contains(CyberKillChainStage.COMMAND_AND_CONTROL)) {
            baseProbability += 0.1; // C2建立进一步增加概率
        }
        
        return Math.min(baseProbability, 1.0);
    }
    
    /**
     * 评估攻击复杂度
     * 
     * @param events 攻击事件列表
     * @param completedStages 已完成的阶段
     * @return 攻击复杂度
     */
    private AttackChainAnalysis.AttackProgressAssessment.AttackComplexity evaluateAttackComplexity(
            List<AttackChainEvent> events, Set<CyberKillChainStage> completedStages) {
        
        int complexityScore = 0;
        
        // 基于阶段数量
        complexityScore += completedStages.size();
        
        // 基于威胁类型多样性
        Set<String> uniqueThreatTypes = events.stream()
                .map(AttackChainEvent::getThreatType)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        complexityScore += uniqueThreatTypes.size();
        
        // 基于攻击技术多样性
        Set<String> uniqueTechniques = events.stream()
                .map(AttackChainEvent::getAttackTechnique)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        complexityScore += uniqueTechniques.size();
        
        // 基于特殊阶段
        if (completedStages.contains(CyberKillChainStage.WEAPONIZATION)) {
            complexityScore += 2; // 武器化表示较高复杂度
        }
        
        if (complexityScore <= 3) {
            return AttackChainAnalysis.AttackProgressAssessment.AttackComplexity.LOW;
        } else if (complexityScore <= 6) {
            return AttackChainAnalysis.AttackProgressAssessment.AttackComplexity.MEDIUM;
        } else if (complexityScore <= 10) {
            return AttackChainAnalysis.AttackProgressAssessment.AttackComplexity.HIGH;
        } else {
            return AttackChainAnalysis.AttackProgressAssessment.AttackComplexity.ADVANCED;
        }
    }
    
    /**
     * 评估攻击持久性
     * 
     * @param events 攻击事件列表
     * @return 攻击持久性
     */
    private AttackChainAnalysis.AttackProgressAssessment.AttackPersistence evaluateAttackPersistence(
            List<AttackChainEvent> events) {
        
        if (events.size() < 2) {
            return AttackChainAnalysis.AttackProgressAssessment.AttackPersistence.TEMPORARY;
        }
        
        // 计算攻击持续时间
        LocalDateTime startTime = events.stream()
                .map(AttackChainEvent::getTimestamp)
                .min(LocalDateTime::compareTo)
                .orElse(LocalDateTime.now());
        
        LocalDateTime endTime = events.stream()
                .map(AttackChainEvent::getTimestamp)
                .max(LocalDateTime::compareTo)
                .orElse(LocalDateTime.now());
        
        long durationHours = java.time.Duration.between(startTime, endTime).toHours();
        
        // 基于持续时间判断
        if (durationHours < 1) {
            return AttackChainAnalysis.AttackProgressAssessment.AttackPersistence.TEMPORARY;
        } else if (durationHours < 24) {
            return AttackChainAnalysis.AttackProgressAssessment.AttackPersistence.SHORT_TERM;
        } else if (durationHours < 168) { // 一周
            return AttackChainAnalysis.AttackProgressAssessment.AttackPersistence.LONG_TERM;
        } else {
            return AttackChainAnalysis.AttackProgressAssessment.AttackPersistence.PERSISTENT;
        }
    }
    
    // 辅助方法
    private boolean hasMultipleTargetPorts(List<AttackChainEvent> events) {
        Set<Integer> targetPorts = events.stream()
                .map(AttackChainEvent::getTargetPort)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        return targetPorts.size() >= 3;
    }
    
    private boolean hasSuccessfulDeliveryIndicators(List<AttackChainEvent> events) {
        return events.stream().anyMatch(e -> 
                e.getConfidence() != null && e.getConfidence() > 0.7);
    }
    
    private boolean hasSuccessfulExploitationIndicators(List<AttackChainEvent> events) {
        return events.stream().anyMatch(e -> 
                e.getSeverity() == AttackChainEvent.EventSeverity.HIGH ||
                e.getSeverity() == AttackChainEvent.EventSeverity.CRITICAL);
    }
    
    private boolean hasPersistenceMechanisms(List<AttackChainEvent> events) {
        return events.stream().anyMatch(e -> {
            String description = e.getDescription();
            return description != null && 
                   (description.contains("后门") || description.contains("持久化") || 
                    description.contains("服务") || description.contains("注册表"));
        });
    }
    
    private boolean hasStableCommunication(List<AttackChainEvent> events) {
        return events.size() >= 2; // 多次通信事件表示稳定连接
    }
    
    private boolean hasMaliciousActions(List<AttackChainEvent> events) {
        return events.stream().anyMatch(e -> {
            String threatType = e.getThreatType();
            return threatType != null && 
                   (threatType.contains("窃取") || threatType.contains("破坏") || 
                    threatType.contains("加密") || threatType.contains("勒索"));
        });
    }
}
