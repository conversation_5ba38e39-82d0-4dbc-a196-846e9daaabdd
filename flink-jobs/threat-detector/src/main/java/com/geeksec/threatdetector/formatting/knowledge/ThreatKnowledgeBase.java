package com.geeksec.threatdetector.formatting.knowledge;

import com.geeksec.threatdetector.formatting.model.AlarmFormattedContent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 威胁知识库
 * 提供威胁相关的知识信息，用于告警格式化
 * 
 * <AUTHOR>
 */
@Slf4j
public class ThreatKnowledgeBase implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 威胁信息缓存
     */
    private final ConcurrentMap<String, ThreatInfo> threatInfoCache;
    
    /**
     * 攻击技术缓存
     */
    private final ConcurrentMap<String, AttackTechnique> attackTechniqueCache;
    
    /**
     * 处理建议缓存
     */
    private final ConcurrentMap<String, List<HandlingSuggestion>> handlingSuggestionCache;
    
    /**
     * 构造函数
     */
    public ThreatKnowledgeBase() {
        this.threatInfoCache = new ConcurrentHashMap<>();
        this.attackTechniqueCache = new ConcurrentHashMap<>();
        this.handlingSuggestionCache = new ConcurrentHashMap<>();
        
        // 初始化知识库
        initializeKnowledgeBase();
    }
    
    /**
     * 获取威胁信息
     * 
     * @param threatType 威胁类型
     * @return 威胁信息
     */
    public ThreatInfo getThreatInfo(String threatType) {
        return threatInfoCache.get(threatType);
    }
    
    /**
     * 获取攻击技术信息
     * 
     * @param technique 攻击技术
     * @return 攻击技术信息
     */
    public AttackTechnique getAttackTechnique(String technique) {
        return attackTechniqueCache.get(technique);
    }
    
    /**
     * 获取处理建议
     * 
     * @param threatType 威胁类型
     * @return 处理建议列表
     */
    public List<HandlingSuggestion> getHandlingSuggestions(String threatType) {
        return handlingSuggestionCache.get(threatType);
    }
    
    /**
     * 威胁信息模型
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThreatInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 威胁类型
         */
        private String threatType;
        
        /**
         * 威胁名称
         */
        private String threatName;
        
        /**
         * 威胁描述
         */
        private String description;
        
        /**
         * 威胁分类
         */
        private String category;
        
        /**
         * 严重程度
         */
        private String severity;
        
        /**
         * 攻击向量
         */
        private List<String> attackVectors;
        
        /**
         * 影响范围
         */
        private String impactScope;
        
        /**
         * 检测原理
         */
        private String detectionPrinciple;
        
        /**
         * 技术背景
         */
        private String technicalBackground;
        
        /**
         * 相关CVE
         */
        private List<String> relatedCVEs;
        
        /**
         * 参考链接
         */
        private List<AlarmFormattedContent.Reference> references;
        
        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
    }
    
    /**
     * 攻击技术模型
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttackTechnique implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 技术ID
         */
        private String techniqueId;
        
        /**
         * 技术名称
         */
        private String techniqueName;
        
        /**
         * MITRE ATT&CK ID
         */
        private String mitreId;
        
        /**
         * 技术描述
         */
        private String description;
        
        /**
         * 攻击阶段
         */
        private String attackPhase;
        
        /**
         * 平台
         */
        private List<String> platforms;
        
        /**
         * 检测方法
         */
        private List<String> detectionMethods;
        
        /**
         * 缓解措施
         */
        private List<String> mitigations;
        
        /**
         * 相关技术
         */
        private List<String> relatedTechniques;
    }
    
    /**
     * 处理建议模型
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HandlingSuggestion implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 建议ID
         */
        private String suggestionId;
        
        /**
         * 建议标题
         */
        private String title;
        
        /**
         * 建议描述
         */
        private String description;
        
        /**
         * 处理步骤
         */
        private List<String> steps;
        
        /**
         * 优先级
         */
        private AlarmFormattedContent.Priority priority;
        
        /**
         * 时间框架
         */
        private String timeFrame;
        
        /**
         * 所需技能
         */
        private String requiredSkills;
        
        /**
         * 风险评估
         */
        private String riskAssessment;
        
        /**
         * 适用场景
         */
        private List<String> applicableScenarios;
    }
    
    /**
     * 初始化知识库
     */
    private void initializeKnowledgeBase() {
        log.info("初始化威胁知识库");
        
        // 初始化威胁信息
        initializeThreatInfo();
        
        // 初始化攻击技术
        initializeAttackTechniques();
        
        // 初始化处理建议
        initializeHandlingSuggestions();
        
        log.info("威胁知识库初始化完成，威胁信息: {}, 攻击技术: {}, 处理建议: {}", 
                threatInfoCache.size(), attackTechniqueCache.size(), handlingSuggestionCache.size());
    }
    
    /**
     * 初始化威胁信息
     */
    private void initializeThreatInfo() {
        // 恶意软件
        threatInfoCache.put("恶意软件", ThreatInfo.builder()
                .threatType("恶意软件")
                .threatName("恶意软件攻击")
                .description("恶意软件是指故意设计用来损害计算机、服务器、客户端或计算机网络的软件")
                .category("恶意代码")
                .severity("高")
                .attackVectors(List.of("邮件附件", "恶意下载", "USB传播", "网络传播"))
                .impactScope("系统完整性、数据安全、业务连续性")
                .detectionPrinciple("基于特征码匹配、行为分析、启发式检测等技术识别恶意软件")
                .technicalBackground("恶意软件通常包含病毒、蠕虫、木马、勒索软件等多种类型")
                .relatedCVEs(List.of("CVE-2021-34527", "CVE-2021-1675"))
                .updateTime(LocalDateTime.now())
                .build());
        
        // 网络扫描
        threatInfoCache.put("网络扫描", ThreatInfo.builder()
                .threatType("网络扫描")
                .threatName("网络侦察扫描")
                .description("攻击者通过扫描网络端口、服务和漏洞来收集目标信息的行为")
                .category("侦察活动")
                .severity("中")
                .attackVectors(List.of("端口扫描", "服务扫描", "漏洞扫描", "网络映射"))
                .impactScope("信息泄露、攻击准备")
                .detectionPrinciple("通过分析网络流量模式、连接频率和端口访问行为检测扫描活动")
                .technicalBackground("扫描是攻击链的初始阶段，用于收集目标系统信息")
                .updateTime(LocalDateTime.now())
                .build());
        
        // DNS隧道
        threatInfoCache.put("DNS隧道", ThreatInfo.builder()
                .threatType("DNS隧道")
                .threatName("DNS隐蔽隧道通信")
                .description("利用DNS协议建立隐蔽通信通道，绕过网络安全检测")
                .category("隐蔽通信")
                .severity("高")
                .attackVectors(List.of("DNS查询", "DNS响应", "子域名编码"))
                .impactScope("数据泄露、命令控制、网络安全绕过")
                .detectionPrinciple("通过分析DNS查询模式、频率和异常域名特征检测隧道行为")
                .technicalBackground("DNS隧道利用DNS协议的特性，将数据编码在DNS查询和响应中")
                .updateTime(LocalDateTime.now())
                .build());
        
        // Webshell
        threatInfoCache.put("Webshell", ThreatInfo.builder()
                .threatType("Webshell")
                .threatName("Web后门")
                .description("植入在Web服务器上的恶意脚本，提供远程控制功能")
                .category("后门程序")
                .severity("高")
                .attackVectors(List.of("文件上传漏洞", "代码注入", "SQL注入", "命令执行"))
                .impactScope("服务器控制、数据窃取、权限提升")
                .detectionPrinciple("通过分析Web请求特征、文件行为和通信模式检测Webshell")
                .technicalBackground("Webshell通常以PHP、ASP、JSP等脚本形式存在")
                .updateTime(LocalDateTime.now())
                .build());
        
        // 加密工具
        threatInfoCache.put("加密工具", ThreatInfo.builder()
                .threatType("加密工具")
                .threatName("恶意加密通信工具")
                .description("使用加密技术隐藏恶意通信的工具和技术")
                .category("隐蔽通信")
                .severity("中")
                .attackVectors(List.of("SSL/TLS加密", "自定义加密", "流量混淆"))
                .impactScope("通信隐蔽、检测规避")
                .detectionPrinciple("通过分析加密流量特征、证书信息和通信模式检测恶意加密工具")
                .technicalBackground("攻击者使用加密技术来隐藏恶意活动，规避安全检测")
                .updateTime(LocalDateTime.now())
                .build());
    }
    
    /**
     * 初始化攻击技术
     */
    private void initializeAttackTechniques() {
        // 远程代码执行
        attackTechniqueCache.put("远程代码执行", AttackTechnique.builder()
                .techniqueId("T001")
                .techniqueName("远程代码执行")
                .mitreId("T1059")
                .description("攻击者在目标系统上执行任意代码的技术")
                .attackPhase("执行")
                .platforms(List.of("Windows", "Linux", "macOS"))
                .detectionMethods(List.of("进程监控", "网络监控", "文件监控"))
                .mitigations(List.of("输入验证", "权限控制", "网络隔离"))
                .relatedTechniques(List.of("命令注入", "代码注入"))
                .build());
        
        // 权限提升
        attackTechniqueCache.put("权限提升", AttackTechnique.builder()
                .techniqueId("T002")
                .techniqueName("权限提升")
                .mitreId("T1068")
                .description("攻击者获取更高权限的技术")
                .attackPhase("权限提升")
                .platforms(List.of("Windows", "Linux", "macOS"))
                .detectionMethods(List.of("权限监控", "系统调用监控", "日志分析"))
                .mitigations(List.of("最小权限原则", "系统更新", "访问控制"))
                .relatedTechniques(List.of("漏洞利用", "凭据窃取"))
                .build());
    }
    
    /**
     * 初始化处理建议
     */
    private void initializeHandlingSuggestions() {
        // 恶意软件处理建议
        handlingSuggestionCache.put("恶意软件", List.of(
                HandlingSuggestion.builder()
                        .suggestionId("MAL001")
                        .title("立即隔离感染主机")
                        .description("将感染恶意软件的主机从网络中隔离，防止横向传播")
                        .steps(List.of(
                                "断开网络连接",
                                "保留现场证据",
                                "启动应急响应流程",
                                "通知相关人员"
                        ))
                        .priority(AlarmFormattedContent.Priority.URGENT)
                        .timeFrame("立即执行")
                        .requiredSkills("网络管理、安全响应")
                        .riskAssessment("高风险，需要立即处理")
                        .applicableScenarios(List.of("恶意软件感染", "病毒传播"))
                        .build(),
                
                HandlingSuggestion.builder()
                        .suggestionId("MAL002")
                        .title("进行恶意软件分析")
                        .description("对检测到的恶意软件进行详细分析，了解其行为和影响")
                        .steps(List.of(
                                "收集恶意软件样本",
                                "在沙箱环境中分析",
                                "识别恶意行为",
                                "评估影响范围",
                                "制定清除方案"
                        ))
                        .priority(AlarmFormattedContent.Priority.HIGH)
                        .timeFrame("4小时内")
                        .requiredSkills("恶意软件分析、逆向工程")
                        .riskAssessment("中等风险，需要专业分析")
                        .applicableScenarios(List.of("未知恶意软件", "高级威胁"))
                        .build()
        ));
        
        // 网络扫描处理建议
        handlingSuggestionCache.put("网络扫描", List.of(
                HandlingSuggestion.builder()
                        .suggestionId("SCAN001")
                        .title("阻断扫描源IP")
                        .description("在防火墙或网络设备上阻断扫描源IP地址")
                        .steps(List.of(
                                "识别扫描源IP",
                                "评估IP信誉",
                                "配置防火墙规则",
                                "监控阻断效果"
                        ))
                        .priority(AlarmFormattedContent.Priority.HIGH)
                        .timeFrame("30分钟内")
                        .requiredSkills("网络管理、防火墙配置")
                        .riskAssessment("中等风险，可能是攻击前奏")
                        .applicableScenarios(List.of("端口扫描", "服务扫描"))
                        .build(),
                
                HandlingSuggestion.builder()
                        .suggestionId("SCAN002")
                        .title("加强监控和日志记录")
                        .description("增强对扫描活动的监控，收集更多威胁情报")
                        .steps(List.of(
                                "启用详细日志记录",
                                "配置实时监控",
                                "设置告警阈值",
                                "分析扫描模式"
                        ))
                        .priority(AlarmFormattedContent.Priority.MEDIUM)
                        .timeFrame("1小时内")
                        .requiredSkills("日志分析、监控配置")
                        .riskAssessment("低风险，预防性措施")
                        .applicableScenarios(List.of("持续扫描", "侦察活动"))
                        .build()
        ));
    }
    
    /**
     * 添加威胁信息
     * 
     * @param threatInfo 威胁信息
     */
    public void addThreatInfo(ThreatInfo threatInfo) {
        threatInfoCache.put(threatInfo.getThreatType(), threatInfo);
        log.info("添加威胁信息: {}", threatInfo.getThreatType());
    }
    
    /**
     * 添加攻击技术
     * 
     * @param attackTechnique 攻击技术
     */
    public void addAttackTechnique(AttackTechnique attackTechnique) {
        attackTechniqueCache.put(attackTechnique.getTechniqueName(), attackTechnique);
        log.info("添加攻击技术: {}", attackTechnique.getTechniqueName());
    }
    
    /**
     * 添加处理建议
     * 
     * @param threatType 威胁类型
     * @param suggestions 处理建议列表
     */
    public void addHandlingSuggestions(String threatType, List<HandlingSuggestion> suggestions) {
        handlingSuggestionCache.put(threatType, suggestions);
        log.info("添加处理建议: {}, 数量: {}", threatType, suggestions.size());
    }
    
    /**
     * 获取知识库统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Integer> getStatistics() {
        Map<String, Integer> stats = new ConcurrentHashMap<>();
        stats.put("threatInfo", threatInfoCache.size());
        stats.put("attackTechnique", attackTechniqueCache.size());
        stats.put("handlingSuggestion", handlingSuggestionCache.size());
        return stats;
    }
}
