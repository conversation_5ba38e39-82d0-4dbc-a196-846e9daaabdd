package com.geeksec.threatdetector.subscription.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 通知渠道模型
 * 定义告警通知的发送渠道
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationChannel implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 渠道ID
     */
    private String channelId;
    
    /**
     * 渠道名称
     */
    private String channelName;
    
    /**
     * 渠道类型
     */
    private ChannelType channelType;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 接收地址（邮箱、手机号、webhook地址等）
     */
    private String address;
    
    /**
     * 渠道配置参数
     */
    private Map<String, Object> config;
    
    /**
     * 通知模板ID
     */
    private String templateId;
    
    /**
     * 优先级
     */
    private ChannelPriority priority;
    
    /**
     * 重试配置
     */
    private RetryConfig retryConfig;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 最后发送时间
     */
    private LocalDateTime lastSentTime;
    
    /**
     * 发送次数
     */
    private Long sentCount;
    
    /**
     * 成功次数
     */
    private Long successCount;
    
    /**
     * 失败次数
     */
    private Long failureCount;
    
    /**
     * 渠道类型枚举
     */
    public enum ChannelType {
        /** 邮件 */
        EMAIL("email", "邮件通知"),
        /** 短信 */
        SMS("sms", "短信通知"),
        /** 钉钉 */
        DINGTALK("dingtalk", "钉钉通知"),
        /** 企业微信 */
        WECHAT_WORK("wechat_work", "企业微信通知"),
        /** Slack */
        SLACK("slack", "Slack通知"),
        /** Webhook */
        WEBHOOK("webhook", "Webhook通知"),
        /** 飞书 */
        FEISHU("feishu", "飞书通知"),
        /** 电话 */
        PHONE("phone", "电话通知"),
        /** 推送通知 */
        PUSH("push", "推送通知"),
        /** 自定义 */
        CUSTOM("custom", "自定义通知");
        
        private final String code;
        private final String description;
        
        ChannelType(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 渠道优先级枚举
     */
    public enum ChannelPriority {
        /** 低优先级 */
        LOW(1),
        /** 普通优先级 */
        NORMAL(2),
        /** 高优先级 */
        HIGH(3),
        /** 紧急优先级 */
        URGENT(4);
        
        private final int level;
        
        ChannelPriority(int level) {
            this.level = level;
        }
        
        public int getLevel() {
            return level;
        }
    }
    
    /**
     * 重试配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RetryConfig implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 是否启用重试
         */
        private Boolean enabled;
        
        /**
         * 最大重试次数
         */
        private Integer maxRetries;
        
        /**
         * 重试间隔（秒）
         */
        private Integer retryIntervalSeconds;
        
        /**
         * 重试策略
         */
        private RetryStrategy strategy;
        
        /**
         * 重试策略枚举
         */
        public enum RetryStrategy {
            /** 固定间隔 */
            FIXED,
            /** 指数退避 */
            EXPONENTIAL,
            /** 线性增长 */
            LINEAR
        }
    }
    
    /**
     * 获取配置参数
     * 
     * @param key 参数键
     * @return 参数值
     */
    public Object getConfigValue(String key) {
        if (config == null) {
            return null;
        }
        return config.get(key);
    }
    
    /**
     * 获取配置参数（字符串类型）
     * 
     * @param key 参数键
     * @param defaultValue 默认值
     * @return 参数值
     */
    public String getConfigString(String key, String defaultValue) {
        Object value = getConfigValue(key);
        return value != null ? value.toString() : defaultValue;
    }
    
    /**
     * 获取配置参数（整数类型）
     * 
     * @param key 参数键
     * @param defaultValue 默认值
     * @return 参数值
     */
    public Integer getConfigInteger(String key, Integer defaultValue) {
        Object value = getConfigValue(key);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }
    
    /**
     * 获取配置参数（布尔类型）
     * 
     * @param key 参数键
     * @param defaultValue 默认值
     * @return 参数值
     */
    public Boolean getConfigBoolean(String key, Boolean defaultValue) {
        Object value = getConfigValue(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return defaultValue;
    }
    
    /**
     * 设置配置参数
     * 
     * @param key 参数键
     * @param value 参数值
     */
    public void setConfigValue(String key, Object value) {
        if (config == null) {
            config = new java.util.HashMap<>();
        }
        config.put(key, value);
    }
    
    /**
     * 更新发送统计
     * 
     * @param success 是否成功
     */
    public void updateSendStatistics(boolean success) {
        this.lastSentTime = LocalDateTime.now();
        this.sentCount = (this.sentCount == null ? 0 : this.sentCount) + 1;
        
        if (success) {
            this.successCount = (this.successCount == null ? 0 : this.successCount) + 1;
        } else {
            this.failureCount = (this.failureCount == null ? 0 : this.failureCount) + 1;
        }
        
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 获取成功率
     * 
     * @return 成功率
     */
    public double getSuccessRate() {
        if (sentCount == null || sentCount == 0) {
            return 0.0;
        }
        
        long success = successCount != null ? successCount : 0;
        return (double) success / sentCount;
    }
    
    /**
     * 获取失败率
     * 
     * @return 失败率
     */
    public double getFailureRate() {
        if (sentCount == null || sentCount == 0) {
            return 0.0;
        }
        
        long failure = failureCount != null ? failureCount : 0;
        return (double) failure / sentCount;
    }
    
    /**
     * 检查渠道是否可用
     * 
     * @return 是否可用
     */
    public boolean isAvailable() {
        return Boolean.TRUE.equals(enabled) && 
               address != null && !address.trim().isEmpty();
    }
    
    /**
     * 验证渠道配置是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        if (channelType == null || address == null || address.trim().isEmpty()) {
            return false;
        }
        
        // 根据渠道类型验证地址格式
        switch (channelType) {
            case EMAIL:
                return isValidEmail(address);
            case SMS:
            case PHONE:
                return isValidPhone(address);
            case WEBHOOK:
                return isValidUrl(address);
            default:
                return true; // 其他类型暂不验证
        }
    }
    
    /**
     * 验证邮箱格式
     * 
     * @param email 邮箱地址
     * @return 是否有效
     */
    private boolean isValidEmail(String email) {
        String emailRegex = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
        return email.matches(emailRegex);
    }
    
    /**
     * 验证手机号格式
     * 
     * @param phone 手机号
     * @return 是否有效
     */
    private boolean isValidPhone(String phone) {
        String phoneRegex = "^[1-9]\\d{10}$"; // 简单的11位手机号验证
        return phone.matches(phoneRegex);
    }
    
    /**
     * 验证URL格式
     * 
     * @param url URL地址
     * @return 是否有效
     */
    private boolean isValidUrl(String url) {
        try {
            new java.net.URL(url);
            return true;
        } catch (java.net.MalformedURLException e) {
            return false;
        }
    }
    
    /**
     * 创建邮件通知渠道
     * 
     * @param email 邮箱地址
     * @param templateId 模板ID
     * @return 通知渠道
     */
    public static NotificationChannel createEmailChannel(String email, String templateId) {
        return NotificationChannel.builder()
                .channelType(ChannelType.EMAIL)
                .address(email)
                .templateId(templateId)
                .enabled(true)
                .priority(ChannelPriority.NORMAL)
                .createTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建短信通知渠道
     * 
     * @param phone 手机号
     * @param templateId 模板ID
     * @return 通知渠道
     */
    public static NotificationChannel createSmsChannel(String phone, String templateId) {
        return NotificationChannel.builder()
                .channelType(ChannelType.SMS)
                .address(phone)
                .templateId(templateId)
                .enabled(true)
                .priority(ChannelPriority.HIGH)
                .createTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建Webhook通知渠道
     * 
     * @param webhookUrl Webhook地址
     * @param templateId 模板ID
     * @return 通知渠道
     */
    public static NotificationChannel createWebhookChannel(String webhookUrl, String templateId) {
        return NotificationChannel.builder()
                .channelType(ChannelType.WEBHOOK)
                .address(webhookUrl)
                .templateId(templateId)
                .enabled(true)
                .priority(ChannelPriority.NORMAL)
                .createTime(LocalDateTime.now())
                .build();
    }
}
