package com.geeksec.threatdetector.killchain.knowledge;

import com.geeksec.threatdetector.killchain.model.CyberKillChainStage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 攻击链知识库
 * 提供威胁类型到Cyber Kill Chain阶段的映射知识
 * 
 * <AUTHOR>
 */
@Slf4j
public class AttackChainKnowledgeBase implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 威胁类型到Kill Chain阶段的映射
     */
    private final ConcurrentMap<String, List<CyberKillChainStage>> threatToStageMapping;
    
    /**
     * 攻击技术到Kill Chain阶段的映射
     */
    private final ConcurrentMap<String, CyberKillChainStage> techniqueToStageMapping;
    
    /**
     * MITRE ATT&CK战术到Kill Chain阶段的映射
     */
    private final ConcurrentMap<String, CyberKillChainStage> tacticToStageMapping;
    
    /**
     * 威胁指标模式到Kill Chain阶段的映射
     */
    private final ConcurrentMap<String, List<CyberKillChainStage>> iocPatternToStageMapping;
    
    /**
     * 攻击链模式库
     */
    private final ConcurrentMap<String, AttackChainPattern> attackChainPatterns;
    
    /**
     * 构造函数
     */
    public AttackChainKnowledgeBase() {
        this.threatToStageMapping = new ConcurrentHashMap<>();
        this.techniqueToStageMapping = new ConcurrentHashMap<>();
        this.tacticToStageMapping = new ConcurrentHashMap<>();
        this.iocPatternToStageMapping = new ConcurrentHashMap<>();
        this.attackChainPatterns = new ConcurrentHashMap<>();
        
        // 初始化知识库
        initializeKnowledgeBase();
    }
    
    /**
     * 根据威胁类型获取可能的Kill Chain阶段
     * 
     * @param threatType 威胁类型
     * @return 可能的Kill Chain阶段列表
     */
    public List<CyberKillChainStage> getStagesByThreatType(String threatType) {
        return threatToStageMapping.getOrDefault(threatType, Arrays.asList(CyberKillChainStage.RECONNAISSANCE));
    }
    
    /**
     * 根据攻击技术获取Kill Chain阶段
     * 
     * @param technique 攻击技术
     * @return Kill Chain阶段
     */
    public CyberKillChainStage getStageByTechnique(String technique) {
        return techniqueToStageMapping.get(technique);
    }
    
    /**
     * 根据MITRE ATT&CK战术获取Kill Chain阶段
     * 
     * @param tactic MITRE ATT&CK战术
     * @return Kill Chain阶段
     */
    public CyberKillChainStage getStageByTactic(String tactic) {
        return tacticToStageMapping.get(tactic);
    }
    
    /**
     * 根据威胁指标模式获取可能的Kill Chain阶段
     * 
     * @param iocPattern 威胁指标模式
     * @return 可能的Kill Chain阶段列表
     */
    public List<CyberKillChainStage> getStagesByIOCPattern(String iocPattern) {
        return iocPatternToStageMapping.get(iocPattern);
    }
    
    /**
     * 获取攻击链模式
     * 
     * @param patternName 模式名称
     * @return 攻击链模式
     */
    public AttackChainPattern getAttackChainPattern(String patternName) {
        return attackChainPatterns.get(patternName);
    }
    
    /**
     * 攻击链模式
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttackChainPattern implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 模式名称
         */
        private String patternName;
        
        /**
         * 模式描述
         */
        private String description;
        
        /**
         * 典型的攻击阶段序列
         */
        private List<CyberKillChainStage> typicalStageSequence;
        
        /**
         * 关键指标
         */
        private List<String> keyIndicators;
        
        /**
         * 威胁组织
         */
        private List<String> associatedThreatGroups;
        
        /**
         * 攻击持续时间范围
         */
        private String durationRange;
        
        /**
         * 检测难度
         */
        private DetectionDifficulty detectionDifficulty;
        
        /**
         * 检测难度枚举
         */
        public enum DetectionDifficulty {
            EASY("容易检测"),
            MODERATE("中等难度"),
            HARD("困难检测"),
            VERY_HARD("极难检测");
            
            private final String description;
            
            DetectionDifficulty(String description) {
                this.description = description;
            }
            
            public String getDescription() { return description; }
        }
    }
    
    /**
     * 初始化知识库
     */
    private void initializeKnowledgeBase() {
        log.info("初始化攻击链知识库");
        
        // 初始化威胁类型映射
        initializeThreatTypeMapping();
        
        // 初始化攻击技术映射
        initializeTechniqueMapping();
        
        // 初始化MITRE ATT&CK战术映射
        initializeTacticMapping();
        
        // 初始化IOC模式映射
        initializeIOCPatternMapping();
        
        // 初始化攻击链模式
        initializeAttackChainPatterns();
        
        log.info("攻击链知识库初始化完成，威胁类型: {}, 攻击技术: {}, 战术: {}, IOC模式: {}, 攻击链模式: {}", 
                threatToStageMapping.size(), techniqueToStageMapping.size(), 
                tacticToStageMapping.size(), iocPatternToStageMapping.size(), attackChainPatterns.size());
    }
    
    /**
     * 初始化威胁类型映射
     */
    private void initializeThreatTypeMapping() {
        // 网络扫描 - 主要在侦察阶段
        threatToStageMapping.put("网络扫描", Arrays.asList(CyberKillChainStage.RECONNAISSANCE));
        threatToStageMapping.put("端口扫描", Arrays.asList(CyberKillChainStage.RECONNAISSANCE));
        threatToStageMapping.put("服务扫描", Arrays.asList(CyberKillChainStage.RECONNAISSANCE));
        threatToStageMapping.put("漏洞扫描", Arrays.asList(CyberKillChainStage.RECONNAISSANCE));
        
        // 恶意软件 - 可能在多个阶段
        threatToStageMapping.put("恶意软件", Arrays.asList(
                CyberKillChainStage.WEAPONIZATION,
                CyberKillChainStage.DELIVERY,
                CyberKillChainStage.EXPLOITATION,
                CyberKillChainStage.INSTALLATION
        ));
        threatToStageMapping.put("木马", Arrays.asList(
                CyberKillChainStage.WEAPONIZATION,
                CyberKillChainStage.INSTALLATION,
                CyberKillChainStage.COMMAND_AND_CONTROL
        ));
        threatToStageMapping.put("病毒", Arrays.asList(
                CyberKillChainStage.WEAPONIZATION,
                CyberKillChainStage.DELIVERY,
                CyberKillChainStage.INSTALLATION
        ));
        threatToStageMapping.put("勒索软件", Arrays.asList(
                CyberKillChainStage.INSTALLATION,
                CyberKillChainStage.ACTIONS_ON_OBJECTIVES
        ));
        
        // Webshell - 主要在安装和命令控制阶段
        threatToStageMapping.put("Webshell", Arrays.asList(
                CyberKillChainStage.INSTALLATION,
                CyberKillChainStage.COMMAND_AND_CONTROL
        ));
        threatToStageMapping.put("Web后门", Arrays.asList(
                CyberKillChainStage.INSTALLATION,
                CyberKillChainStage.COMMAND_AND_CONTROL
        ));
        
        // DNS隧道 - 主要在命令控制阶段
        threatToStageMapping.put("DNS隧道", Arrays.asList(CyberKillChainStage.COMMAND_AND_CONTROL));
        threatToStageMapping.put("隐蔽通道", Arrays.asList(CyberKillChainStage.COMMAND_AND_CONTROL));
        
        // 加密工具 - 主要在命令控制阶段
        threatToStageMapping.put("加密工具", Arrays.asList(CyberKillChainStage.COMMAND_AND_CONTROL));
        threatToStageMapping.put("加密通信", Arrays.asList(CyberKillChainStage.COMMAND_AND_CONTROL));
        
        // 漏洞利用 - 主要在利用阶段
        threatToStageMapping.put("漏洞利用", Arrays.asList(CyberKillChainStage.EXPLOITATION));
        threatToStageMapping.put("缓冲区溢出", Arrays.asList(CyberKillChainStage.EXPLOITATION));
        threatToStageMapping.put("代码注入", Arrays.asList(CyberKillChainStage.EXPLOITATION));
        threatToStageMapping.put("SQL注入", Arrays.asList(CyberKillChainStage.EXPLOITATION));
        
        // 钓鱼攻击 - 主要在投递阶段
        threatToStageMapping.put("钓鱼攻击", Arrays.asList(CyberKillChainStage.DELIVERY));
        threatToStageMapping.put("钓鱼邮件", Arrays.asList(CyberKillChainStage.DELIVERY));
        threatToStageMapping.put("恶意附件", Arrays.asList(CyberKillChainStage.DELIVERY));
        
        // 数据窃取 - 主要在目标行动阶段
        threatToStageMapping.put("数据窃取", Arrays.asList(CyberKillChainStage.ACTIONS_ON_OBJECTIVES));
        threatToStageMapping.put("数据泄露", Arrays.asList(CyberKillChainStage.ACTIONS_ON_OBJECTIVES));
        threatToStageMapping.put("敏感信息泄露", Arrays.asList(CyberKillChainStage.ACTIONS_ON_OBJECTIVES));
        
        // 横向移动 - 主要在目标行动阶段
        threatToStageMapping.put("横向移动", Arrays.asList(CyberKillChainStage.ACTIONS_ON_OBJECTIVES));
        threatToStageMapping.put("权限提升", Arrays.asList(
                CyberKillChainStage.EXPLOITATION,
                CyberKillChainStage.ACTIONS_ON_OBJECTIVES
        ));
    }
    
    /**
     * 初始化攻击技术映射
     */
    private void initializeTechniqueMapping() {
        // 侦察阶段技术
        techniqueToStageMapping.put("网络扫描", CyberKillChainStage.RECONNAISSANCE);
        techniqueToStageMapping.put("端口扫描", CyberKillChainStage.RECONNAISSANCE);
        techniqueToStageMapping.put("服务发现", CyberKillChainStage.RECONNAISSANCE);
        techniqueToStageMapping.put("系统信息收集", CyberKillChainStage.RECONNAISSANCE);
        techniqueToStageMapping.put("网络嗅探", CyberKillChainStage.RECONNAISSANCE);
        
        // 武器化阶段技术
        techniqueToStageMapping.put("恶意软件开发", CyberKillChainStage.WEAPONIZATION);
        techniqueToStageMapping.put("漏洞利用工具", CyberKillChainStage.WEAPONIZATION);
        techniqueToStageMapping.put("载荷封装", CyberKillChainStage.WEAPONIZATION);
        
        // 投递阶段技术
        techniqueToStageMapping.put("钓鱼邮件", CyberKillChainStage.DELIVERY);
        techniqueToStageMapping.put("恶意附件", CyberKillChainStage.DELIVERY);
        techniqueToStageMapping.put("水坑攻击", CyberKillChainStage.DELIVERY);
        techniqueToStageMapping.put("USB投递", CyberKillChainStage.DELIVERY);
        
        // 利用阶段技术
        techniqueToStageMapping.put("缓冲区溢出", CyberKillChainStage.EXPLOITATION);
        techniqueToStageMapping.put("代码注入", CyberKillChainStage.EXPLOITATION);
        techniqueToStageMapping.put("SQL注入", CyberKillChainStage.EXPLOITATION);
        techniqueToStageMapping.put("命令注入", CyberKillChainStage.EXPLOITATION);
        techniqueToStageMapping.put("权限提升", CyberKillChainStage.EXPLOITATION);
        
        // 安装阶段技术
        techniqueToStageMapping.put("后门安装", CyberKillChainStage.INSTALLATION);
        techniqueToStageMapping.put("Webshell植入", CyberKillChainStage.INSTALLATION);
        techniqueToStageMapping.put("服务安装", CyberKillChainStage.INSTALLATION);
        techniqueToStageMapping.put("注册表修改", CyberKillChainStage.INSTALLATION);
        techniqueToStageMapping.put("计划任务", CyberKillChainStage.INSTALLATION);
        
        // 命令控制阶段技术
        techniqueToStageMapping.put("C2通信", CyberKillChainStage.COMMAND_AND_CONTROL);
        techniqueToStageMapping.put("DNS隧道", CyberKillChainStage.COMMAND_AND_CONTROL);
        techniqueToStageMapping.put("HTTP通信", CyberKillChainStage.COMMAND_AND_CONTROL);
        techniqueToStageMapping.put("加密通道", CyberKillChainStage.COMMAND_AND_CONTROL);
        
        // 目标行动阶段技术
        techniqueToStageMapping.put("数据窃取", CyberKillChainStage.ACTIONS_ON_OBJECTIVES);
        techniqueToStageMapping.put("数据销毁", CyberKillChainStage.ACTIONS_ON_OBJECTIVES);
        techniqueToStageMapping.put("横向移动", CyberKillChainStage.ACTIONS_ON_OBJECTIVES);
        techniqueToStageMapping.put("勒索加密", CyberKillChainStage.ACTIONS_ON_OBJECTIVES);
    }
    
    /**
     * 初始化MITRE ATT&CK战术映射
     */
    private void initializeTacticMapping() {
        // MITRE ATT&CK战术到Kill Chain阶段的映射
        tacticToStageMapping.put("Reconnaissance", CyberKillChainStage.RECONNAISSANCE);
        tacticToStageMapping.put("Resource Development", CyberKillChainStage.WEAPONIZATION);
        tacticToStageMapping.put("Initial Access", CyberKillChainStage.DELIVERY);
        tacticToStageMapping.put("Execution", CyberKillChainStage.EXPLOITATION);
        tacticToStageMapping.put("Persistence", CyberKillChainStage.INSTALLATION);
        tacticToStageMapping.put("Privilege Escalation", CyberKillChainStage.EXPLOITATION);
        tacticToStageMapping.put("Defense Evasion", CyberKillChainStage.INSTALLATION);
        tacticToStageMapping.put("Credential Access", CyberKillChainStage.ACTIONS_ON_OBJECTIVES);
        tacticToStageMapping.put("Discovery", CyberKillChainStage.RECONNAISSANCE);
        tacticToStageMapping.put("Lateral Movement", CyberKillChainStage.ACTIONS_ON_OBJECTIVES);
        tacticToStageMapping.put("Collection", CyberKillChainStage.ACTIONS_ON_OBJECTIVES);
        tacticToStageMapping.put("Command and Control", CyberKillChainStage.COMMAND_AND_CONTROL);
        tacticToStageMapping.put("Exfiltration", CyberKillChainStage.ACTIONS_ON_OBJECTIVES);
        tacticToStageMapping.put("Impact", CyberKillChainStage.ACTIONS_ON_OBJECTIVES);
    }
    
    /**
     * 初始化IOC模式映射
     */
    private void initializeIOCPatternMapping() {
        // 网络扫描相关IOC
        iocPatternToStageMapping.put("大量端口连接", Arrays.asList(CyberKillChainStage.RECONNAISSANCE));
        iocPatternToStageMapping.put("异常DNS查询", Arrays.asList(
                CyberKillChainStage.RECONNAISSANCE,
                CyberKillChainStage.COMMAND_AND_CONTROL
        ));
        
        // 恶意文件相关IOC
        iocPatternToStageMapping.put("恶意文件哈希", Arrays.asList(
                CyberKillChainStage.WEAPONIZATION,
                CyberKillChainStage.DELIVERY,
                CyberKillChainStage.INSTALLATION
        ));
        
        // 网络通信相关IOC
        iocPatternToStageMapping.put("C2域名", Arrays.asList(CyberKillChainStage.COMMAND_AND_CONTROL));
        iocPatternToStageMapping.put("可疑IP通信", Arrays.asList(CyberKillChainStage.COMMAND_AND_CONTROL));
        
        // 系统行为相关IOC
        iocPatternToStageMapping.put("异常进程", Arrays.asList(
                CyberKillChainStage.EXPLOITATION,
                CyberKillChainStage.INSTALLATION
        ));
        iocPatternToStageMapping.put("注册表修改", Arrays.asList(CyberKillChainStage.INSTALLATION));
        iocPatternToStageMapping.put("文件系统修改", Arrays.asList(CyberKillChainStage.INSTALLATION));
    }
    
    /**
     * 初始化攻击链模式
     */
    private void initializeAttackChainPatterns() {
        // APT攻击模式
        attackChainPatterns.put("APT攻击", AttackChainPattern.builder()
                .patternName("APT攻击")
                .description("高级持续威胁攻击模式")
                .typicalStageSequence(Arrays.asList(
                        CyberKillChainStage.RECONNAISSANCE,
                        CyberKillChainStage.WEAPONIZATION,
                        CyberKillChainStage.DELIVERY,
                        CyberKillChainStage.EXPLOITATION,
                        CyberKillChainStage.INSTALLATION,
                        CyberKillChainStage.COMMAND_AND_CONTROL,
                        CyberKillChainStage.ACTIONS_ON_OBJECTIVES
                ))
                .keyIndicators(Arrays.asList("长期潜伏", "多阶段攻击", "高级技术", "定向攻击"))
                .associatedThreatGroups(Arrays.asList("APT1", "APT28", "APT29"))
                .durationRange("数月到数年")
                .detectionDifficulty(AttackChainPattern.DetectionDifficulty.VERY_HARD)
                .build());
        
        // 勒索软件攻击模式
        attackChainPatterns.put("勒索软件攻击", AttackChainPattern.builder()
                .patternName("勒索软件攻击")
                .description("勒索软件攻击模式")
                .typicalStageSequence(Arrays.asList(
                        CyberKillChainStage.DELIVERY,
                        CyberKillChainStage.EXPLOITATION,
                        CyberKillChainStage.INSTALLATION,
                        CyberKillChainStage.ACTIONS_ON_OBJECTIVES
                ))
                .keyIndicators(Arrays.asList("文件加密", "勒索信息", "快速传播"))
                .associatedThreatGroups(Arrays.asList("REvil", "Conti", "LockBit"))
                .durationRange("数小时到数天")
                .detectionDifficulty(AttackChainPattern.DetectionDifficulty.MODERATE)
                .build());
        
        // 网络钓鱼攻击模式
        attackChainPatterns.put("网络钓鱼攻击", AttackChainPattern.builder()
                .patternName("网络钓鱼攻击")
                .description("网络钓鱼攻击模式")
                .typicalStageSequence(Arrays.asList(
                        CyberKillChainStage.RECONNAISSANCE,
                        CyberKillChainStage.WEAPONIZATION,
                        CyberKillChainStage.DELIVERY,
                        CyberKillChainStage.EXPLOITATION
                ))
                .keyIndicators(Arrays.asList("钓鱼邮件", "恶意链接", "凭据窃取"))
                .associatedThreatGroups(Arrays.asList("各种网络犯罪组织"))
                .durationRange("数分钟到数小时")
                .detectionDifficulty(AttackChainPattern.DetectionDifficulty.EASY)
                .build());
    }
    
    /**
     * 添加威胁类型映射
     * 
     * @param threatType 威胁类型
     * @param stages Kill Chain阶段列表
     */
    public void addThreatTypeMapping(String threatType, List<CyberKillChainStage> stages) {
        threatToStageMapping.put(threatType, stages);
        log.info("添加威胁类型映射: {} -> {}", threatType, stages);
    }
    
    /**
     * 添加攻击技术映射
     * 
     * @param technique 攻击技术
     * @param stage Kill Chain阶段
     */
    public void addTechniqueMapping(String technique, CyberKillChainStage stage) {
        techniqueToStageMapping.put(technique, stage);
        log.info("添加攻击技术映射: {} -> {}", technique, stage);
    }
    
    /**
     * 添加攻击链模式
     * 
     * @param pattern 攻击链模式
     */
    public void addAttackChainPattern(AttackChainPattern pattern) {
        attackChainPatterns.put(pattern.getPatternName(), pattern);
        log.info("添加攻击链模式: {}", pattern.getPatternName());
    }
    
    /**
     * 获取知识库统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Integer> getStatistics() {
        Map<String, Integer> stats = new ConcurrentHashMap<>();
        stats.put("threatTypes", threatToStageMapping.size());
        stats.put("techniques", techniqueToStageMapping.size());
        stats.put("tactics", tacticToStageMapping.size());
        stats.put("iocPatterns", iocPatternToStageMapping.size());
        stats.put("attackChainPatterns", attackChainPatterns.size());
        return stats;
    }
}
