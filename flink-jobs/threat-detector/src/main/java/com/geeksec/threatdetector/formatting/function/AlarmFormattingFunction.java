package com.geeksec.threatdetector.formatting.function;

import com.geeksec.threatdetector.formatting.AlarmFormatter;
import com.geeksec.threatdetector.formatting.model.AlarmFormattedContent;
import com.geeksec.threatdetector.formatting.model.FormattingContext;
import com.geeksec.threatdetector.model.output.Alarm;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 告警格式化Flink函数
 * 在Flink数据流中对告警进行格式化处理
 * 
 * <AUTHOR>
 */
@Slf4j
public class AlarmFormattingFunction extends RichMapFunction<Alarm, Alarm> {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 告警格式化器
     */
    private transient AlarmFormatter alarmFormatter;
    
    /**
     * 格式化上下文
     */
    private final FormattingContext formattingContext;
    
    /**
     * 是否启用格式化
     */
    private final boolean formattingEnabled;
    
    /**
     * 是否将格式化内容添加到告警中
     */
    private final boolean attachFormattedContent;
    
    /**
     * 统计信息输出间隔（秒）
     */
    private final int statisticsIntervalSeconds;
    
    /**
     * 定时任务执行器
     */
    private transient ScheduledExecutorService scheduledExecutor;
    
    /**
     * 统计信息
     */
    private transient long processedCount = 0;
    private transient long formattedCount = 0;
    private transient long errorCount = 0;
    private transient long startTime = 0;
    
    /**
     * 构造函数
     * 
     * @param formattingContext 格式化上下文
     * @param formattingEnabled 是否启用格式化
     * @param attachFormattedContent 是否将格式化内容添加到告警中
     * @param statisticsIntervalSeconds 统计信息输出间隔
     */
    public AlarmFormattingFunction(FormattingContext formattingContext,
                                 boolean formattingEnabled,
                                 boolean attachFormattedContent,
                                 int statisticsIntervalSeconds) {
        this.formattingContext = formattingContext != null ? formattingContext : FormattingContext.createDefault();
        this.formattingEnabled = formattingEnabled;
        this.attachFormattedContent = attachFormattedContent;
        this.statisticsIntervalSeconds = statisticsIntervalSeconds;
    }
    
    /**
     * 默认构造函数
     */
    public AlarmFormattingFunction() {
        this(FormattingContext.createDefault(), true, true, 60);
    }
    
    /**
     * 创建简洁格式化函数
     * 
     * @return 简洁格式化函数
     */
    public static AlarmFormattingFunction createBrief() {
        return new AlarmFormattingFunction(
                FormattingContext.createBrief(),
                true,
                true,
                60
        );
    }
    
    /**
     * 创建技术格式化函数
     * 
     * @return 技术格式化函数
     */
    public static AlarmFormattingFunction createTechnical() {
        return new AlarmFormattingFunction(
                FormattingContext.createTechnical(),
                true,
                true,
                60
        );
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        if (!formattingEnabled) {
            log.info("告警格式化功能已禁用");
            return;
        }
        
        // 初始化告警格式化器
        alarmFormatter = new AlarmFormatter();
        
        // 记录开始时间
        startTime = System.currentTimeMillis();
        
        // 启动定时任务
        if (statisticsIntervalSeconds > 0) {
            scheduledExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
                Thread t = new Thread(r, "AlarmFormatting-Timer");
                t.setDaemon(true);
                return t;
            });
            
            scheduledExecutor.scheduleAtFixedRate(
                    this::printStatistics,
                    statisticsIntervalSeconds,
                    statisticsIntervalSeconds,
                    TimeUnit.SECONDS
            );
        }
        
        log.info("告警格式化函数初始化完成，格式化策略: {}, 语言: {}", 
                formattingContext.getStrategy(), formattingContext.getLanguage());
    }
    
    @Override
    public Alarm map(Alarm alarm) throws Exception {
        if (alarm == null) {
            return null;
        }
        
        processedCount++;
        
        // 如果未启用格式化，直接返回原告警
        if (!formattingEnabled || alarmFormatter == null) {
            return alarm;
        }
        
        try {
            // 执行格式化
            AlarmFormattedContent formattedContent = alarmFormatter.formatAlarm(alarm, formattingContext);
            
            if (formattedContent != null) {
                formattedCount++;
                
                // 如果需要将格式化内容附加到告警中
                if (attachFormattedContent) {
                    alarm = attachFormattedContentToAlarm(alarm, formattedContent);
                }
                
                log.debug("告警格式化完成: {}, 策略: {}", 
                        alarm.getAlarmId(), formattingContext.getStrategy());
            } else {
                errorCount++;
                log.warn("告警格式化返回空结果: {}", alarm.getAlarmId());
            }
            
        } catch (Exception e) {
            errorCount++;
            log.error("告警格式化失败: {}, 错误: {}", alarm.getAlarmId(), e.getMessage(), e);
        }
        
        return alarm;
    }
    
    @Override
    public void close() throws Exception {
        super.close();
        
        log.info("关闭告警格式化函数");
        
        // 关闭定时任务
        if (scheduledExecutor != null) {
            scheduledExecutor.shutdown();
            try {
                if (!scheduledExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduledExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduledExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 打印最终统计信息
        printFinalStatistics();
    }
    
    /**
     * 将格式化内容附加到告警中
     * 
     * @param alarm 原始告警
     * @param formattedContent 格式化内容
     * @return 附加了格式化内容的告警
     */
    private Alarm attachFormattedContentToAlarm(Alarm alarm, AlarmFormattedContent formattedContent) {
        // 创建告警的副本
        Alarm enrichedAlarm = alarm.toBuilder().build();
        
        // 更新描述信息
        if (formattedContent.getSummary() != null) {
            if (formattedContent.getSummary().getDetailedDescription() != null) {
                enrichedAlarm.setDescription(formattedContent.getSummary().getDetailedDescription());
            }
        }
        
        // 添加格式化内容到扩展属性
        if (enrichedAlarm.getExtensions() == null) {
            enrichedAlarm.setExtensions(new java.util.HashMap<>());
        }
        
        // 添加格式化摘要
        if (formattedContent.getSummary() != null) {
            enrichedAlarm.getExtensions().put("formatted_summary", formattedContent.getSummary());
        }
        
        // 添加处理建议
        if (formattedContent.getHandlingSuggestions() != null) {
            enrichedAlarm.getExtensions().put("handling_suggestions", formattedContent.getHandlingSuggestions());
        }
        
        // 添加原理说明
        if (formattedContent.getPrincipleExplanation() != null) {
            enrichedAlarm.getExtensions().put("principle_explanation", formattedContent.getPrincipleExplanation());
        }
        
        // 添加影响分析
        if (formattedContent.getImpactAnalysis() != null) {
            enrichedAlarm.getExtensions().put("impact_analysis", formattedContent.getImpactAnalysis());
        }
        
        // 添加格式化元信息
        enrichedAlarm.getExtensions().put("formatting_strategy", formattedContent.getStrategy());
        enrichedAlarm.getExtensions().put("formatting_time", formattedContent.getFormattedTime());
        enrichedAlarm.getExtensions().put("formatting_version", formattedContent.getFormattingVersion());
        
        return enrichedAlarm;
    }
    
    /**
     * 定时打印统计信息
     */
    private void printStatistics() {
        try {
            long currentTime = System.currentTimeMillis();
            long runningTime = currentTime - startTime;
            
            log.info("=== 告警格式化统计信息 ===");
            log.info("运行时间: {}秒", runningTime / 1000);
            log.info("处理告警数: {}", processedCount);
            log.info("格式化成功数: {}", formattedCount);
            log.info("格式化失败数: {}", errorCount);
            
            if (processedCount > 0) {
                double successRate = (double) formattedCount / processedCount * 100;
                double errorRate = (double) errorCount / processedCount * 100;
                log.info("格式化成功率: {:.2f}%", successRate);
                log.info("格式化失败率: {:.2f}%", errorRate);
            }
            
            if (runningTime > 0) {
                double throughput = (double) processedCount / (runningTime / 1000.0);
                log.info("处理吞吐量: {:.2f} 告警/秒", throughput);
            }
            
            // 格式化器统计信息
            if (alarmFormatter != null) {
                AlarmFormatter.FormattingStatistics formatterStats = alarmFormatter.getStatistics();
                log.info("格式化器统计:");
                log.info("  总格式化数: {}", formatterStats.getTotalFormatted());
                log.info("  缓存命中数: {}", formatterStats.getCacheHits());
                log.info("  缓存未命中数: {}", formatterStats.getCacheMisses());
                log.info("  缓存命中率: {:.2f}%", formatterStats.getCacheHitRate() * 100);
                log.info("  格式化错误数: {}", formatterStats.getFormattingErrors());
                log.info("  缓存大小: {}", formatterStats.getCacheSize());
            }
            
            log.info("=== 统计信息完成 ===");
            
        } catch (Exception e) {
            log.error("打印统计信息失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 打印最终统计信息
     */
    private void printFinalStatistics() {
        long currentTime = System.currentTimeMillis();
        long totalRunningTime = currentTime - startTime;
        
        log.info("=== 告警格式化最终统计 ===");
        log.info("总运行时间: {}秒", totalRunningTime / 1000);
        log.info("总处理告警数: {}", processedCount);
        log.info("格式化成功数: {}", formattedCount);
        log.info("格式化失败数: {}", errorCount);
        
        if (processedCount > 0) {
            double successRate = (double) formattedCount / processedCount * 100;
            double errorRate = (double) errorCount / processedCount * 100;
            log.info("格式化成功率: {:.2f}%", successRate);
            log.info("格式化失败率: {:.2f}%", errorRate);
        }
        
        if (totalRunningTime > 0) {
            double avgThroughput = (double) processedCount / (totalRunningTime / 1000.0);
            log.info("平均吞吐量: {:.2f} 告警/秒", avgThroughput);
        }
        
        if (alarmFormatter != null) {
            AlarmFormatter.FormattingStatistics formatterStats = alarmFormatter.getStatistics();
            log.info("格式化器最终统计:");
            log.info("  总格式化数: {}", formatterStats.getTotalFormatted());
            log.info("  缓存命中率: {:.2f}%", formatterStats.getCacheHitRate() * 100);
            log.info("  格式化错误率: {:.2f}%", formatterStats.getErrorRate() * 100);
            log.info("  最终缓存大小: {}", formatterStats.getCacheSize());
        }
        
        log.info("=== 最终统计完成 ===");
    }
    
    /**
     * 获取当前统计信息
     * 
     * @return 统计信息
     */
    public FormattingFunctionStatistics getCurrentStatistics() {
        long currentTime = System.currentTimeMillis();
        long runningTime = currentTime - startTime;
        
        return new FormattingFunctionStatistics(
                processedCount,
                formattedCount,
                errorCount,
                runningTime,
                formattingContext.getStrategy(),
                formattingContext.getLanguage()
        );
    }
    
    /**
     * 格式化函数统计信息
     */
    public static class FormattingFunctionStatistics {
        private final long processedCount;
        private final long formattedCount;
        private final long errorCount;
        private final long runningTimeMs;
        private final AlarmFormattedContent.FormattingStrategy strategy;
        private final String language;
        
        public FormattingFunctionStatistics(long processedCount, long formattedCount, long errorCount,
                                          long runningTimeMs, AlarmFormattedContent.FormattingStrategy strategy,
                                          String language) {
            this.processedCount = processedCount;
            this.formattedCount = formattedCount;
            this.errorCount = errorCount;
            this.runningTimeMs = runningTimeMs;
            this.strategy = strategy;
            this.language = language;
        }
        
        public double getSuccessRate() {
            return processedCount > 0 ? (double) formattedCount / processedCount : 0.0;
        }
        
        public double getErrorRate() {
            return processedCount > 0 ? (double) errorCount / processedCount : 0.0;
        }
        
        public double getThroughput() {
            return runningTimeMs > 0 ? (double) processedCount / (runningTimeMs / 1000.0) : 0.0;
        }
        
        // Getters
        public long getProcessedCount() { return processedCount; }
        public long getFormattedCount() { return formattedCount; }
        public long getErrorCount() { return errorCount; }
        public long getRunningTimeMs() { return runningTimeMs; }
        public AlarmFormattedContent.FormattingStrategy getStrategy() { return strategy; }
        public String getLanguage() { return language; }
    }
}
