package com.geeksec.threatdetector.killchain.analyzer;

import com.geeksec.threatdetector.killchain.knowledge.AttackChainKnowledgeBase;
import com.geeksec.threatdetector.killchain.model.AttackChainEvent;
import com.geeksec.threatdetector.killchain.model.CyberKillChainStage;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 攻击链阶段分类器
 * 负责将攻击事件分类到相应的Cyber Kill Chain阶段
 * 
 * <AUTHOR>
 */
@Slf4j
public class StageClassifier implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 攻击链知识库
     */
    private final AttackChainKnowledgeBase knowledgeBase;
    
    /**
     * 分类规则缓存
     */
    private final Map<String, CyberKillChainStage> classificationCache;
    
    /**
     * 构造函数
     * 
     * @param knowledgeBase 攻击链知识库
     */
    public StageClassifier(AttackChainKnowledgeBase knowledgeBase) {
        this.knowledgeBase = knowledgeBase;
        this.classificationCache = new ConcurrentHashMap<>();
    }
    
    /**
     * 分类攻击事件到Kill Chain阶段
     * 
     * @param event 攻击事件
     * @return Kill Chain阶段
     */
    public CyberKillChainStage classifyEvent(AttackChainEvent event) {
        if (event == null) {
            return CyberKillChainStage.RECONNAISSANCE;
        }
        
        try {
            // 1. 基于威胁类型分类
            CyberKillChainStage stageByThreat = classifyByThreatType(event);
            if (stageByThreat != null) {
                return stageByThreat;
            }
            
            // 2. 基于攻击技术分类
            CyberKillChainStage stageByTechnique = classifyByTechnique(event);
            if (stageByTechnique != null) {
                return stageByTechnique;
            }
            
            // 3. 基于网络行为分类
            CyberKillChainStage stageByBehavior = classifyByNetworkBehavior(event);
            if (stageByBehavior != null) {
                return stageByBehavior;
            }
            
            // 4. 基于威胁指标分类
            CyberKillChainStage stageByIndicators = classifyByThreatIndicators(event);
            if (stageByIndicators != null) {
                return stageByIndicators;
            }
            
            // 5. 基于事件描述分类
            CyberKillChainStage stageByDescription = classifyByDescription(event);
            if (stageByDescription != null) {
                return stageByDescription;
            }
            
            // 默认返回侦察阶段
            log.debug("无法精确分类事件，使用默认阶段: {}", event.getEventId());
            return CyberKillChainStage.RECONNAISSANCE;
            
        } catch (Exception e) {
            log.error("事件分类失败: {}, 错误: {}", event.getEventId(), e.getMessage(), e);
            return CyberKillChainStage.RECONNAISSANCE;
        }
    }
    
    /**
     * 基于威胁类型分类
     * 
     * @param event 攻击事件
     * @return Kill Chain阶段
     */
    private CyberKillChainStage classifyByThreatType(AttackChainEvent event) {
        String threatType = event.getThreatType();
        if (threatType == null) {
            return null;
        }
        
        // 检查缓存
        String cacheKey = "threat:" + threatType;
        CyberKillChainStage cachedStage = classificationCache.get(cacheKey);
        if (cachedStage != null) {
            return cachedStage;
        }
        
        // 从知识库获取可能的阶段
        List<CyberKillChainStage> possibleStages = knowledgeBase.getStagesByThreatType(threatType);
        if (possibleStages != null && !possibleStages.isEmpty()) {
            // 如果有多个可能的阶段，选择第一个（通常是最典型的）
            CyberKillChainStage selectedStage = possibleStages.get(0);
            
            // 如果有多个阶段，需要进一步分析
            if (possibleStages.size() > 1) {
                selectedStage = selectBestStageFromMultiple(event, possibleStages);
            }
            
            // 缓存结果
            classificationCache.put(cacheKey, selectedStage);
            return selectedStage;
        }
        
        return null;
    }
    
    /**
     * 基于攻击技术分类
     * 
     * @param event 攻击事件
     * @return Kill Chain阶段
     */
    private CyberKillChainStage classifyByTechnique(AttackChainEvent event) {
        String technique = event.getAttackTechnique();
        if (technique == null) {
            return null;
        }
        
        String cacheKey = "technique:" + technique;
        CyberKillChainStage cachedStage = classificationCache.get(cacheKey);
        if (cachedStage != null) {
            return cachedStage;
        }
        
        CyberKillChainStage stage = knowledgeBase.getStageByTechnique(technique);
        if (stage != null) {
            classificationCache.put(cacheKey, stage);
        }
        
        return stage;
    }
    
    /**
     * 基于网络行为分类
     * 
     * @param event 攻击事件
     * @return Kill Chain阶段
     */
    private CyberKillChainStage classifyByNetworkBehavior(AttackChainEvent event) {
        // 基于端口和协议分析
        Integer targetPort = event.getTargetPort();
        String protocol = event.getProtocol();
        
        if (targetPort != null) {
            // 常见扫描端口
            if (isCommonScanPort(targetPort)) {
                return CyberKillChainStage.RECONNAISSANCE;
            }
            
            // Web服务端口
            if (isWebServicePort(targetPort)) {
                // 可能是投递、利用或安装阶段
                return determineWebServiceStage(event);
            }
            
            // 远程管理端口
            if (isRemoteManagementPort(targetPort)) {
                return CyberKillChainStage.COMMAND_AND_CONTROL;
            }
        }
        
        // 基于协议分析
        if ("DNS".equalsIgnoreCase(protocol)) {
            // DNS查询可能是侦察或C2通信
            return determineDNSStage(event);
        }
        
        return null;
    }
    
    /**
     * 基于威胁指标分类
     * 
     * @param event 攻击事件
     * @return Kill Chain阶段
     */
    private CyberKillChainStage classifyByThreatIndicators(AttackChainEvent event) {
        if (event.getIndicators() == null || event.getIndicators().isEmpty()) {
            return null;
        }
        
        for (AttackChainEvent.ThreatIndicator indicator : event.getIndicators()) {
            // 基于指标类型判断
            switch (indicator.getType()) {
                case IP_ADDRESS:
                    // IP地址指标可能表示多个阶段
                    break;
                case DOMAIN:
                    // 域名指标通常表示C2通信
                    return CyberKillChainStage.COMMAND_AND_CONTROL;
                case URL:
                    // URL指标可能是投递或利用
                    return CyberKillChainStage.DELIVERY;
                case FILE_HASH:
                    // 文件哈希通常表示武器化或安装
                    return CyberKillChainStage.WEAPONIZATION;
                default:
                    break;
            }
        }
        
        return null;
    }
    
    /**
     * 基于事件描述分类
     * 
     * @param event 攻击事件
     * @return Kill Chain阶段
     */
    private CyberKillChainStage classifyByDescription(AttackChainEvent event) {
        String description = event.getDescription();
        if (description == null) {
            return null;
        }
        
        String lowerDesc = description.toLowerCase();
        
        // 侦察阶段关键词
        if (containsAny(lowerDesc, "扫描", "探测", "侦察", "枚举", "发现")) {
            return CyberKillChainStage.RECONNAISSANCE;
        }
        
        // 投递阶段关键词
        if (containsAny(lowerDesc, "钓鱼", "邮件", "附件", "下载", "投递")) {
            return CyberKillChainStage.DELIVERY;
        }
        
        // 利用阶段关键词
        if (containsAny(lowerDesc, "漏洞", "利用", "溢出", "注入", "执行")) {
            return CyberKillChainStage.EXPLOITATION;
        }
        
        // 安装阶段关键词
        if (containsAny(lowerDesc, "安装", "植入", "后门", "webshell", "持久化")) {
            return CyberKillChainStage.INSTALLATION;
        }
        
        // 命令控制阶段关键词
        if (containsAny(lowerDesc, "c2", "命令", "控制", "通信", "隧道", "回连")) {
            return CyberKillChainStage.COMMAND_AND_CONTROL;
        }
        
        // 目标行动阶段关键词
        if (containsAny(lowerDesc, "窃取", "泄露", "加密", "勒索", "破坏", "横向")) {
            return CyberKillChainStage.ACTIONS_ON_OBJECTIVES;
        }
        
        return null;
    }
    
    /**
     * 从多个可能的阶段中选择最佳阶段
     * 
     * @param event 攻击事件
     * @param possibleStages 可能的阶段列表
     * @return 最佳阶段
     */
    private CyberKillChainStage selectBestStageFromMultiple(AttackChainEvent event, 
                                                          List<CyberKillChainStage> possibleStages) {
        // 基于事件的其他特征进一步判断
        
        // 如果有置信度信息，优先选择后期阶段（更严重）
        if (event.getConfidence() != null && event.getConfidence() > 0.8) {
            return possibleStages.stream()
                    .max((s1, s2) -> Integer.compare(s1.getStageNumber(), s2.getStageNumber()))
                    .orElse(possibleStages.get(0));
        }
        
        // 基于事件严重程度选择
        if (event.getSeverity() == AttackChainEvent.EventSeverity.CRITICAL) {
            // 关键事件通常在后期阶段
            return possibleStages.stream()
                    .filter(CyberKillChainStage::isLateStage)
                    .findFirst()
                    .orElse(possibleStages.get(0));
        }
        
        // 默认选择第一个
        return possibleStages.get(0);
    }
    
    /**
     * 判断是否为常见扫描端口
     * 
     * @param port 端口号
     * @return 是否为扫描端口
     */
    private boolean isCommonScanPort(int port) {
        int[] scanPorts = {21, 22, 23, 25, 53, 80, 110, 135, 139, 143, 443, 993, 995, 1433, 3389};
        for (int scanPort : scanPorts) {
            if (port == scanPort) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 判断是否为Web服务端口
     * 
     * @param port 端口号
     * @return 是否为Web服务端口
     */
    private boolean isWebServicePort(int port) {
        return port == 80 || port == 443 || port == 8080 || port == 8443;
    }
    
    /**
     * 判断是否为远程管理端口
     * 
     * @param port 端口号
     * @return 是否为远程管理端口
     */
    private boolean isRemoteManagementPort(int port) {
        return port == 22 || port == 23 || port == 3389 || port == 5900;
    }
    
    /**
     * 确定Web服务相关的阶段
     * 
     * @param event 攻击事件
     * @return Kill Chain阶段
     */
    private CyberKillChainStage determineWebServiceStage(AttackChainEvent event) {
        String threatType = event.getThreatType();
        if (threatType != null) {
            if (threatType.contains("Webshell") || threatType.contains("后门")) {
                return CyberKillChainStage.INSTALLATION;
            } else if (threatType.contains("注入") || threatType.contains("漏洞")) {
                return CyberKillChainStage.EXPLOITATION;
            }
        }
        
        // 默认为投递阶段
        return CyberKillChainStage.DELIVERY;
    }
    
    /**
     * 确定DNS相关的阶段
     * 
     * @param event 攻击事件
     * @return Kill Chain阶段
     */
    private CyberKillChainStage determineDNSStage(AttackChainEvent event) {
        String threatType = event.getThreatType();
        if (threatType != null && threatType.contains("隧道")) {
            return CyberKillChainStage.COMMAND_AND_CONTROL;
        }
        
        // 大量DNS查询可能是侦察
        return CyberKillChainStage.RECONNAISSANCE;
    }
    
    /**
     * 检查字符串是否包含任意一个关键词
     * 
     * @param text 文本
     * @param keywords 关键词数组
     * @return 是否包含
     */
    private boolean containsAny(String text, String... keywords) {
        for (String keyword : keywords) {
            if (text.contains(keyword)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 清理分类缓存
     */
    public void clearCache() {
        classificationCache.clear();
        log.info("分类缓存已清理");
    }
    
    /**
     * 获取缓存统计信息
     * 
     * @return 缓存大小
     */
    public int getCacheSize() {
        return classificationCache.size();
    }
}
