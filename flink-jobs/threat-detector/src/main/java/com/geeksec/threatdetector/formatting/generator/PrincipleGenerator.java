package com.geeksec.threatdetector.formatting.generator;

import com.geeksec.threatdetector.formatting.knowledge.ThreatKnowledgeBase;
import com.geeksec.threatdetector.formatting.model.AlarmFormattedContent;
import com.geeksec.threatdetector.formatting.model.FormattingContext;
import com.geeksec.threatdetector.model.output.Alarm;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 原理说明生成器
 * 根据告警信息生成检测原理和技术背景说明
 * 
 * <AUTHOR>
 */
@Slf4j
public class PrincipleGenerator implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 威胁知识库
     */
    private final ThreatKnowledgeBase knowledgeBase;
    
    /**
     * 原理模板缓存
     */
    private final Map<String, PrincipleTemplate> principleTemplateCache;
    
    /**
     * 构造函数
     * 
     * @param knowledgeBase 威胁知识库
     */
    public PrincipleGenerator(ThreatKnowledgeBase knowledgeBase) {
        this.knowledgeBase = knowledgeBase;
        this.principleTemplateCache = new ConcurrentHashMap<>();
        initializePrincipleTemplates();
    }
    
    /**
     * 生成原理说明
     * 
     * @param alarm 告警信息
     * @param context 格式化上下文
     * @return 原理说明
     */
    public AlarmFormattedContent.PrincipleExplanation generatePrincipleExplanation(Alarm alarm, FormattingContext context) {
        if (alarm == null) {
            return null;
        }
        
        try {
            String threatType = alarm.getThreatType();
            
            // 生成检测原理
            String detectionPrinciple = generateDetectionPrinciple(alarm, context);
            
            // 生成技术背景
            String technicalBackground = generateTechnicalBackground(alarm, context);
            
            // 生成攻击原理
            String attackPrinciple = generateAttackPrinciple(alarm, context);
            
            // 生成检测方法
            String detectionMethod = generateDetectionMethod(alarm, context);
            
            // 生成相关技术
            List<String> relatedTechnologies = generateRelatedTechnologies(alarm, context);
            
            // 生成参考资料
            List<AlarmFormattedContent.Reference> references = generateReferences(alarm, context);
            
            return AlarmFormattedContent.PrincipleExplanation.builder()
                    .detectionPrinciple(detectionPrinciple)
                    .technicalBackground(technicalBackground)
                    .attackPrinciple(attackPrinciple)
                    .detectionMethod(detectionMethod)
                    .relatedTechnologies(relatedTechnologies)
                    .references(references)
                    .build();
            
        } catch (Exception e) {
            log.error("生成原理说明失败: {}", e.getMessage(), e);
            return createDefaultPrincipleExplanation(alarm);
        }
    }
    
    /**
     * 生成检测原理
     * 
     * @param alarm 告警信息
     * @param context 格式化上下文
     * @return 检测原理
     */
    private String generateDetectionPrinciple(Alarm alarm, FormattingContext context) {
        String threatType = alarm.getThreatType();
        String detectorDisplayName = alarm.getDetectorDisplayName();
        
        // 从知识库获取检测原理
        ThreatKnowledgeBase.ThreatInfo threatInfo = knowledgeBase.getThreatInfo(threatType);
        if (threatInfo != null && threatInfo.getDetectionPrinciple() != null) {
            return threatInfo.getDetectionPrinciple();
        }
        
        // 根据威胁类型生成检测原理
        switch (threatType) {
            case "恶意软件":
                return generateMalwareDetectionPrinciple(alarm, context);
            case "网络扫描":
                return generateScanDetectionPrinciple(alarm, context);
            case "DNS隧道":
                return generateDnsTunnelDetectionPrinciple(alarm, context);
            case "Webshell":
                return generateWebshellDetectionPrinciple(alarm, context);
            case "加密工具":
                return generateEncryptedToolDetectionPrinciple(alarm, context);
            default:
                return generateGenericDetectionPrinciple(alarm, context);
        }
    }
    
    /**
     * 生成恶意软件检测原理
     */
    private String generateMalwareDetectionPrinciple(Alarm alarm, FormattingContext context) {
        StringBuilder principle = new StringBuilder();
        
        principle.append("恶意软件检测基于多层检测技术：");
        principle.append("1) 特征码匹配：通过比对已知恶意软件的特征码数据库，识别已知威胁；");
        principle.append("2) 行为分析：监控程序运行时的行为模式，如文件操作、网络通信、注册表修改等，识别可疑行为；");
        principle.append("3) 启发式检测：基于恶意软件的常见特征和行为模式，使用算法识别未知威胁；");
        principle.append("4) 沙箱分析：在隔离环境中执行可疑文件，观察其行为特征；");
        principle.append("5) 机器学习：使用训练好的模型分析文件特征，预测恶意概率。");
        
        if (alarm.getConfidence() != null) {
            principle.append(String.format(" 当前检测置信度为%.2f，", alarm.getConfidence()));
            if (alarm.getConfidence() >= 0.9) {
                principle.append("表明检测结果高度可信。");
            } else if (alarm.getConfidence() >= 0.7) {
                principle.append("表明检测结果较为可信。");
            } else {
                principle.append("建议进一步验证。");
            }
        }
        
        return principle.toString();
    }
    
    /**
     * 生成网络扫描检测原理
     */
    private String generateScanDetectionPrinciple(Alarm alarm, FormattingContext context) {
        StringBuilder principle = new StringBuilder();
        
        principle.append("网络扫描检测基于流量行为分析：");
        principle.append("1) 连接模式分析：检测短时间内对多个端口或主机的连接尝试；");
        principle.append("2) 频率统计：分析连接频率，识别异常高频的网络活动；");
        principle.append("3) 端口序列分析：检测按顺序扫描端口的行为模式；");
        principle.append("4) 响应时间分析：分析连接响应时间，识别扫描工具的特征；");
        principle.append("5) 负载分析：检测扫描数据包的特征负载。");
        
        if (alarm.getSrcIp() != null && alarm.getDstIp() != null) {
            principle.append(String.format(" 检测到从%s到%s的扫描行为，", alarm.getSrcIp(), alarm.getDstIp()));
            if (alarm.getSrcPort() != null && alarm.getDstPort() != null) {
                principle.append(String.format("涉及端口%d到%d。", alarm.getSrcPort(), alarm.getDstPort()));
            }
        }
        
        return principle.toString();
    }
    
    /**
     * 生成DNS隧道检测原理
     */
    private String generateDnsTunnelDetectionPrinciple(Alarm alarm, FormattingContext context) {
        StringBuilder principle = new StringBuilder();
        
        principle.append("DNS隧道检测基于DNS流量异常分析：");
        principle.append("1) 查询频率分析：检测异常高频的DNS查询；");
        principle.append("2) 域名特征分析：识别异常长度、随机字符、编码特征的域名；");
        principle.append("3) 查询类型分析：检测非常规的DNS查询类型和模式；");
        principle.append("4) 响应大小分析：分析DNS响应的大小分布，识别数据传输；");
        principle.append("5) 时间模式分析：检测DNS查询的时间间隔模式；");
        principle.append("6) 熵值分析：计算域名的随机性，识别编码数据。");
        
        principle.append(" DNS隧道通常利用DNS协议的特性，将数据编码在域名或DNS记录中，");
        principle.append("绕过传统的网络安全检测，建立隐蔽的通信通道。");
        
        return principle.toString();
    }
    
    /**
     * 生成Webshell检测原理
     */
    private String generateWebshellDetectionPrinciple(Alarm alarm, FormattingContext context) {
        StringBuilder principle = new StringBuilder();
        
        principle.append("Webshell检测基于Web流量和文件行为分析：");
        principle.append("1) HTTP请求特征分析：检测异常的HTTP请求模式、参数和头部；");
        principle.append("2) 文件上传行为分析：监控文件上传操作，识别可疑脚本文件；");
        principle.append("3) 代码特征匹配：扫描Web目录中的文件，匹配已知Webshell特征；");
        principle.append("4) 执行行为监控：监控Web应用的命令执行、文件操作等行为；");
        principle.append("5) 通信模式分析：分析Webshell与攻击者的通信模式。");
        
        principle.append(" Webshell通常以PHP、ASP、JSP等脚本形式存在，");
        principle.append("为攻击者提供远程控制Web服务器的能力，");
        principle.append("可用于数据窃取、权限提升、横向移动等恶意活动。");
        
        return principle.toString();
    }
    
    /**
     * 生成加密工具检测原理
     */
    private String generateEncryptedToolDetectionPrinciple(Alarm alarm, FormattingContext context) {
        StringBuilder principle = new StringBuilder();
        
        principle.append("加密工具检测基于加密流量分析：");
        principle.append("1) 证书分析：检测SSL/TLS证书的异常特征，如自签名、异常字段等；");
        principle.append("2) 流量模式分析：分析加密流量的大小、频率、时间模式；");
        principle.append("3) 握手特征分析：检测SSL/TLS握手过程的异常特征；");
        principle.append("4) 加密算法分析：识别非标准或弱加密算法的使用；");
        principle.append("5) 元数据分析：分析加密连接的元数据特征。");
        
        principle.append(" 恶意加密工具通常使用加密技术隐藏恶意通信，");
        principle.append("规避传统的基于明文的检测方法，");
        principle.append("需要通过分析加密流量的元特征来识别威胁。");
        
        return principle.toString();
    }
    
    /**
     * 生成通用检测原理
     */
    private String generateGenericDetectionPrinciple(Alarm alarm, FormattingContext context) {
        StringBuilder principle = new StringBuilder();
        
        principle.append("威胁检测基于多维度分析：");
        principle.append("1) 模式匹配：基于已知威胁特征进行模式匹配；");
        principle.append("2) 异常检测：通过统计分析识别偏离正常基线的行为；");
        principle.append("3) 规则引擎：使用预定义的检测规则识别威胁；");
        principle.append("4) 机器学习：使用训练好的模型进行威胁分类和预测。");
        
        if (alarm.getDetectorDisplayName() != null) {
            principle.append(String.format(" 当前告警由%s检测器生成，", alarm.getDetectorDisplayName()));
            principle.append("该检测器专门针对此类威胁进行了优化。");
        }
        
        return principle.toString();
    }
    
    /**
     * 生成技术背景
     */
    private String generateTechnicalBackground(Alarm alarm, FormattingContext context) {
        String threatType = alarm.getThreatType();
        
        // 从知识库获取技术背景
        ThreatKnowledgeBase.ThreatInfo threatInfo = knowledgeBase.getThreatInfo(threatType);
        if (threatInfo != null && threatInfo.getTechnicalBackground() != null) {
            return threatInfo.getTechnicalBackground();
        }
        
        // 根据威胁类型生成技术背景
        switch (threatType) {
            case "恶意软件":
                return "恶意软件是指故意设计用来损害计算机、服务器、客户端或计算机网络的软件。" +
                       "包括病毒、蠕虫、木马、勒索软件、间谍软件等多种类型。" +
                       "现代恶意软件通常具有多态性、加密、反调试等对抗技术。";
            case "网络扫描":
                return "网络扫描是攻击者收集目标信息的常用技术，属于网络侦察阶段。" +
                       "通过扫描可以发现开放端口、运行服务、操作系统版本等信息。" +
                       "常见的扫描技术包括TCP扫描、UDP扫描、SYN扫描等。";
            case "DNS隧道":
                return "DNS隧道是一种利用DNS协议进行数据传输的技术。" +
                       "由于DNS流量通常不被过滤，攻击者可以利用DNS查询和响应传输数据。" +
                       "常见的DNS隧道工具包括Iodine、DNSCat、Cobalt Strike等。";
            case "Webshell":
                return "Webshell是一种Web后门程序，通常以脚本形式存在于Web服务器上。" +
                       "攻击者可以通过Webshell远程控制Web服务器，执行命令、上传文件等。" +
                       "常见的Webshell包括中国菜刀、冰蝎、哥斯拉等。";
            default:
                return "该威胁类型涉及网络安全领域的专业技术，" +
                       "需要结合具体的攻击手法和技术特征进行分析。";
        }
    }
    
    /**
     * 生成攻击原理
     */
    private String generateAttackPrinciple(Alarm alarm, FormattingContext context) {
        String threatType = alarm.getThreatType();
        
        switch (threatType) {
            case "恶意软件":
                return "恶意软件攻击通常包括以下阶段：1) 初始感染：通过邮件附件、恶意下载、漏洞利用等方式感染目标系统；" +
                       "2) 持久化：在系统中建立持久化机制，确保重启后仍能运行；" +
                       "3) 权限提升：利用漏洞获取更高权限；" +
                       "4) 横向移动：在网络中传播，感染其他系统；" +
                       "5) 数据窃取或破坏：执行最终的恶意目标。";
            case "网络扫描":
                return "网络扫描攻击原理：攻击者首先进行网络侦察，通过扫描发现目标网络的拓扑结构、" +
                       "开放端口、运行服务等信息。基于扫描结果，攻击者可以识别潜在的攻击入口点，" +
                       "为后续的漏洞利用、暴力破解等攻击做准备。";
            case "DNS隧道":
                return "DNS隧道攻击原理：攻击者控制一个域名，将恶意软件配置为通过DNS查询与该域名通信。" +
                       "数据被编码在DNS查询的子域名中，服务器响应中包含编码的命令或数据。" +
                       "由于DNS流量看起来正常，可以绕过大多数网络安全设备的检测。";
            case "Webshell":
                return "Webshell攻击原理：攻击者首先通过文件上传漏洞、代码注入、SQL注入等方式，" +
                       "在Web服务器上植入恶意脚本文件。然后通过HTTP请求与Webshell通信，" +
                       "执行系统命令、上传文件、窃取数据等恶意操作。";
            default:
                return "该攻击利用了系统或网络的安全薄弱环节，" +
                       "通过特定的技术手段实现对目标系统的未授权访问或控制。";
        }
    }
    
    /**
     * 生成检测方法
     */
    private String generateDetectionMethod(Alarm alarm, FormattingContext context) {
        String detectorDisplayName = alarm.getDetectorDisplayName();
        
        if (detectorDisplayName != null) {
            switch (detectorDisplayName) {
                case "MalwareDetector":
                    return "采用多引擎检测技术，结合特征码匹配、行为分析、机器学习等方法进行检测。";
                case "NetworkScanDetector":
                    return "通过网络流量分析，检测异常的连接模式和端口访问行为。";
                case "DNSTunnelDetector":
                    return "基于DNS流量分析，检测异常的查询模式和域名特征。";
                case "WebshellDetector":
                    return "通过Web流量分析和文件扫描，检测Webshell的特征和行为。";
                default:
                    return "使用专门的检测算法和规则引擎进行威胁识别。";
            }
        }
        
        return "采用多层检测技术，包括签名匹配、异常检测、行为分析等方法。";
    }
    
    /**
     * 生成相关技术
     */
    private List<String> generateRelatedTechnologies(Alarm alarm, FormattingContext context) {
        List<String> technologies = new ArrayList<>();
        String threatType = alarm.getThreatType();
        
        switch (threatType) {
            case "恶意软件":
                technologies.addAll(List.of("反病毒技术", "沙箱分析", "行为监控", "机器学习", "威胁情报"));
                break;
            case "网络扫描":
                technologies.addAll(List.of("网络流量分析", "入侵检测系统", "防火墙", "网络监控"));
                break;
            case "DNS隧道":
                technologies.addAll(List.of("DNS安全", "流量分析", "域名信誉", "网络监控"));
                break;
            case "Webshell":
                technologies.addAll(List.of("Web应用防火墙", "文件完整性监控", "Web安全扫描"));
                break;
            default:
                technologies.addAll(List.of("网络安全", "威胁检测", "安全监控"));
                break;
        }
        
        return technologies;
    }
    
    /**
     * 生成参考资料
     */
    private List<AlarmFormattedContent.Reference> generateReferences(Alarm alarm, FormattingContext context) {
        List<AlarmFormattedContent.Reference> references = new ArrayList<>();
        String threatType = alarm.getThreatType();
        
        // 从知识库获取参考资料
        ThreatKnowledgeBase.ThreatInfo threatInfo = knowledgeBase.getThreatInfo(threatType);
        if (threatInfo != null && threatInfo.getReferences() != null) {
            references.addAll(threatInfo.getReferences());
        }
        
        // 添加通用参考资料
        references.add(AlarmFormattedContent.Reference.builder()
                .title("MITRE ATT&CK Framework")
                .url("https://attack.mitre.org/")
                .description("全球威胁情报框架")
                .type("框架")
                .build());
        
        references.add(AlarmFormattedContent.Reference.builder()
                .title("NIST Cybersecurity Framework")
                .url("https://www.nist.gov/cyberframework")
                .description("网络安全框架指南")
                .type("标准")
                .build());
        
        return references;
    }
    
    /**
     * 创建默认原理说明
     */
    private AlarmFormattedContent.PrincipleExplanation createDefaultPrincipleExplanation(Alarm alarm) {
        return AlarmFormattedContent.PrincipleExplanation.builder()
                .detectionPrinciple("系统基于预定义的检测规则和算法识别出可疑活动")
                .technicalBackground("该威胁涉及网络安全领域的专业技术")
                .attackPrinciple("攻击者利用系统或网络的安全薄弱环节进行攻击")
                .detectionMethod("采用多层检测技术进行威胁识别")
                .relatedTechnologies(List.of("网络安全", "威胁检测"))
                .references(new ArrayList<>())
                .build();
    }
    
    /**
     * 初始化原理模板
     */
    private void initializePrincipleTemplates() {
        // 这里可以初始化各种原理模板
        log.info("原理模板初始化完成");
    }
    
    /**
     * 原理模板类
     */
    private static class PrincipleTemplate implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private final String threatType;
        private final String detectionPrinciple;
        private final String technicalBackground;
        private final String attackPrinciple;
        
        public PrincipleTemplate(String threatType, String detectionPrinciple, 
                               String technicalBackground, String attackPrinciple) {
            this.threatType = threatType;
            this.detectionPrinciple = detectionPrinciple;
            this.technicalBackground = technicalBackground;
            this.attackPrinciple = attackPrinciple;
        }
        
        // Getters
        public String getThreatType() { return threatType; }
        public String getDetectionPrinciple() { return detectionPrinciple; }
        public String getTechnicalBackground() { return technicalBackground; }
        public String getAttackPrinciple() { return attackPrinciple; }
    }
}
