package com.geeksec.threatdetector.formatting.generator;

import com.geeksec.threatdetector.formatting.knowledge.ThreatKnowledgeBase;
import com.geeksec.threatdetector.formatting.model.AlarmFormattedContent;
import com.geeksec.threatdetector.formatting.model.FormattingContext;
import com.geeksec.threatdetector.model.output.Alarm;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 告警原因生成器
 * 根据告警信息生成详细的原因分析
 * 
 * <AUTHOR>
 */
@Slf4j
public class ReasonGenerator implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 威胁知识库
     */
    private final ThreatKnowledgeBase knowledgeBase;
    
    /**
     * 原因模板缓存
     */
    private final Map<String, ReasonTemplate> reasonTemplateCache;
    
    /**
     * 构造函数
     * 
     * @param knowledgeBase 威胁知识库
     */
    public ReasonGenerator(ThreatKnowledgeBase knowledgeBase) {
        this.knowledgeBase = knowledgeBase;
        this.reasonTemplateCache = new ConcurrentHashMap<>();
        initializeReasonTemplates();
    }
    
    /**
     * 生成告警原因分析
     * 
     * @param alarm 告警信息
     * @param context 格式化上下文
     * @return 原因分析
     */
    public AlarmFormattedContent.AlarmReasonAnalysis generateReasonAnalysis(Alarm alarm, FormattingContext context) {
        if (alarm == null) {
            return null;
        }
        
        try {
            // 生成检测原因
            List<AlarmFormattedContent.DetectionReason> detectionReasons = generateDetectionReasons(alarm, context);
            
            // 生成技术分析
            String technicalAnalysis = generateTechnicalAnalysis(alarm, context);
            
            // 生成行为分析
            String behaviorAnalysis = generateBehaviorAnalysis(alarm, context);
            
            // 生成模式匹配结果
            List<AlarmFormattedContent.PatternMatch> patternMatches = generatePatternMatches(alarm, context);
            
            // 生成置信度分析
            String confidenceAnalysis = generateConfidenceAnalysis(alarm, context);
            
            return AlarmFormattedContent.AlarmReasonAnalysis.builder()
                    .detectionReasons(detectionReasons)
                    .technicalAnalysis(technicalAnalysis)
                    .behaviorAnalysis(behaviorAnalysis)
                    .patternMatches(patternMatches)
                    .confidenceAnalysis(confidenceAnalysis)
                    .build();
            
        } catch (Exception e) {
            log.error("生成告警原因分析失败: {}", e.getMessage(), e);
            return createDefaultReasonAnalysis(alarm);
        }
    }
    
    /**
     * 生成检测原因
     * 
     * @param alarm 告警信息
     * @param context 格式化上下文
     * @return 检测原因列表
     */
    private List<AlarmFormattedContent.DetectionReason> generateDetectionReasons(Alarm alarm, FormattingContext context) {
        List<AlarmFormattedContent.DetectionReason> reasons = new ArrayList<>();
        
        String threatType = alarm.getThreatType();
        String alarmType = alarm.getAlarmType();
        
        // 根据威胁类型生成原因
        ReasonTemplate template = reasonTemplateCache.get(threatType);
        if (template != null) {
            reasons.addAll(generateReasonsFromTemplate(alarm, template, context));
        }
        
        // 根据检测器类型生成原因
        if (alarm.getDetectorType() != null) {
            AlarmFormattedContent.DetectionReason detectorReason = generateDetectorReason(alarm, context);
            if (detectorReason != null) {
                reasons.add(detectorReason);
            }
        }
        
        // 根据置信度生成原因
        if (alarm.getConfidence() != null) {
            AlarmFormattedContent.DetectionReason confidenceReason = generateConfidenceReason(alarm, context);
            if (confidenceReason != null) {
                reasons.add(confidenceReason);
            }
        }
        
        return reasons;
    }
    
    /**
     * 从模板生成原因
     * 
     * @param alarm 告警信息
     * @param template 原因模板
     * @param context 格式化上下文
     * @return 检测原因列表
     */
    private List<AlarmFormattedContent.DetectionReason> generateReasonsFromTemplate(
            Alarm alarm, ReasonTemplate template, FormattingContext context) {
        
        List<AlarmFormattedContent.DetectionReason> reasons = new ArrayList<>();
        
        for (ReasonTemplate.ReasonPattern pattern : template.getPatterns()) {
            if (pattern.matches(alarm)) {
                AlarmFormattedContent.DetectionReason reason = AlarmFormattedContent.DetectionReason.builder()
                        .reasonType(pattern.getReasonType())
                        .description(pattern.generateDescription(alarm))
                        .detectedFeature(pattern.getFeatureName())
                        .actualValue(pattern.extractActualValue(alarm))
                        .expectedValue(pattern.getExpectedValue())
                        .importance(pattern.getImportance())
                        .build();
                reasons.add(reason);
            }
        }
        
        return reasons;
    }
    
    /**
     * 生成检测器原因
     * 
     * @param alarm 告警信息
     * @param context 格式化上下文
     * @return 检测原因
     */
    private AlarmFormattedContent.DetectionReason generateDetectorReason(Alarm alarm, FormattingContext context) {
        if (alarm.getDetectorType() == null) {
            return null;
        }
        String detectorDisplayName = alarm.getDetectorDisplayName();
        String description;
        
        switch (detectorDisplayName) {
            case "MalwareDetector":
                description = "恶意软件检测器通过特征码匹配和行为分析识别出恶意软件活动";
                break;
            case "NetworkScanDetector":
                description = "网络扫描检测器通过分析连接模式和端口访问行为识别出扫描活动";
                break;
            case "DNSTunnelDetector":
                description = "DNS隧道检测器通过分析DNS查询模式和异常域名特征识别出隧道通信";
                break;
            case "WebshellDetector":
                description = "Webshell检测器通过分析Web请求特征和文件行为识别出后门程序";
                break;
            default:
                description = String.format("%s检测器识别出可疑活动", detectorDisplayName);
                break;
        }
        
        return AlarmFormattedContent.DetectionReason.builder()
                .reasonType("检测器匹配")
                .description(description)
                .detectedFeature("检测器规则")
                .actualValue(detectorDisplayName)
                .importance(AlarmFormattedContent.ImportanceLevel.HIGH)
                .build();
    }
    
    /**
     * 生成置信度原因
     * 
     * @param alarm 告警信息
     * @param context 格式化上下文
     * @return 检测原因
     */
    private AlarmFormattedContent.DetectionReason generateConfidenceReason(Alarm alarm, FormattingContext context) {
        Double confidence = alarm.getConfidence();
        String description;
        AlarmFormattedContent.ImportanceLevel importance;
        
        if (confidence >= 0.9) {
            description = "检测置信度极高，强烈建议立即处理";
            importance = AlarmFormattedContent.ImportanceLevel.CRITICAL;
        } else if (confidence >= 0.7) {
            description = "检测置信度较高，建议优先处理";
            importance = AlarmFormattedContent.ImportanceLevel.HIGH;
        } else if (confidence >= 0.5) {
            description = "检测置信度中等，建议进一步验证";
            importance = AlarmFormattedContent.ImportanceLevel.MEDIUM;
        } else {
            description = "检测置信度较低，可能存在误报";
            importance = AlarmFormattedContent.ImportanceLevel.LOW;
        }
        
        return AlarmFormattedContent.DetectionReason.builder()
                .reasonType("置信度评估")
                .description(description)
                .detectedFeature("检测置信度")
                .actualValue(String.format("%.2f", confidence))
                .expectedValue("> 0.5")
                .importance(importance)
                .build();
    }
    
    /**
     * 生成技术分析
     * 
     * @param alarm 告警信息
     * @param context 格式化上下文
     * @return 技术分析
     */
    private String generateTechnicalAnalysis(Alarm alarm, FormattingContext context) {
        StringBuilder analysis = new StringBuilder();
        
        // 获取威胁信息
        ThreatKnowledgeBase.ThreatInfo threatInfo = knowledgeBase.getThreatInfo(alarm.getThreatType());
        if (threatInfo != null) {
            analysis.append("技术背景: ").append(threatInfo.getTechnicalBackground()).append(" ");
            analysis.append("检测原理: ").append(threatInfo.getDetectionPrinciple()).append(" ");
        }
        
        // 添加网络信息分析
        if (alarm.getSrcIp() != null && alarm.getDstIp() != null) {
            analysis.append(String.format("网络通信分析: 检测到从%s到%s的可疑通信，", 
                    alarm.getSrcIp(), alarm.getDstIp()));
            
            if (alarm.getSrcPort() != null && alarm.getDstPort() != null) {
                analysis.append(String.format("使用端口%d到%d，", alarm.getSrcPort(), alarm.getDstPort()));
            }
            
            if (alarm.getProtocol() != null) {
                analysis.append(String.format("协议类型为%s。", alarm.getProtocol()));
            }
        }
        
        return analysis.toString();
    }
    
    /**
     * 生成行为分析
     * 
     * @param alarm 告警信息
     * @param context 格式化上下文
     * @return 行为分析
     */
    private String generateBehaviorAnalysis(Alarm alarm, FormattingContext context) {
        StringBuilder analysis = new StringBuilder();
        
        String threatType = alarm.getThreatType();
        
        switch (threatType) {
            case "恶意软件":
                analysis.append("行为特征: 检测到恶意代码执行、文件系统修改、网络通信等恶意行为模式。");
                break;
            case "网络扫描":
                analysis.append("行为特征: 检测到大量端口连接尝试、服务探测、网络映射等侦察行为。");
                break;
            case "DNS隧道":
                analysis.append("行为特征: 检测到异常DNS查询模式、高频域名解析、编码数据传输等隧道行为。");
                break;
            case "Webshell":
                analysis.append("行为特征: 检测到异常Web请求、文件上传、命令执行等后门行为。");
                break;
            default:
                analysis.append("行为特征: 检测到与正常业务模式不符的异常行为。");
                break;
        }
        
        // 添加时间模式分析
        if (alarm.getTimestamp() != null) {
            analysis.append(" 时间模式分析: 该活动发生在")
                    .append(alarm.getTimestamp().getHour())
                    .append("时，需要关注是否为异常时间段的活动。");
        }
        
        return analysis.toString();
    }
    
    /**
     * 生成模式匹配结果
     * 
     * @param alarm 告警信息
     * @param context 格式化上下文
     * @return 模式匹配列表
     */
    private List<AlarmFormattedContent.PatternMatch> generatePatternMatches(Alarm alarm, FormattingContext context) {
        List<AlarmFormattedContent.PatternMatch> matches = new ArrayList<>();
        
        // 根据告警类型生成模式匹配
        String alarmType = alarm.getAlarmType();
        if (alarmType != null && alarmType.contains("高危")) {
            matches.add(AlarmFormattedContent.PatternMatch.builder()
                    .patternName("高危威胁模式")
                    .matchedContent(alarmType)
                    .matchLocation("告警类型字段")
                    .confidence(0.95)
                    .build());
        }
        
        // 根据描述生成模式匹配
        if (alarm.getDescription() != null) {
            if (alarm.getDescription().contains("恶意")) {
                matches.add(AlarmFormattedContent.PatternMatch.builder()
                        .patternName("恶意行为模式")
                        .matchedContent("恶意")
                        .matchLocation("告警描述")
                        .confidence(0.85)
                        .build());
            }
        }
        
        return matches;
    }
    
    /**
     * 生成置信度分析
     * 
     * @param alarm 告警信息
     * @param context 格式化上下文
     * @return 置信度分析
     */
    private String generateConfidenceAnalysis(Alarm alarm, FormattingContext context) {
        Double confidence = alarm.getConfidence();
        if (confidence == null) {
            return "置信度信息不可用";
        }
        
        StringBuilder analysis = new StringBuilder();
        analysis.append(String.format("检测置信度为%.2f，", confidence));
        
        if (confidence >= 0.9) {
            analysis.append("属于高置信度检测，误报概率极低，建议立即采取行动。");
        } else if (confidence >= 0.7) {
            analysis.append("属于较高置信度检测，误报概率较低，建议优先处理。");
        } else if (confidence >= 0.5) {
            analysis.append("属于中等置信度检测，建议结合其他信息进行综合判断。");
        } else {
            analysis.append("属于低置信度检测，建议进一步验证后再采取行动。");
        }
        
        // 添加置信度影响因素分析
        analysis.append(" 置信度主要基于特征匹配度、行为异常程度、历史模式对比等因素综合计算得出。");
        
        return analysis.toString();
    }
    
    /**
     * 创建默认原因分析
     * 
     * @param alarm 告警信息
     * @return 默认原因分析
     */
    private AlarmFormattedContent.AlarmReasonAnalysis createDefaultReasonAnalysis(Alarm alarm) {
        List<AlarmFormattedContent.DetectionReason> defaultReasons = List.of(
                AlarmFormattedContent.DetectionReason.builder()
                        .reasonType("系统检测")
                        .description("系统检测到可疑活动")
                        .detectedFeature("异常行为")
                        .actualValue("检测到威胁")
                        .importance(AlarmFormattedContent.ImportanceLevel.MEDIUM)
                        .build()
        );
        
        return AlarmFormattedContent.AlarmReasonAnalysis.builder()
                .detectionReasons(defaultReasons)
                .technicalAnalysis("系统检测到异常活动，需要进一步分析")
                .behaviorAnalysis("检测到与正常模式不符的行为")
                .patternMatches(new ArrayList<>())
                .confidenceAnalysis("置信度信息不可用")
                .build();
    }
    
    /**
     * 初始化原因模板
     */
    private void initializeReasonTemplates() {
        // 恶意软件原因模板
        reasonTemplateCache.put("恶意软件", new ReasonTemplate("恶意软件", List.of(
                new ReasonTemplate.ReasonPattern("特征码匹配", "特征码", "检测到已知恶意软件特征码", 
                        AlarmFormattedContent.ImportanceLevel.HIGH),
                new ReasonTemplate.ReasonPattern("行为分析", "行为模式", "检测到恶意行为模式", 
                        AlarmFormattedContent.ImportanceLevel.HIGH),
                new ReasonTemplate.ReasonPattern("启发式检测", "启发式规则", "启发式规则检测到可疑活动", 
                        AlarmFormattedContent.ImportanceLevel.MEDIUM)
        )));
        
        // 网络扫描原因模板
        reasonTemplateCache.put("网络扫描", new ReasonTemplate("网络扫描", List.of(
                new ReasonTemplate.ReasonPattern("端口扫描", "端口访问模式", "检测到大量端口连接尝试", 
                        AlarmFormattedContent.ImportanceLevel.MEDIUM),
                new ReasonTemplate.ReasonPattern("服务探测", "服务响应模式", "检测到服务探测行为", 
                        AlarmFormattedContent.ImportanceLevel.MEDIUM),
                new ReasonTemplate.ReasonPattern("频率异常", "连接频率", "检测到异常高频连接", 
                        AlarmFormattedContent.ImportanceLevel.HIGH)
        )));
    }
    
    /**
     * 原因模板类
     */
    private static class ReasonTemplate implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private final String threatType;
        private final List<ReasonPattern> patterns;
        
        public ReasonTemplate(String threatType, List<ReasonPattern> patterns) {
            this.threatType = threatType;
            this.patterns = patterns;
        }
        
        public List<ReasonPattern> getPatterns() {
            return patterns;
        }
        
        /**
         * 原因模式类
         */
        private static class ReasonPattern implements Serializable {
            private static final long serialVersionUID = 1L;
            
            private final String reasonType;
            private final String featureName;
            private final String description;
            private final AlarmFormattedContent.ImportanceLevel importance;
            private final String expectedValue;
            
            public ReasonPattern(String reasonType, String featureName, String description, 
                               AlarmFormattedContent.ImportanceLevel importance) {
                this.reasonType = reasonType;
                this.featureName = featureName;
                this.description = description;
                this.importance = importance;
                this.expectedValue = "正常";
            }
            
            public boolean matches(Alarm alarm) {
                // 简化的匹配逻辑，实际应该更复杂
                return true;
            }
            
            public String generateDescription(Alarm alarm) {
                return description;
            }
            
            public String extractActualValue(Alarm alarm) {
                return "异常";
            }
            
            // Getters
            public String getReasonType() { return reasonType; }
            public String getFeatureName() { return featureName; }
            public String getExpectedValue() { return expectedValue; }
            public AlarmFormattedContent.ImportanceLevel getImportance() { return importance; }
        }
    }
}
