package com.geeksec.threatdetector.detection.detector.tunnel;

import com.geeksec.threatdetector.detection.DetectorType;
import com.geeksec.threatdetector.detection.ThreatDetector;
import com.geeksec.threatdetector.model.detection.DetectionResult;
import com.geeksec.threatdetector.model.input.*;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 隧道检测器
 * 检测各种网络隧道技术，包括HTTP隧道、SSL隧道、TCP隧道、DNS隧道等
 *
 * <AUTHOR>
 */
@Slf4j
public class TunnelDetector implements ThreatDetector {

    private static final long serialVersionUID = 1L;

    // 可疑端口列表
    private static final Set<Integer> SUSPICIOUS_PORTS = new HashSet<>(Arrays.asList(
            22, 23, 25, 53, 80, 443, 8080, 8443, 1080, 3128, 8888, 9999
    ));

    // DNS隧道检测阈值
    private static final int DNS_QUERY_LENGTH_THRESHOLD = 50;
    private static final double DNS_ENTROPY_THRESHOLD = 3.5;

    @Override
    public DetectorType getDetectorType() {
        return DetectorType.TUNNEL;
    }

    @Override
    public List<DetectionResult> detect(NetworkEvent event) {
        List<DetectionResult> results = new ArrayList<>();

        try {
            // 根据事件类型进行不同的隧道检测
            switch (event.getEventType()) {
                case HTTP:
                    DetectionResult httpResult = detectHttpTunnel(event);
                    if (httpResult != null) {
                        results.add(httpResult);
                    }
                    break;
                case SSL:
                    DetectionResult sslResult = detectSslTunnel(event);
                    if (sslResult != null) {
                        results.add(sslResult);
                    }
                    break;
                case TCP:
                    DetectionResult tcpResult = detectTcpTunnel(event);
                    if (tcpResult != null) {
                        results.add(tcpResult);
                    }
                    break;
                case DNS:
                    DetectionResult dnsResult = detectDnsTunnel(event);
                    if (dnsResult != null) {
                        results.add(dnsResult);
                    }
                    break;
                default:
                    // 其他类型暂不处理
                    break;
            }

        } catch (Exception e) {
            log.error("隧道检测异常: {}", e.getMessage(), e);
        }

        return results;
    }

    /**
     * 检测HTTP隧道
     */
    private DetectionResult detectHttpTunnel(NetworkEvent event) {
        HttpInfo httpInfo = event.getHttpInfo();
        if (httpInfo == null) {
            return null;
        }

        // 1. 检查异常的HTTP方法
        String method = httpInfo.getMethod();
        if (method != null && !method.matches("GET|POST|PUT|DELETE|HEAD|OPTIONS|TRACE|CONNECT")) {
            return createDetectionResult(event, "HTTP_TUNNEL_METHOD", 
                    "HTTP隧道（异常方法）",
                    DetectionResult.ThreatLevel.MEDIUM, 0.7,
                    "异常HTTP方法: " + method);
        }

        // 2. 检查异常的URI长度
        String uri = httpInfo.getUri();
        if (uri != null && uri.length() > 2000) {
            return createDetectionResult(event, "HTTP_TUNNEL_LONG_URI", 
                    "HTTP隧道（超长URI）",
                    DetectionResult.ThreatLevel.MEDIUM, 0.6,
                    "URI长度: " + uri.length());
        }

        // 3. 检查异常的User-Agent
        String userAgent = httpInfo.getUserAgent();
        if (userAgent != null && userAgent.length() > 500) {
            return createDetectionResult(event, "HTTP_TUNNEL_LONG_UA", 
                    "HTTP隧道（超长User-Agent）",
                    DetectionResult.ThreatLevel.LOW, 0.5,
                    "User-Agent长度: " + userAgent.length());
        }

        // 4. 检查请求体中的异常模式
        String requestBody = httpInfo.getRequestBody();
        if (requestBody != null) {
            if (containsRepeatedPatterns(requestBody)) {
                return createDetectionResult(event, "HTTP_TUNNEL_REPEATED_PATTERN", 
                        "HTTP隧道（重复模式）",
                        DetectionResult.ThreatLevel.MEDIUM, 0.6,
                        "检测到重复模式");
            }

            if (containsAbnormalEncodedData(requestBody)) {
                return createDetectionResult(event, "HTTP_TUNNEL_ENCODED_DATA", 
                        "HTTP隧道（异常编码）",
                        DetectionResult.ThreatLevel.MEDIUM, 0.7,
                        "检测到异常编码数据");
            }
        }

        return null;
    }

    /**
     * 检测SSL隧道
     */
    private DetectionResult detectSslTunnel(NetworkEvent event) {
        SslInfo sslInfo = event.getSslInfo();
        if (sslInfo == null) {
            return null;
        }

        // 1. 检查自签名证书
        if (Boolean.TRUE.equals(sslInfo.getSelfSigned())) {
            return createDetectionResult(event, "SSL_TUNNEL_SELF_SIGNED", 
                    "SSL隧道（自签名证书）",
                    DetectionResult.ThreatLevel.MEDIUM, 0.6,
                    "使用自签名证书");
        }

        // 2. 检查过期证书
        if (Boolean.TRUE.equals(sslInfo.getCertExpired())) {
            return createDetectionResult(event, "SSL_TUNNEL_EXPIRED_CERT", 
                    "SSL隧道（过期证书）",
                    DetectionResult.ThreatLevel.LOW, 0.4,
                    "使用过期证书");
        }

        // 3. 检查异常的服务器名称
        String serverName = sslInfo.getServerName();
        if (serverName != null && (serverName.matches(".*\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}.*") || 
                                   serverName.length() > 100)) {
            return createDetectionResult(event, "SSL_TUNNEL_SUSPICIOUS_SNI", 
                    "SSL隧道（可疑SNI）",
                    DetectionResult.ThreatLevel.MEDIUM, 0.6,
                    "可疑服务器名称: " + serverName);
        }

        return null;
    }

    /**
     * 检测TCP隧道
     */
    private DetectionResult detectTcpTunnel(NetworkEvent event) {
        TcpInfo tcpInfo = event.getTcpInfo();
        if (tcpInfo == null) {
            return null;
        }

        // 1. 检查可疑端口
        Integer dstPort = event.getDstPort();
        if (dstPort != null && SUSPICIOUS_PORTS.contains(dstPort)) {
            
            // 2. 检查连接持续时间
            Long duration = tcpInfo.getConnectionDuration();
            if (duration != null && duration > 3600000) { // 1小时
                return createDetectionResult(event, "TCP_TUNNEL_LONG_CONNECTION", 
                        "TCP隧道（长时间连接）",
                        DetectionResult.ThreatLevel.MEDIUM, 0.7,
                        String.format("端口: %d, 持续时间: %d毫秒", dstPort, duration));
            }

            // 3. 检查负载特征
            if (Boolean.TRUE.equals(tcpInfo.getHasPayload()) && tcpInfo.getPayloadLength() != null) {
                byte[] payload = tcpInfo.getPayload();
                if (payload != null && payload.length > 0) {
                    if (containsAbnormalPattern(payload)) {
                        return createDetectionResult(event, "TCP_TUNNEL_ABNORMAL_PAYLOAD", 
                                "TCP隧道（异常负载）",
                                DetectionResult.ThreatLevel.MEDIUM, 0.6,
                                "检测到异常负载模式");
                    }

                    if (isLikelyEncrypted(payload)) {
                        return createDetectionResult(event, "TCP_TUNNEL_ENCRYPTED_PAYLOAD", 
                                "TCP隧道（加密负载）",
                                DetectionResult.ThreatLevel.LOW, 0.5,
                                "检测到可能的加密负载");
                    }
                }
            }
        }

        return null;
    }

    /**
     * 检测DNS隧道
     */
    private DetectionResult detectDnsTunnel(NetworkEvent event) {
        DnsInfo dnsInfo = event.getDnsInfo();
        if (dnsInfo == null) {
            return null;
        }

        String queryName = dnsInfo.getQueryName();
        if (queryName == null) {
            return null;
        }

        // 1. 检查查询名称长度
        if (queryName.length() > DNS_QUERY_LENGTH_THRESHOLD) {
            return createDetectionResult(event, "DNS_TUNNEL_LONG_QUERY", 
                    "DNS隧道（超长查询）",
                    DetectionResult.ThreatLevel.MEDIUM, 0.7,
                    "查询长度: " + queryName.length());
        }

        // 2. 检查查询名称熵值
        double entropy = calculateEntropy(queryName);
        if (entropy > DNS_ENTROPY_THRESHOLD) {
            return createDetectionResult(event, "DNS_TUNNEL_HIGH_ENTROPY", 
                    "DNS隧道（高熵值）",
                    DetectionResult.ThreatLevel.MEDIUM, 0.6,
                    String.format("熵值: %.2f", entropy));
        }

        // 3. 检查异常的查询类型
        String queryType = dnsInfo.getQueryType();
        if (queryType != null && (queryType.equals("TXT") || queryType.equals("NULL"))) {
            return createDetectionResult(event, "DNS_TUNNEL_SUSPICIOUS_TYPE", 
                    "DNS隧道（可疑查询类型）",
                    DetectionResult.ThreatLevel.LOW, 0.5,
                    "查询类型: " + queryType);
        }

        // 4. 检查Base64编码特征
        if (queryName.matches(".*[A-Za-z0-9+/=]{20,}.*")) {
            return createDetectionResult(event, "DNS_TUNNEL_BASE64", 
                    "DNS隧道（Base64编码）",
                    DetectionResult.ThreatLevel.MEDIUM, 0.7,
                    "检测到Base64编码特征");
        }

        return null;
    }

    /**
     * 检查是否包含重复模式
     */
    private boolean containsRepeatedPatterns(String data) {
        if (data == null || data.length() < 10) {
            return false;
        }

        // 简单的重复模式检测
        for (int i = 1; i <= data.length() / 2; i++) {
            String pattern = data.substring(0, i);
            if (data.replace(pattern, "").length() < data.length() * 0.3) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否包含异常编码数据
     */
    private boolean containsAbnormalEncodedData(String data) {
        if (data == null) {
            return false;
        }

        // 检查Base64编码
        if (data.matches(".*[A-Za-z0-9+/=]{50,}.*")) {
            return true;
        }

        // 检查十六进制编码
        if (data.matches(".*[0-9a-fA-F]{100,}.*")) {
            return true;
        }

        return false;
    }

    /**
     * 检查字节数组是否包含异常模式
     */
    private boolean containsAbnormalPattern(byte[] payload) {
        if (payload == null || payload.length < 10) {
            return false;
        }

        // 检查重复字节
        Map<Byte, Integer> byteCount = new HashMap<>();
        for (byte b : payload) {
            byteCount.put(b, byteCount.getOrDefault(b, 0) + 1);
        }

        // 如果某个字节出现频率超过80%，可能是异常模式
        for (int count : byteCount.values()) {
            if (count > payload.length * 0.8) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否可能是加密数据
     */
    private boolean isLikelyEncrypted(byte[] payload) {
        if (payload == null || payload.length < 20) {
            return false;
        }

        // 计算字节熵值
        double entropy = calculateByteEntropy(payload);
        return entropy > 7.0; // 高熵值可能表示加密数据
    }

    /**
     * 计算字符串熵值
     */
    private double calculateEntropy(String data) {
        if (data == null || data.isEmpty()) {
            return 0.0;
        }

        Map<Character, Integer> charCount = new HashMap<>();
        for (char c : data.toCharArray()) {
            charCount.put(c, charCount.getOrDefault(c, 0) + 1);
        }

        double entropy = 0.0;
        int length = data.length();
        for (int count : charCount.values()) {
            double probability = (double) count / length;
            entropy -= probability * (Math.log(probability) / Math.log(2));
        }

        return entropy;
    }

    /**
     * 计算字节数组熵值
     */
    private double calculateByteEntropy(byte[] data) {
        if (data == null || data.length == 0) {
            return 0.0;
        }

        Map<Byte, Integer> byteCount = new HashMap<>();
        for (byte b : data) {
            byteCount.put(b, byteCount.getOrDefault(b, 0) + 1);
        }

        double entropy = 0.0;
        int length = data.length;
        for (int count : byteCount.values()) {
            double probability = (double) count / length;
            entropy -= probability * (Math.log(probability) / Math.log(2));
        }

        return entropy;
    }

    /**
     * 创建检测结果
     */
    private DetectionResult createDetectionResult(NetworkEvent event, String threatType, 
                                                String threatName, DetectionResult.ThreatLevel level, 
                                                double confidence, String description) {
        return DetectionResult.builder()
                .detectorType(getDetectorType())
                .threatType(threatType)
                .threatName(threatName)
                .threatLevel(level)
                .confidence(confidence)
                .description(description)
                .sessionId(event.getSessionId())
                .srcIp(event.getSrcIp())
                .dstIp(event.getDstIp())
                .srcPort(event.getSrcPort())
                .dstPort(event.getDstPort())
                .protocol(event.getProtocol())
                .detectionTime(LocalDateTime.now())
                .sessionLabel("TUNNEL_COMMUNICATION")
                .assetLabel("TUNNEL_ENDPOINT")
                .labelValue(threatName)
                .build();
    }

    @Override
    public int getPriority() {
        return 30; // 中等优先级
    }
}
